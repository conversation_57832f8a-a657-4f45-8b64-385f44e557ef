<?php
/**
 * 更新PHPMailer路径引用
 * 将所有文件中的PHPMailer路径更新为本项目内的路径
 */

header('Content-Type: text/html; charset=utf-8');

$updateResult = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_paths'])) {
    $updateResult = updatePHPMailerPaths();
}

function updatePHPMailerPaths() {
    $result = [
        'success' => false,
        'updated_files' => [],
        'errors' => [],
        'changes' => []
    ];
    
    try {
        // 需要更新的文件列表
        $filesToUpdate = [
            '../server/working-email-sender.php' => [
                'old' => '../../test_email_system/vendor/autoload.php',
                'new' => '../vendor/autoload.php'
            ],
            '../server/phpmailer-email-sender.php' => [
                'old' => '../../test_email_system/vendor/autoload.php',
                'new' => '../vendor/autoload.php'
            ]
        ];
        
        foreach ($filesToUpdate as $filePath => $replacement) {
            if (file_exists($filePath)) {
                $content = file_get_contents($filePath);
                $originalContent = $content;
                
                // 替换路径
                $content = str_replace($replacement['old'], $replacement['new'], $content);
                
                // 如果内容有变化，写回文件
                if ($content !== $originalContent) {
                    if (file_put_contents($filePath, $content)) {
                        $result['updated_files'][] = $filePath;
                        $result['changes'][] = [
                            'file' => $filePath,
                            'old' => $replacement['old'],
                            'new' => $replacement['new']
                        ];
                    } else {
                        $result['errors'][] = "无法写入文件: $filePath";
                    }
                } else {
                    $result['changes'][] = [
                        'file' => $filePath,
                        'status' => '无需更改'
                    ];
                }
            } else {
                $result['errors'][] = "文件不存在: $filePath";
            }
        }
        
        $result['success'] = empty($result['errors']);
        
    } catch (Exception $e) {
        $result['errors'][] = "异常: " . $e->getMessage();
    }
    
    return $result;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>更新PHPMailer路径引用</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
        button { padding: 8px 16px; margin: 5px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
        .highlight { background: #ffffcc; padding: 15px; border-left: 4px solid #ffeb3b; margin: 15px 0; }
        .change-item { background: #f9f9f9; padding: 10px; margin: 5px 0; border-left: 3px solid #2196F3; }
    </style>
</head>
<body>
    <h1>🔄 更新PHPMailer路径引用</h1>
    <p><strong>操作时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="highlight">
        <h3>🎯 操作说明</h3>
        <p><strong>目的:</strong> 将所有文件中的PHPMailer路径从外部引用更新为本项目内的路径</p>
        <p><strong>路径变更:</strong></p>
        <ul>
            <li><strong>旧路径:</strong> <code>../../test_email_system/vendor/autoload.php</code></li>
            <li><strong>新路径:</strong> <code>../vendor/autoload.php</code></li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>📊 当前vendor目录状态</h2>
        <?php
        $vendorDir = '../vendor';
        $vendorExists = is_dir($vendorDir);
        $autoloadExists = file_exists($vendorDir . '/autoload.php');
        $phpmailerExists = is_dir($vendorDir . '/phpmailer');
        
        echo "<p><strong>vendor目录:</strong> " . ($vendorExists ? '<span class="success">存在</span>' : '<span class="error">不存在</span>') . "</p>";
        echo "<p><strong>autoload.php:</strong> " . ($autoloadExists ? '<span class="success">存在</span>' : '<span class="error">不存在</span>') . "</p>";
        echo "<p><strong>phpmailer目录:</strong> " . ($phpmailerExists ? '<span class="success">存在</span>' : '<span class="error">不存在</span>') . "</p>";
        
        if (!$vendorExists || !$autoloadExists) {
            echo "<div class='warning'>";
            echo "<p>⚠️ vendor目录或autoload.php不存在，请先运行 <a href='copy-phpmailer.php'>复制PHPMailer</a> 操作</p>";
            echo "</div>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>📝 需要更新的文件</h2>
        <?php
        $filesToCheck = [
            '../server/working-email-sender.php',
            '../server/phpmailer-email-sender.php'
        ];
        
        foreach ($filesToCheck as $file) {
            $exists = file_exists($file);
            echo "<p><strong>$file:</strong> " . ($exists ? '<span class="success">存在</span>' : '<span class="error">不存在</span>') . "</p>";
            
            if ($exists) {
                $content = file_get_contents($file);
                $hasOldPath = strpos($content, '../../test_email_system/vendor/autoload.php') !== false;
                $hasNewPath = strpos($content, '../vendor/autoload.php') !== false;
                
                echo "<p style='margin-left: 20px;'>";
                echo "旧路径: " . ($hasOldPath ? '<span class="warning">存在</span>' : '<span class="success">不存在</span>') . " | ";
                echo "新路径: " . ($hasNewPath ? '<span class="success">存在</span>' : '<span class="info">不存在</span>');
                echo "</p>";
            }
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>🔧 执行路径更新</h2>
        <?php if (empty($updateResult)): ?>
            <form method="POST">
                <p>点击下面的按钮更新所有文件中的PHPMailer路径引用:</p>
                <button type="submit" name="update_paths">更新路径引用</button>
            </form>
        <?php else: ?>
            <h3>更新结果:</h3>
            
            <?php if ($updateResult['success']): ?>
                <div class="success">
                    <h4>✅ 路径更新成功!</h4>
                    <p><strong>更新的文件:</strong> <?php echo count($updateResult['updated_files']); ?> 个</p>
                </div>
            <?php else: ?>
                <div class="error">
                    <h4>❌ 更新过程中出现错误</h4>
                    <p><strong>错误数量:</strong> <?php echo count($updateResult['errors']); ?> 个</p>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($updateResult['changes'])): ?>
                <h4>变更详情:</h4>
                <?php foreach ($updateResult['changes'] as $change): ?>
                    <div class="change-item">
                        <p><strong>文件:</strong> <?php echo htmlspecialchars($change['file']); ?></p>
                        <?php if (isset($change['status'])): ?>
                            <p><strong>状态:</strong> <?php echo $change['status']; ?></p>
                        <?php else: ?>
                            <p><strong>旧路径:</strong> <code><?php echo htmlspecialchars($change['old']); ?></code></p>
                            <p><strong>新路径:</strong> <code><?php echo htmlspecialchars($change['new']); ?></code></p>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
            
            <?php if (!empty($updateResult['errors'])): ?>
                <h4>错误信息:</h4>
                <pre class="error"><?php echo implode("\n", $updateResult['errors']); ?></pre>
            <?php endif; ?>
            
            <form method="POST">
                <button type="submit" name="update_paths">重新更新</button>
            </form>
        <?php endif; ?>
    </div>
    
    <?php if (!empty($updateResult) && $updateResult['success']): ?>
    <div class="test-section">
        <h2>✅ 下一步操作</h2>
        <p>路径更新成功！现在可以:</p>
        <ol>
            <li><strong>测试PHPMailer加载:</strong> 验证新路径是否正常工作</li>
            <li><strong>测试邮件发送:</strong> 确认邮件功能正常</li>
            <li><strong>测试密码找回:</strong> 完整测试密码找回流程</li>
        </ol>
        
        <p>
            <a href="test-local-phpmailer.php"><button>测试本地PHPMailer</button></a>
            <a href="test-working-email-sender.php"><button>测试邮件发送器</button></a>
        </p>
    </div>
    <?php endif; ?>
    
    <div class="test-section">
        <h2>🔗 相关链接</h2>
        <ul>
            <li><a href="copy-phpmailer.php">复制PHPMailer文件</a></li>
            <li><a href="test-working-email-sender.php">测试工作的邮件发送器</a></li>
            <li><a href="verify-phpmailer-path.php">验证PHPMailer路径</a></li>
            <li><a href="../new-lock.html">返回登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
