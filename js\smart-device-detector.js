/**
 * 智能设备检测和跳转系统
 * 自动识别Android TV环境并跳转到相应的优化版本
 */

class SmartDeviceDetector {
    constructor() {
        this.deviceInfo = this.detectDevice();
        this.currentPage = this.getCurrentPage();
        this.init();
    }
    
    /**
     * 检测设备类型和环境
     */
    detectDevice() {
        const userAgent = navigator.userAgent.toLowerCase();
        const platform = navigator.platform?.toLowerCase() || '';
        
        // Android TV检测
        const isAndroidTV = this.isAndroidTV(userAgent);
        
        // 其他设备类型检测
        const isMobile = /android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
        const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent);
        const isDesktop = !isMobile && !isTablet;
        
        // 性能评估
        const performanceLevel = this.assessPerformance();
        
        return {
            isAndroidTV: isAndroidTV,
            isMobile: isMobile,
            isTablet: isTablet,
            isDesktop: isDesktop,
            userAgent: userAgent,
            platform: platform,
            performanceLevel: performanceLevel,
            screen: {
                width: screen.width,
                height: screen.height,
                pixelRatio: window.devicePixelRatio || 1
            },
            memory: navigator.deviceMemory || 'unknown',
            cores: navigator.hardwareConcurrency || 'unknown'
        };
    }
    
    /**
     * 检测是否为Android TV
     */
    isAndroidTV(userAgent) {
        // Android TV特征检测
        const androidTVPatterns = [
            /android.*tv/i,
            /smarttv/i,
            /googletv/i,
            /android.*; tv\)/i,
            /android.*; aft/i,  // Amazon Fire TV
            /android.*; mibox/i, // Mi Box
            /android.*; shield/i, // NVIDIA Shield
            /android.*; chromecast/i, // Chromecast
            /android.*; androidtv/i
        ];
        
        // 检查User Agent
        const hasAndroidTVUA = androidTVPatterns.some(pattern => pattern.test(userAgent));
        
        // 检查屏幕特征 (TV通常是横屏且分辨率较高)
        const isLandscape = screen.width > screen.height;
        const isLargeScreen = screen.width >= 1280 || screen.height >= 720;
        
        // 检查是否支持TV相关API
        const hasTVFeatures = 'ontouchstart' in window === false && // 通常没有触摸
                             navigator.maxTouchPoints === 0;
        
        return hasAndroidTVUA || (
            userAgent.includes('android') && 
            isLandscape && 
            isLargeScreen && 
            hasTVFeatures
        );
    }
    
    /**
     * 评估设备性能
     */
    assessPerformance() {
        let score = 0;
        
        // 基于内存评分
        if (this.deviceInfo?.memory >= 4) score += 3;
        else if (this.deviceInfo?.memory >= 2) score += 2;
        else if (this.deviceInfo?.memory >= 1) score += 1;
        
        // 基于CPU核心数评分
        if (this.deviceInfo?.cores >= 8) score += 3;
        else if (this.deviceInfo?.cores >= 4) score += 2;
        else if (this.deviceInfo?.cores >= 2) score += 1;
        
        // 基于屏幕分辨率评分
        const totalPixels = screen.width * screen.height;
        if (totalPixels >= 3840 * 2160) score += 1; // 4K
        else if (totalPixels >= 1920 * 1080) score += 2; // 1080p
        else score += 3; // 720p或更低
        
        if (score >= 7) return 'high';
        else if (score >= 4) return 'medium';
        else return 'low';
    }
    
    /**
     * 获取当前页面类型
     */
    getCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop();
        
        if (filename.includes('index-tv-optimized')) return 'tv-optimized';
        else if (filename.includes('index')) return 'standard';
        else return 'other';
    }
    
    /**
     * 初始化设备检测和跳转逻辑
     */
    init() {
        console.log('🔍 智能设备检测启动');
        console.log('📱 设备信息:', this.deviceInfo);

        // 检查是否是页面刷新（通过 performance.navigation 或其他方式）
        this.handlePageRefresh();

        // 只有在应该进行自动检测时才检查跳转
        if (this.shouldPerformAutoDetection()) {
            console.log('✅ 开始进行设备检测和跳转检查');
            this.checkAndRedirect();
        } else {
            console.log('⏭️ 跳过自动检测（用户已手动选择或已完成检测）');
        }

        // 设置性能配置
        this.applyPerformanceConfig();

        // 记录设备信息到全局
        window.deviceInfo = this.deviceInfo;
    }
    
    /**
     * 处理页面刷新逻辑
     */
    handlePageRefresh() {
        // 检查是否是页面刷新
        const isPageRefresh = this.isPageRefresh();

        if (isPageRefresh) {
            console.log('🔄 检测到页面刷新，清除手动切换标记，重新进行设备检测');
            // 清除手动切换相关的标记，但保留其他会话数据
            sessionStorage.removeItem('manual_version_switch');
            sessionStorage.removeItem('device_redirect_done');

            // 注意：不清除 login_redirect_done 等其他重要的会话标记
        }
    }

    /**
     * 检测是否是页面刷新
     */
    isPageRefresh() {
        // 方法1：使用 performance.navigation（已废弃但仍可用）
        if (performance.navigation && performance.navigation.type === 1) {
            return true;
        }

        // 方法2：使用 performance.getEntriesByType
        const navigationEntries = performance.getEntriesByType('navigation');
        if (navigationEntries.length > 0) {
            const navEntry = navigationEntries[0];
            return navEntry.type === 'reload';
        }

        // 方法3：检查 document.referrer 是否与当前页面相同
        if (document.referrer === window.location.href) {
            return true;
        }

        return false;
    }

    /**
     * 检查并执行跳转
     */
    checkAndRedirect() {
        // 检查是否是手动切换 - 手动切换优先级最高
        const manualSwitch = sessionStorage.getItem('manual_version_switch') === 'true';

        if (manualSwitch) {
            console.log('🔧 用户已手动切换版本，完全跳过所有自动检测和跳转');
            // 手动切换后，完全停止任何自动行为
            return;
        }

        // 检查是否已经完成过设备检测
        if (sessionStorage.getItem('device_redirect_done') === 'true') {
            console.log('🔄 已完成设备重定向，跳过检查');
            return;
        }

        // 只有在没有手动切换且没有完成过检测的情况下，才进行自动检测
        const shouldUseTV = this.shouldUseTVVersion();
        const currentIsTV = this.currentPage === 'tv-optimized';

        if (shouldUseTV && !currentIsTV) {
            console.log('📺 首次检测到Android TV环境，跳转到优化版本');
            this.redirectToTVVersion();
        } else if (!shouldUseTV && currentIsTV) {
            console.log('💻 首次检测到非TV环境，跳转到标准版本');
            this.redirectToStandardVersion();
        } else {
            console.log('✅ 当前页面适合当前设备环境');
            sessionStorage.setItem('device_redirect_done', 'true');
        }
    }
    
    /**
     * 判断是否应该使用TV版本
     */
    shouldUseTVVersion() {
        // 在测试环境中禁用自动检测
        if (window.location.pathname.includes('/test/')) {
            return false;
        }

        // 只有真正的Android TV设备才自动使用TV版本
        if (this.deviceInfo.isAndroidTV) return true;

        // 移除低性能设备的自动TV版本逻辑，避免桌面用户被强制切换
        // 桌面用户应该可以自由选择版本，而不是被性能检测强制切换

        return false;
    }
    
    /**
     * 跳转到TV优化版本
     */
    redirectToTVVersion() {
        // 保留当前URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const targetUrl = 'index-tv-optimized.php' + (urlParams.toString() ? '?' + urlParams.toString() : '');
        
        // 设置重定向标记
        sessionStorage.setItem('device_redirect_done', 'true');
        sessionStorage.setItem('redirect_reason', 'android_tv_detected');
        
        // 显示跳转提示
        this.showRedirectMessage('正在为您切换到Android TV优化版本...', () => {
            window.location.href = targetUrl;
        });
    }
    
    /**
     * 跳转到标准版本
     */
    redirectToStandardVersion() {
        // 保留当前URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const targetUrl = 'index.php' + (urlParams.toString() ? '?' + urlParams.toString() : '');
        
        // 设置重定向标记
        sessionStorage.setItem('device_redirect_done', 'true');
        sessionStorage.setItem('redirect_reason', 'standard_device_detected');
        
        // 显示跳转提示
        this.showRedirectMessage('正在为您切换到标准版本...', () => {
            window.location.href = targetUrl;
        });
    }
    
    /**
     * 显示跳转提示消息
     */
    showRedirectMessage(message, callback) {
        // 创建提示层
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        `;
        
        const messageBox = document.createElement('div');
        messageBox.style.cssText = `
            background: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        `;
        
        messageBox.innerHTML = `
            <div style="font-size: 18px; margin-bottom: 20px; color: #333;">
                🔄 ${message}
            </div>
            <div style="font-size: 14px; color: #666;">
                请稍候...
            </div>
        `;
        
        overlay.appendChild(messageBox);
        document.body.appendChild(overlay);
        
        // 2秒后执行跳转
        setTimeout(() => {
            callback();
        }, 2000);
    }
    
    /**
     * 应用性能配置
     */
    applyPerformanceConfig() {
        // 如果是TV环境且在标准页面，应用临时优化
        if (this.deviceInfo.isAndroidTV && this.currentPage === 'standard') {
            console.log('📺 在标准页面应用TV临时优化');
            this.applyTempTVOptimizations();
        }
        
        // 设置全局性能配置
        window.performanceConfig = {
            deviceType: this.getDeviceType(),
            performanceLevel: this.deviceInfo.performanceLevel,
            isAndroidTV: this.deviceInfo.isAndroidTV,
            recommendedUpdateInterval: this.getRecommendedUpdateInterval()
        };
    }
    
    /**
     * 获取设备类型
     */
    getDeviceType() {
        if (this.deviceInfo.isAndroidTV) return 'android-tv';
        if (this.deviceInfo.isMobile) return 'mobile';
        if (this.deviceInfo.isTablet) return 'tablet';
        return 'desktop';
    }
    
    /**
     * 获取推荐的更新间隔
     */
    getRecommendedUpdateInterval() {
        if (this.deviceInfo.isAndroidTV) {
            switch (this.deviceInfo.performanceLevel) {
                case 'high': return 33; // 30fps
                case 'medium': return 50; // 20fps
                case 'low': return 100; // 10fps
                default: return 50;
            }
        }
        return 10; // 标准设备保持高频更新
    }
    
    /**
     * 应用临时TV优化
     */
    applyTempTVOptimizations() {
        const style = document.createElement('style');
        style.textContent = `
            /* 临时TV优化样式 */
            *, *::before, *::after {
                animation-duration: 0.5s !important;
            }
            .countdown-number {
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7) !important;
            }
        `;
        document.head.appendChild(style);
    }
    
    /**
     * 获取设备信息摘要
     */
    getDeviceSummary() {
        return {
            type: this.getDeviceType(),
            isAndroidTV: this.deviceInfo.isAndroidTV,
            performance: this.deviceInfo.performanceLevel,
            screen: `${this.deviceInfo.screen.width}x${this.deviceInfo.screen.height}`,
            memory: this.deviceInfo.memory,
            cores: this.deviceInfo.cores
        };
    }

    /**
     * 手动切换到TV版本
     */
    manualSwitchToTV() {
        console.log('🔧 用户手动切换到TV版本');

        // 立即设置手动切换标记，阻止任何自动检测
        sessionStorage.setItem('manual_version_switch', 'true');
        sessionStorage.setItem('device_redirect_done', 'true');

        // 检查是否在测试环境
        const currentPath = window.location.pathname;
        let targetUrl;

        if (currentPath.includes('/test/')) {
            // 测试环境
            targetUrl = 'test-version-switch-tv.html';
        } else {
            // 生产环境
            targetUrl = 'index-tv-optimized.php';
        }

        console.log('📝 手动切换标记已设置，直接跳转到:', targetUrl);

        // 直接跳转，不使用任何检测逻辑
        this.showRedirectMessage('正在为您切换到Android TV优化版本...', () => {
            window.location.href = targetUrl;
        });
    }

    /**
     * 手动切换到标准版本
     */
    manualSwitchToStandard() {
        console.log('🔧 用户手动切换到标准版本');

        // 立即设置手动切换标记，阻止任何自动检测
        sessionStorage.setItem('manual_version_switch', 'true');
        sessionStorage.setItem('device_redirect_done', 'true');

        // 检查是否在测试环境
        const currentPath = window.location.pathname;
        let targetUrl;

        if (currentPath.includes('/test/')) {
            // 测试环境
            targetUrl = 'test-version-switch.html';
        } else {
            // 生产环境
            targetUrl = 'index.php';
        }

        console.log('📝 手动切换标记已设置，直接跳转到:', targetUrl);

        // 直接跳转，不使用任何检测逻辑
        this.showRedirectMessage('正在为您切换到标准版本...', () => {
            window.location.href = targetUrl;
        });
    }

    /**
     * 重置自动检测（清除手动切换标记）
     * 注意：这个方法应该只在用户明确要求重置时调用
     * 比如：页面刷新、重新登录等场景
     */
    resetAutoDetection() {
        console.log('🔄 用户主动重置自动检测');
        sessionStorage.removeItem('manual_version_switch');
        sessionStorage.removeItem('preferred_version');
        sessionStorage.removeItem('device_redirect_done');

        // 重新检测
        this.checkAndRedirect();
    }

    /**
     * 检查是否应该进行自动检测
     * 只有在以下情况下才进行自动检测：
     * 1. 没有手动切换标记
     * 2. 没有完成过设备检测
     * 3. 是页面首次加载或刷新
     */
    shouldPerformAutoDetection() {
        const manualSwitch = sessionStorage.getItem('manual_version_switch') === 'true';
        const redirectDone = sessionStorage.getItem('device_redirect_done') === 'true';

        if (manualSwitch) {
            console.log('🚫 用户已手动切换版本，跳过自动检测');
            return false;
        }

        if (redirectDone) {
            console.log('🚫 已完成设备检测，跳过重复检测');
            return false;
        }

        return true;
    }
}

// 自动初始化设备检测
if (typeof window !== 'undefined') {
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.smartDeviceDetector = new SmartDeviceDetector();
        });
    } else {
        window.smartDeviceDetector = new SmartDeviceDetector();
    }
}

// 导出类
window.SmartDeviceDetector = SmartDeviceDetector;
