<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码找回功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .info {
            color: #0c5460;
            background: #d1ecf1;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .test-section {
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin: 20px 0;
        }
        
        .code-display {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 14px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <h1>🔑 密码找回功能测试</h1>
    
    <div class="container">
        <h2>系统更新内容</h2>
        <div class="info">
            <strong>✅ 已完成的更新:</strong><br>
            1. 输入框添加了明显的线框边框<br>
            2. 注册界面去除了QQ输入框<br>
            3. 邮箱字段改为必填项<br>
            4. 数据库去除了qq_number字段<br>
            5. 添加了密码找回功能<br>
            6. 新增了password_resets表<br>
            7. 支持多个账户使用同一邮箱
        </div>
    </div>
    
    <div class="container">
        <h2>🧪 功能测试</h2>
        
        <div class="test-section">
            <h3>1. 测试用户注册（邮箱必填）</h3>
            <button onclick="testRegister()">测试注册功能</button>
            <div id="registerResult"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 测试密码找回</h3>
            <button onclick="testForgotPassword()">测试密码找回</button>
            <div id="forgotResult"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 测试密码重置</h3>
            <button onclick="testResetPassword()">测试密码重置</button>
            <div id="resetResult"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 检查数据库结构</h3>
            <button onclick="checkDatabase()">检查数据库</button>
            <div id="dbResult"></div>
        </div>
    </div>
    
    <div class="container">
        <h2>📊 测试数据</h2>
        <div class="info">
            <strong>测试用户:</strong> testuser / test123<br>
            <strong>测试邮箱:</strong> <EMAIL><br>
            <strong>示例激活码:</strong> 43DJ3-2UYFL-JJXAA-6BCM6<br>
            <strong>数据库文件:</strong> server/user_system.db3
        </div>
    </div>
    
    <div class="container">
        <h2>🔗 快速链接</h2>
        <button onclick="window.location.href='../new-lock.html'">新登录页面</button>
        <button onclick="window.location.href='../reset-password.html?token=test'">密码重置页面</button>
        <button onclick="window.location.href='../index.php'">主页面</button>
    </div>

    <script>
        async function testRegister() {
            const resultDiv = document.getElementById('registerResult');
            resultDiv.innerHTML = '<div class="info">测试用户注册...</div>';
            
            try {
                const testUser = {
                    username: 'testuser' + Date.now(),
                    password: 'test123456',
                    email: 'test' + Date.now() + '@example.com',
                    captcha: 'TEST' // 需要真实验证码
                };
                
                const response = await fetch('../api/register.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testUser)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ 注册测试成功（需要真实验证码才能完成）</div>
                        <div class="code-display">测试用户: ${testUser.username}</div>
                        <div class="code-display">测试邮箱: ${testUser.email}</div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ 注册失败: ${data.message}</div>
                        <div class="info">这是预期的，因为需要真实验证码</div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }
        
        async function testForgotPassword() {
            const resultDiv = document.getElementById('forgotResult');
            resultDiv.innerHTML = '<div class="info">测试密码找回...</div>';
            
            try {
                const forgotData = {
                    email: '<EMAIL>',
                    captcha: 'TEST' // 需要真实验证码
                };
                
                const response = await fetch('../api/forgot-password.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(forgotData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ 密码找回测试成功</div>
                        <div class="info">消息: ${data.message}</div>
                        ${data.debug_info ? `
                            <div class="code-display">
                                调试信息:<br>
                                邮箱: ${data.debug_info.email}<br>
                                令牌: ${data.debug_info.token}<br>
                                重置链接: ${data.debug_info.reset_link}
                            </div>
                        ` : ''}
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ 密码找回失败: ${data.message}</div>
                        <div class="info">这是预期的，因为需要真实验证码</div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }
        
        async function testResetPassword() {
            const resultDiv = document.getElementById('resetResult');
            resultDiv.innerHTML = '<div class="info">测试密码重置...</div>';
            
            try {
                const resetData = {
                    token: 'invalid_token_for_test',
                    new_password: 'newpassword123'
                };
                
                const response = await fetch('../api/reset-password.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(resetData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ 密码重置成功</div>
                        <div class="info">消息: ${data.message}</div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ 密码重置失败: ${data.message}</div>
                        <div class="info">这是预期的，因为使用了无效令牌</div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }
        
        function checkDatabase() {
            const resultDiv = document.getElementById('dbResult');
            resultDiv.innerHTML = `
                <div class="info">
                    📊 数据库结构更新:<br><br>
                    <strong>users表变更:</strong><br>
                    - ❌ 去除了 qq_number 字段<br>
                    - ✅ email 字段改为 NOT NULL（必填）<br><br>
                    
                    <strong>新增表:</strong><br>
                    - ✅ password_resets 表（密码重置令牌）<br><br>
                    
                    <strong>新增索引:</strong><br>
                    - ✅ idx_users_email<br>
                    - ✅ idx_password_resets_token<br>
                    - ✅ idx_password_resets_email<br><br>
                    
                    <strong>测试数据:</strong><br>
                    - 用户: testuser (<EMAIL>)<br>
                    - 激活码: 10个可用<br>
                    - 配置: 5项系统配置
                </div>
            `;
        }
        
        // 页面加载时自动检查数据库
        window.addEventListener('load', () => {
            checkDatabase();
        });
    </script>
</body>
</html>
