<?php
/**
 * 手动复制vendor文件
 * 解决Windows路径问题
 */

echo "<h1>手动复制vendor文件</h1>\n";
echo "<p>开始时间: " . date('Y-m-d H:i:s') . "</p>\n";

$sourceBase = '../../test_email_system/vendor';
$targetBase = '../vendor';

echo "<h2>步骤1: 检查源目录</h2>\n";
if (!is_dir($sourceBase)) {
    echo "<p style='color: red;'>❌ 源目录不存在: $sourceBase</p>\n";
    exit;
}
echo "<p style='color: green;'>✅ 源目录存在: $sourceBase</p>\n";

echo "<h2>步骤2: 创建目标目录结构</h2>\n";
$directories = [
    $targetBase,
    $targetBase . '/composer',
    $targetBase . '/phpmailer',
    $targetBase . '/phpmailer/phpmailer',
    $targetBase . '/phpmailer/phpmailer/src'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "<p style='color: green;'>✅ 创建目录: $dir</p>\n";
        } else {
            echo "<p style='color: red;'>❌ 创建目录失败: $dir</p>\n";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ 目录已存在: $dir</p>\n";
    }
}

echo "<h2>步骤3: 复制关键文件</h2>\n";
$filesToCopy = [
    // Composer核心文件
    'autoload.php' => 'autoload.php',
    'composer/ClassLoader.php' => 'composer/ClassLoader.php',
    'composer/autoload_real.php' => 'composer/autoload_real.php',
    'composer/autoload_static.php' => 'composer/autoload_static.php',
    'composer/autoload_psr4.php' => 'composer/autoload_psr4.php',
    'composer/autoload_classmap.php' => 'composer/autoload_classmap.php',
    'composer/autoload_namespaces.php' => 'composer/autoload_namespaces.php',
    'composer/platform_check.php' => 'composer/platform_check.php',
];

$copiedFiles = 0;
$errors = 0;

foreach ($filesToCopy as $source => $target) {
    $sourcePath = $sourceBase . '/' . $source;
    $targetPath = $targetBase . '/' . $target;
    
    if (file_exists($sourcePath)) {
        if (copy($sourcePath, $targetPath)) {
            echo "<p style='color: green;'>✅ 复制成功: $source</p>\n";
            $copiedFiles++;
        } else {
            echo "<p style='color: red;'>❌ 复制失败: $source</p>\n";
            $errors++;
        }
    } else {
        echo "<p style='color: orange;'>⚠️ 源文件不存在: $source</p>\n";
        $errors++;
    }
}

echo "<h2>步骤4: 复制PHPMailer目录</h2>\n";
$phpmailerSource = $sourceBase . '/phpmailer';
$phpmailerTarget = $targetBase . '/phpmailer';

if (is_dir($phpmailerSource)) {
    echo "<p>开始复制PHPMailer目录...</p>\n";
    
    // 使用递归复制
    function copyDir($src, $dst) {
        $dir = opendir($src);
        @mkdir($dst);
        $count = 0;
        
        while (false !== ($file = readdir($dir))) {
            if (($file != '.') && ($file != '..')) {
                if (is_dir($src . '/' . $file)) {
                    $count += copyDir($src . '/' . $file, $dst . '/' . $file);
                } else {
                    if (copy($src . '/' . $file, $dst . '/' . $file)) {
                        $count++;
                    }
                }
            }
        }
        closedir($dir);
        return $count;
    }
    
    $phpmailerFiles = copyDir($phpmailerSource, $phpmailerTarget);
    echo "<p style='color: green;'>✅ PHPMailer文件复制完成: $phpmailerFiles 个文件</p>\n";
    $copiedFiles += $phpmailerFiles;
} else {
    echo "<p style='color: red;'>❌ PHPMailer源目录不存在</p>\n";
    $errors++;
}

echo "<h2>步骤5: 验证复制结果</h2>\n";
$requiredFiles = [
    'autoload.php',
    'composer/autoload_real.php',
    'composer/autoload_static.php',
    'composer/ClassLoader.php',
    'phpmailer/phpmailer/src/PHPMailer.php'
];

$allExists = true;
foreach ($requiredFiles as $file) {
    $path = $targetBase . '/' . $file;
    if (file_exists($path)) {
        echo "<p style='color: green;'>✅ $file</p>\n";
    } else {
        echo "<p style='color: red;'>❌ $file</p>\n";
        $allExists = false;
    }
}

echo "<h2>总结</h2>\n";
echo "<p><strong>复制的文件数:</strong> $copiedFiles</p>\n";
echo "<p><strong>错误数:</strong> $errors</p>\n";

if ($allExists && $errors == 0) {
    echo "<p style='color: green; font-weight: bold;'>🎉 vendor目录复制成功！</p>\n";
    echo "<p><a href='test-local-phpmailer.php'>测试本地PHPMailer</a></p>\n";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ vendor目录复制不完整，请检查错误</p>\n";
}

echo "<p>完成时间: " . date('Y-m-d H:i:s') . "</p>\n";
?>
