<?php
/**
 * 详细调试邮件发送
 */

echo "🔍 详细调试邮件发送...\n\n";

// 包含必要的文件
require_once '../server/email-config.php';
require_once '../server/email-sender.php';

// 1. 检查配置
echo "1. 检查配置详情:\n";
echo "==================\n";

try {
    $config = getEmailConfig();
    
    echo "✅ 配置加载成功:\n";
    echo "   服务商: " . $config['provider'] . "\n";
    echo "   SMTP主机: " . $config['smtp_host'] . "\n";
    echo "   SMTP端口: " . $config['smtp_port'] . "\n";
    echo "   加密方式: " . $config['smtp_secure'] . "\n";
    echo "   用户名: " . $config['username'] . "\n";
    echo "   密码: " . (empty($config['password']) ? '❌ 未设置' : '✅ 已设置') . "\n";
    echo "   发件人: " . $config['from_email'] . "\n";
    echo "   发件人名称: " . $config['from_name'] . "\n";
    
} catch (Exception $e) {
    echo "❌ 配置加载失败: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n" . str_repeat("=", 50) . "\n\n";

// 2. 测试SMTP连接
echo "2. 测试SMTP连接:\n";
echo "==================\n";

try {
    $emailSender = new EmailSender();
    
    // 使用反射来访问私有方法进行调试
    $reflection = new ReflectionClass($emailSender);
    $connectMethod = $reflection->getMethod('connect');
    $connectMethod->setAccessible(true);
    
    echo "尝试连接到 {$config['smtp_host']}:{$config['smtp_port']}...\n";
    
    $connectMethod->invoke($emailSender);
    
    echo "✅ SMTP连接成功！\n";
    
} catch (Exception $e) {
    echo "❌ SMTP连接失败: " . $e->getMessage() . "\n";
    
    // 提供详细的错误分析
    echo "\n🔧 可能的解决方案:\n";
    
    if (strpos($e->getMessage(), 'Connection refused') !== false) {
        echo "- 检查网络连接\n";
        echo "- 确认SMTP服务器地址和端口正确\n";
        echo "- 检查防火墙设置\n";
    }
    
    if (strpos($e->getMessage(), 'Authentication failed') !== false) {
        echo "- 检查用户名和密码是否正确\n";
        echo "- 对于Gmail，需要使用应用专用密码\n";
        echo "- 对于Outlook，可能需要启用基本认证\n";
    }
    
    if (strpos($e->getMessage(), 'TLS') !== false) {
        echo "- 检查TLS/SSL设置\n";
        echo "- 尝试使用不同的加密方式\n";
    }
    
    exit(1);
}

echo "\n" . str_repeat("=", 50) . "\n\n";

// 3. 测试简单邮件发送
echo "3. 测试简单邮件发送:\n";
echo "==================\n";

$testEmail = '<EMAIL>';
$subject = '测试邮件 - ' . date('Y-m-d H:i:s');
$body = '这是一封测试邮件，发送时间：' . date('Y-m-d H:i:s');

try {
    echo "发送测试邮件...\n";
    echo "收件人: $testEmail\n";
    echo "主题: $subject\n";
    echo "内容: $body\n\n";
    
    $result = $emailSender->sendEmail($testEmail, $subject, $body, false);
    
    if ($result['success']) {
        echo "✅ 邮件发送成功！\n";
        echo "   消息: " . $result['message'] . "\n";
    } else {
        echo "❌ 邮件发送失败: " . $result['message'] . "\n";
        
        if (isset($result['debug'])) {
            echo "   调试信息: " . $result['debug'] . "\n";
        }
        
        if (isset($result['smtp_log'])) {
            echo "   SMTP日志:\n";
            foreach ($result['smtp_log'] as $log) {
                echo "     $log\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ 发送异常: " . $e->getMessage() . "\n";
    echo "   文件: " . $e->getFile() . "\n";
    echo "   行号: " . $e->getLine() . "\n";
}

echo "\n🔧 调试完成！\n";

// 4. 网络连通性测试
echo "\n4. 网络连通性测试:\n";
echo "==================\n";

$host = $config['smtp_host'];
$port = $config['smtp_port'];

echo "测试到 $host:$port 的连接...\n";

$socket = @fsockopen($host, $port, $errno, $errstr, 10);
if ($socket) {
    echo "✅ 网络连接正常\n";
    fclose($socket);
} else {
    echo "❌ 网络连接失败: $errstr ($errno)\n";
    echo "   请检查网络设置和防火墙\n";
}
?>
