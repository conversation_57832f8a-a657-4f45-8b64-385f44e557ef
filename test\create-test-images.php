<?php
// 创建更大的测试图片
$logoWidth = 200;
$logoHeight = 100;
$carWidth = 300;
$carHeight = 200;

// 创建Logo图片
$logoImage = imagecreate($logoWidth, $logoHeight);
$logoBackground = imagecolorallocate($logoImage, 255, 107, 107); // 红色背景
$logoTextColor = imagecolorallocate($logoImage, 255, 255, 255);  // 白色文字

imagefill($logoImage, 0, 0, $logoBackground);

$logoText = 'TEST LOGO';
$font_size = 5;
$text_width = imagefontwidth($font_size) * strlen($logoText);
$text_height = imagefontheight($font_size);
$x = ($logoWidth - $text_width) / 2;
$y = ($logoHeight - $text_height) / 2;

imagestring($logoImage, $font_size, $x, $y, $logoText, $logoTextColor);

// 创建汽车图片
$carImage = imagecreate($carWidth, $carHeight);
$carBackground = imagecolorallocate($carImage, 100, 150, 255); // 蓝色背景
$carTextColor = imagecolorallocate($carImage, 255, 255, 255);  // 白色文字

imagefill($carImage, 0, 0, $carBackground);

$carText = 'TEST CAR';
$car_text_width = imagefontwidth($font_size) * strlen($carText);
$car_text_height = imagefontheight($font_size);
$car_x = ($carWidth - $car_text_width) / 2;
$car_y = ($carHeight - $car_text_height) / 2;

imagestring($carImage, $font_size, $car_x, $car_y, $carText, $carTextColor);

// 确保uploads目录存在
if (!file_exists('../uploads')) {
    mkdir('../uploads', 0777, true);
}

// 保存图片
imagepng($logoImage, '../uploads/logo.png');
imagepng($carImage, '../uploads/car.png');

// 清理内存
imagedestroy($logoImage);
imagedestroy($carImage);

echo "更大的测试图片已创建！Logo: {$logoWidth}x{$logoHeight}, Car: {$carWidth}x{$carHeight}";
?>