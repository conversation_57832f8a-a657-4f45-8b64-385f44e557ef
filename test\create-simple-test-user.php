<?php
// 创建简单的测试用户
$dbPath = '../server/user_system.db3';

try {
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $username = 'testlogin';
    $password = 'test123';
    $email = '<EMAIL>';
    
    // 检查用户是否已存在
    $stmt = $pdo->prepare('SELECT id FROM users WHERE username = ?');
    $stmt->execute([$username]);
    
    if ($stmt->fetch()) {
        echo "用户 $username 已存在<br>\n";
    } else {
        // 创建用户
        $passwordHash = password_hash($password, PASSWORD_ARGON2ID);
        
        $stmt = $pdo->prepare('
            INSERT INTO users (username, password_hash, email, created_at) 
            VALUES (?, ?, ?, datetime("now"))
        ');
        
        $stmt->execute([$username, $passwordHash, $email]);
        
        echo "✅ 成功创建测试用户<br>\n";
        echo "用户名: $username<br>\n";
        echo "密码: $password<br>\n";
        echo "邮箱: $email<br>\n";
    }
    
    echo "<br><a href='test-login-without-captcha.html'>返回测试页面</a>";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage();
}
?>
