/* 锁屏页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

/* 动画背景 */
.animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: bgMove 20s ease-in-out infinite;
    z-index: -1;
}

@keyframes bgMove {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(5deg); }
}

/* 主容器 */
.lock-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2vh;
}

.lock-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3vh 2vw;
    max-width: 90vw;
    width: 100%;
    max-width: 800px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    text-align: center;
}

/* Logo区域 */
.logo-section {
    margin-bottom: 2vh;
}

.logo {
    max-width: 12vw;
    max-height: 8vh;
    height: auto;
}

/* 标题区域 */
.title-section {
    margin-bottom: 3vh;
}

.main-title {
    font-size: 3.5vw;
    color: #333;
    margin-bottom: 1vh;
    font-weight: bold;
}

.subtitle {
    font-size: 1.8vw;
    color: #666;
    margin-bottom: 0;
}

/* 付费信息 */
.payment-section {
    margin-bottom: 3vh;
    padding: 2vh;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    border-radius: 15px;
    color: white;
}

.price-info {
    margin-bottom: 2vh;
}

.price {
    font-size: 4vw;
    font-weight: bold;
    margin-right: 2vw;
}

.validity {
    font-size: 1.5vw;
    opacity: 0.9;
}

/* 二维码区域 */
.qr-codes {
    display: flex;
    justify-content: center;
    gap: 4vw;
    flex-wrap: wrap;
}

.qr-item {
    text-align: center;
}

.qr-code {
    width: 15vw;
    height: 15vw;
    max-width: 200px;
    max-height: 200px;
    border-radius: 10px;
    background: white;
    padding: 0.3vh; /* 减少内边距，提高二维码清晰度 */
    margin-bottom: 1vh;
    box-sizing: border-box; /* 确保padding不影响总尺寸 */
}

.qr-label {
    font-size: 1.4vw;
    font-weight: bold;
}

/* 激活码输入区域 */
.activation-section {
    margin-bottom: 3vh;
    padding: 2vh;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 15px;
}

.activation-title {
    font-size: 2.2vw;
    color: #333;
    margin-bottom: 2vh;
}

.input-group {
    display: flex;
    gap: 1vw;
    margin-bottom: 1vh;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}

.activation-input {
    flex: 1;
    min-width: 300px;
    max-width: 400px;
    padding: 1.5vh 1vw;
    font-size: 1.6vw;
    border: 2px solid #ddd;
    border-radius: 10px;
    text-align: center;
    font-family: 'Courier New', monospace;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.activation-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
}

.unlock-btn {
    padding: 1.5vh 3vw;
    font-size: 1.6vw;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    min-width: 120px;
}

.unlock-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.unlock-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 消息提示 */
.error-message {
    color: #e74c3c;
    font-size: 1.4vw;
    margin-top: 1vh;
    padding: 1vh;
    background: rgba(231, 76, 60, 0.1);
    border-radius: 8px;
    animation: shake 0.5s ease-in-out;
}

.success-message {
    color: #27ae60;
    font-size: 1.4vw;
    margin-top: 1vh;
    padding: 1vh;
    background: rgba(39, 174, 96, 0.1);
    border-radius: 8px;
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 使用说明 */
.instructions {
    margin-bottom: 2vh;
    text-align: left;
    background: rgba(255, 255, 255, 0.8);
    padding: 2vh;
    border-radius: 10px;
}

.instructions h4 {
    font-size: 1.8vw;
    color: #333;
    margin-bottom: 1vh;
    text-align: center;
}

.instructions ol {
    font-size: 1.4vw;
    color: #555;
    line-height: 1.6;
    padding-left: 2vw;
}

.instructions li {
    margin-bottom: 0.5vh;
}

/* 设备信息 */
.device-info {
    font-size: 1.2vw;
    color: #888;
    padding: 1vh;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    font-family: 'Courier New', monospace;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-title {
        font-size: 6vw;
    }
    
    .subtitle {
        font-size: 3.5vw;
    }
    
    .price {
        font-size: 8vw;
    }
    
    .validity {
        font-size: 3vw;
    }
    
    .qr-code {
        width: 25vw;
        height: 25vw;
        padding: 0.2vh; /* 移动端更小的内边距 */
    }
    
    .qr-label {
        font-size: 2.8vw;
    }
    
    .activation-title {
        font-size: 4.5vw;
    }
    
    .activation-input {
        font-size: 3.2vw;
        min-width: 250px;
    }
    
    .unlock-btn {
        font-size: 3.2vw;
        padding: 2vh 4vw;
    }
    
    .instructions h4 {
        font-size: 3.6vw;
    }
    
    .instructions ol {
        font-size: 2.8vw;
    }
    
    .device-info {
        font-size: 2.4vw;
    }
    
    .qr-codes {
        gap: 6vw;
    }
    
    .input-group {
        flex-direction: column;
        gap: 2vh;
    }
}
