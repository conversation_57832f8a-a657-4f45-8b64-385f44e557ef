<?php
/**
 * 复制PHPMailer到本项目
 * 将test_email_system中的PHPMailer相关文件复制到本项目的vendor目录
 */

header('Content-Type: text/html; charset=utf-8');

$copyResult = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['copy_phpmailer'])) {
    $copyResult = copyPHPMailerFiles();
}

function copyPHPMailerFiles() {
    $result = [
        'success' => false,
        'copied_files' => [],
        'errors' => [],
        'total_files' => 0
    ];
    
    try {
        $sourceBase = '../../test_email_system/vendor';
        $targetBase = '../vendor';
        
        // 确保目标目录存在
        if (!is_dir($targetBase)) {
            mkdir($targetBase, 0755, true);
        }
        if (!is_dir($targetBase . '/composer')) {
            mkdir($targetBase . '/composer', 0755, true);
        }
        if (!is_dir($targetBase . '/phpmailer')) {
            mkdir($targetBase . '/phpmailer', 0755, true);
        }
        
        // 需要复制的文件列表
        $filesToCopy = [
            // Composer核心文件
            'autoload.php' => 'autoload.php',
            'composer/ClassLoader.php' => 'composer/ClassLoader.php',
            'composer/autoload_real.php' => 'composer/autoload_real.php',
            'composer/autoload_static.php' => 'composer/autoload_static.php',
            'composer/autoload_psr4.php' => 'composer/autoload_psr4.php',
            'composer/autoload_classmap.php' => 'composer/autoload_classmap.php',
            'composer/autoload_namespaces.php' => 'composer/autoload_namespaces.php',
            'composer/platform_check.php' => 'composer/platform_check.php',
        ];
        
        // 复制Composer文件
        foreach ($filesToCopy as $source => $target) {
            $sourcePath = $sourceBase . '/' . $source;
            $targetPath = $targetBase . '/' . $target;
            
            if (file_exists($sourcePath)) {
                $targetDir = dirname($targetPath);
                if (!is_dir($targetDir)) {
                    mkdir($targetDir, 0755, true);
                }
                
                if (copy($sourcePath, $targetPath)) {
                    $result['copied_files'][] = $target;
                    $result['total_files']++;
                } else {
                    $result['errors'][] = "复制失败: $source -> $target";
                }
            } else {
                $result['errors'][] = "源文件不存在: $source";
            }
        }
        
        // 递归复制PHPMailer目录
        $phpmailerSource = $sourceBase . '/phpmailer';
        $phpmailerTarget = $targetBase . '/phpmailer';
        
        if (is_dir($phpmailerSource)) {
            $phpmailerResult = copyDirectory($phpmailerSource, $phpmailerTarget);
            $result['copied_files'] = array_merge($result['copied_files'], $phpmailerResult['files']);
            $result['total_files'] += $phpmailerResult['count'];
            $result['errors'] = array_merge($result['errors'], $phpmailerResult['errors']);
        }
        
        $result['success'] = empty($result['errors']);
        
    } catch (Exception $e) {
        $result['errors'][] = "异常: " . $e->getMessage();
    }
    
    return $result;
}

function copyDirectory($source, $target) {
    $result = ['files' => [], 'count' => 0, 'errors' => []];
    
    if (!is_dir($target)) {
        mkdir($target, 0755, true);
    }
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::SELF_FIRST
    );
    
    foreach ($iterator as $item) {
        $targetPath = $target . DIRECTORY_SEPARATOR . $iterator->getSubPathName();
        
        if ($item->isDir()) {
            if (!is_dir($targetPath)) {
                mkdir($targetPath, 0755, true);
            }
        } else {
            $targetDir = dirname($targetPath);
            if (!is_dir($targetDir)) {
                mkdir($targetDir, 0755, true);
            }
            
            if (copy($item, $targetPath)) {
                $result['files'][] = 'phpmailer/' . $iterator->getSubPathName();
                $result['count']++;
            } else {
                $result['errors'][] = "复制失败: " . $item . " -> " . $targetPath;
            }
        }
    }
    
    return $result;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复制PHPMailer到本项目</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; max-height: 300px; overflow-y: auto; }
        button { padding: 8px 16px; margin: 5px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
        .highlight { background: #ffffcc; padding: 15px; border-left: 4px solid #ffeb3b; margin: 15px 0; }
    </style>
</head>
<body>
    <h1>📦 复制PHPMailer到本项目</h1>
    <p><strong>操作时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="highlight">
        <h3>🎯 操作说明</h3>
        <p><strong>目的:</strong> 将test_email_system中的PHPMailer相关文件复制到本项目的vendor目录</p>
        <p><strong>原因:</strong> 确保项目部署到服务器时不依赖外部路径</p>
        <p><strong>复制内容:</strong></p>
        <ul>
            <li>Composer自动加载文件</li>
            <li>PHPMailer库文件</li>
            <li>相关依赖文件</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>📊 源文件检查</h2>
        <?php
        $sourceBase = '../../test_email_system/vendor';
        $sourceExists = is_dir($sourceBase);
        
        echo "<p><strong>源目录:</strong> $sourceBase</p>";
        echo "<p><strong>存在状态:</strong> " . ($sourceExists ? '<span class="success">存在</span>' : '<span class="error">不存在</span>') . "</p>";
        
        if ($sourceExists) {
            $phpmailerDir = $sourceBase . '/phpmailer';
            $phpmailerExists = is_dir($phpmailerDir);
            echo "<p><strong>PHPMailer目录:</strong> " . ($phpmailerExists ? '<span class="success">存在</span>' : '<span class="error">不存在</span>') . "</p>";
            
            $autoloadFile = $sourceBase . '/autoload.php';
            $autoloadExists = file_exists($autoloadFile);
            echo "<p><strong>autoload.php:</strong> " . ($autoloadExists ? '<span class="success">存在</span>' : '<span class="error">不存在</span>') . "</p>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>🔧 执行复制操作</h2>
        <?php if (empty($copyResult)): ?>
            <form method="POST">
                <p>点击下面的按钮开始复制PHPMailer文件到本项目:</p>
                <button type="submit" name="copy_phpmailer">开始复制PHPMailer</button>
            </form>
        <?php else: ?>
            <h3>复制结果:</h3>
            
            <?php if ($copyResult['success']): ?>
                <div class="success">
                    <h4>✅ 复制成功!</h4>
                    <p><strong>总共复制文件:</strong> <?php echo $copyResult['total_files']; ?> 个</p>
                </div>
            <?php else: ?>
                <div class="error">
                    <h4>❌ 复制过程中出现错误</h4>
                    <p><strong>复制的文件:</strong> <?php echo count($copyResult['copied_files']); ?> 个</p>
                    <p><strong>错误数量:</strong> <?php echo count($copyResult['errors']); ?> 个</p>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($copyResult['copied_files'])): ?>
                <h4>已复制的文件:</h4>
                <pre><?php echo implode("\n", array_slice($copyResult['copied_files'], 0, 20)); ?>
<?php if (count($copyResult['copied_files']) > 20): ?>
... 还有 <?php echo count($copyResult['copied_files']) - 20; ?> 个文件
<?php endif; ?></pre>
            <?php endif; ?>
            
            <?php if (!empty($copyResult['errors'])): ?>
                <h4>错误信息:</h4>
                <pre class="error"><?php echo implode("\n", $copyResult['errors']); ?></pre>
            <?php endif; ?>
            
            <form method="POST">
                <button type="submit" name="copy_phpmailer">重新复制</button>
            </form>
        <?php endif; ?>
    </div>
    
    <?php if (!empty($copyResult) && $copyResult['success']): ?>
    <div class="test-section">
        <h2>✅ 下一步操作</h2>
        <p>PHPMailer文件复制成功！现在需要:</p>
        <ol>
            <li><strong>更新路径引用:</strong> 修改working-email-sender.php中的路径</li>
            <li><strong>测试加载:</strong> 验证PHPMailer是否能正常加载</li>
            <li><strong>测试邮件发送:</strong> 确认邮件功能正常</li>
        </ol>
        
        <p><a href="update-phpmailer-paths.php"><button>更新路径引用</button></a></p>
    </div>
    <?php endif; ?>
    
    <div class="test-section">
        <h2>🔗 相关链接</h2>
        <ul>
            <li><a href="verify-phpmailer-path.php">验证PHPMailer路径</a></li>
            <li><a href="test-working-email-sender.php">测试工作的邮件发送器</a></li>
            <li><a href="../new-lock.html">返回登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
