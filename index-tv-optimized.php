<?php
// 多品牌Android TV优化版倒计时网站主页面
session_start();

// 检查是否有品牌参数
$hasValidBrand = false;
$brandPath = null;

if (isset($_GET['brand'])) {
    // 从URL参数获取品牌
    $brandPath = $_GET['brand'];
    $hasValidBrand = true;
} elseif (isset($_SERVER['BRAND_PATH'])) {
    // 从环境变量获取品牌（兼容router.php）
    $brandPath = $_SERVER['BRAND_PATH'];
    $hasValidBrand = true;
}

// 如果没有品牌信息，重定向到品牌选择页面
if (!$hasValidBrand) {
    header('Location: brand-selector.html');
    exit;
}

// 设置环境变量供品牌处理器使用
$_SERVER['BRAND_PATH'] = $brandPath;

// 引入品牌处理器
require_once 'includes/brand-handler.php';

// 获取当前品牌信息
$currentBrand = $brandHandler->getCurrentBrand();
$brandPath = $brandHandler->getBrandPath();
$brandName = $brandHandler->getBrandName();

// 获取品牌相关的图片路径
$logoPath = $brandHandler->getLogoPath();
$carPath = $brandHandler->getCarImagePath();

// 设置倒计时目标日期 (2025年12月31日 24:00，即2026年1月1日 00:00:00)
$targetDate = '2026-01-01 00:00:00';

// 页面标题（固定为国家报废置换补贴）
$pageTitle = '国家报废置换补贴倒计时 - Android TV优化版';
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>国家报废置换补贴倒计时 - Android TV优化版</title>
    <link rel="stylesheet" href="css/style-tv-optimized.css">
</head>
<body>
    <!-- Logo显示区域 -->
    <div class="logo-container">
        <?php if (file_exists($logoPath)): ?>
            <img src="<?php echo $logoPath; ?>" alt="Logo" class="logo">
        <?php endif; ?>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
        <h1 class="main-title">国家报废置换补贴 12.31 24:00</h1>
        
        <div class="countdown-container">
            <div class="countdown-item">
                <span class="countdown-number" id="days">00</span>
                <span class="countdown-label">天</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number" id="hours">00</span>
                <span class="countdown-label">小时</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number" id="minutes">00</span>
                <span class="countdown-label">分钟</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number" id="seconds">00</span>
                <span class="countdown-label">秒</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number" id="milliseconds">000</span>
                <span class="countdown-label">毫秒</span>
            </div>
        </div>

        <h2 class="warning-title">2026年1月1日起买车成本至高增加<span class="highlight-number">20000</span>元</h2>
    </div>

    <!-- 汽车图片区域 -->
    <div class="car-container">
        <?php if (file_exists($carPath)): ?>
            <img src="<?php echo $carPath; ?>" alt="汽车" class="car-image">
        <?php endif; ?>
    </div>

    <!-- 用户图标 -->
    <div class="user-icon-container">
        <div class="user-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
            </svg>
        </div>
        <div class="user-menu">
            <div class="user-info">
                <div class="username" id="display-username">加载中...</div>
                <div class="version-info">TV优化版本</div>
                <div class="activation-code" id="display-activation-code">加载中...</div>
                <div class="expiry-date" id="display-expiry-date">加载中...</div>
            </div>
            <button class="logout-btn">🚪 退出登录</button>
        </div>
    </div>

    <!-- 版本切换图标 -->
    <div class="version-toggle-container">
        <div class="version-toggle" title="<?php echo htmlspecialchars($brandHandler->getToggleVersionText()); ?>" onclick="window.location.href='<?php echo htmlspecialchars($brandHandler->getToggleVersionUrl()); ?>'">
            <?php echo $brandHandler->getToggleVersionText(); ?>
        </div>
    </div>

    <script>
        // Android TV优化的倒计时脚本
        class TVOptimizedCountdown {
            constructor(targetDate) {
                this.targetDate = new Date(targetDate).getTime();
                this.elements = {
                    days: document.getElementById('days'),
                    hours: document.getElementById('hours'),
                    minutes: document.getElementById('minutes'),
                    seconds: document.getElementById('seconds'),
                    milliseconds: document.getElementById('milliseconds')
                };
                
                // 缓存上次的值，避免不必要的DOM更新
                this.lastValues = {
                    days: '',
                    hours: '',
                    minutes: '',
                    seconds: '',
                    milliseconds: ''
                };
                
                // 性能优化：预计算填充字符串
                this.padCache = {};
                for (let i = 0; i < 1000; i++) {
                    this.padCache[i] = {
                        '2': i.toString().padStart(2, '0'),
                        '3': i.toString().padStart(3, '0')
                    };
                }
                
                this.init();
            }
            
            init() {
                // 使用requestAnimationFrame进行优化
                this.isRunning = true;
                this.lastUpdateTime = 0;
                this.updateInterval = 50; // 50ms更新一次，降低频率
                
                this.update();
            }
            
            update() {
                if (!this.isRunning) return;
                
                const currentTime = performance.now();
                
                // 限制更新频率
                if (currentTime - this.lastUpdateTime >= this.updateInterval) {
                    this.updateCountdown();
                    this.lastUpdateTime = currentTime;
                }
                
                requestAnimationFrame(() => this.update());
            }
            
            updateCountdown() {
                const now = Date.now();
                const distance = this.targetDate - now;
                
                if (distance < 0) {
                    this.setFinalValues();
                    return;
                }
                
                // 批量计算所有值
                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);
                const milliseconds = Math.floor(distance % 1000);
                
                // 使用缓存的填充字符串
                const newValues = {
                    days: this.padCache[Math.min(days, 999)]?.['2'] || days.toString().padStart(2, '0'),
                    hours: this.padCache[hours]?.['2'] || hours.toString().padStart(2, '0'),
                    minutes: this.padCache[minutes]?.['2'] || minutes.toString().padStart(2, '0'),
                    seconds: this.padCache[seconds]?.['2'] || seconds.toString().padStart(2, '0'),
                    milliseconds: this.padCache[milliseconds]?.['3'] || milliseconds.toString().padStart(3, '0')
                };
                
                // 只更新发生变化的元素
                this.updateChangedElements(newValues);
            }
            
            updateChangedElements(newValues) {
                for (const [key, newValue] of Object.entries(newValues)) {
                    if (this.lastValues[key] !== newValue) {
                        this.elements[key].textContent = newValue;
                        this.lastValues[key] = newValue;
                    }
                }
            }
            
            setFinalValues() {
                const finalValues = {
                    days: '00',
                    hours: '00',
                    minutes: '00',
                    seconds: '00',
                    milliseconds: '000'
                };
                
                this.updateChangedElements(finalValues);
                this.isRunning = false;
            }
            
            // 提供暂停/恢复功能
            pause() {
                this.isRunning = false;
            }
            
            resume() {
                if (!this.isRunning) {
                    this.isRunning = true;
                    this.update();
                }
            }
        }
        
        // 页面可见性API优化 - 页面不可见时暂停倒计时
        let countdown;
        
        function initCountdown() {
            countdown = new TVOptimizedCountdown('<?php echo $targetDate; ?>');
        }
        
        // 页面可见性变化处理
        document.addEventListener('visibilitychange', function() {
            if (countdown) {
                if (document.hidden) {
                    countdown.pause();
                } else {
                    countdown.resume();
                }
            }
        });
        
        // 页面加载完成后初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initCountdown);
        } else {
            initCountdown();
        }
        
        // 内存清理
        window.addEventListener('beforeunload', function() {
            if (countdown) {
                countdown.pause();
            }
        });

        // 版本切换功能
        document.addEventListener('DOMContentLoaded', function() {
            // 版本切换已通过onclick处理，这里保留其他逻辑
            const versionToggle = document.querySelector('.version-toggle');
            if (versionToggle && !versionToggle.onclick) {
                versionToggle.addEventListener('click', function() {
                    // 设置手动切换标记
                    sessionStorage.setItem('manual_version_switch', 'true');
                    sessionStorage.setItem('device_redirect_done', 'true');

                    // 跳转到对应品牌的标准版本
                    window.location.href = '<?php echo htmlspecialchars($brandHandler->getToggleVersionUrl()); ?>';
                });
            }

            // 退出登录功能
            document.querySelector('.logout-btn').addEventListener('click', function() {
                // 清除会话数据
                sessionStorage.clear();
                localStorage.clear();

                // 跳转到正确的登录页面
                window.location.href = 'new-lock.html';
            });

            // 获取并显示真实的用户信息
            loadUserInfo();
        });

        // 获取并显示真实的用户信息
        async function loadUserInfo() {
            try {
                const sessionToken = localStorage.getItem('session_token') ||
                                   sessionStorage.getItem('session_token') ||
                                   getCookie('session_token');

                if (!sessionToken) {
                    console.log('未找到会话令牌');
                    return;
                }

                const response = await fetch('api/verify-session.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        session_token: sessionToken
                    })
                });

                const data = await response.json();

                if (data.success && data.data) {
                    // 更新用户名
                    const usernameElement = document.getElementById('display-username');
                    if (usernameElement) {
                        usernameElement.textContent = data.data.username || '未知用户';
                    }

                    // 更新激活码
                    const activationCodeElement = document.getElementById('display-activation-code');
                    if (activationCodeElement) {
                        activationCodeElement.textContent = data.data.activation_code || '未绑定激活码';
                    }

                    // 更新到期时间
                    const expiryDateElement = document.getElementById('display-expiry-date');
                    if (expiryDateElement) {
                        if (data.data.activation_expires) {
                            const expiryDate = new Date(data.data.activation_expires * 1000);
                            expiryDateElement.textContent = '到期时间: ' + expiryDate.toLocaleDateString('zh-CN');
                        } else {
                            expiryDateElement.textContent = '到期时间: 未设置';
                        }
                    }
                } else {
                    console.log('获取用户信息失败:', data.message);
                }
            } catch (error) {
                console.error('加载用户信息错误:', error);
            }
        }

        // 获取Cookie的辅助函数
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }
    </script>

    <!-- 智能设备检测脚本 -->
    <script src="js/smart-device-detector.js"></script>

    <!-- 增强版会话检查脚本 -->
    <script src="js/enhanced-session-check.js"></script>
</body>
</html>
