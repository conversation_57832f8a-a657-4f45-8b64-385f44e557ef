<?php
/**
 * 测试激活码绑定API
 */

header('Content-Type: text/html; charset=utf-8');

$dbPath = '../server/user_system.db3';
$testCode = 'N3WK7-7YJW7-JHXB4-NWWEJ';

// 模拟绑定API的完整逻辑
function testBindingAPI($activationCode, $mockUserId = 999) {
    global $dbPath;
    
    $result = [
        'success' => false,
        'message' => '',
        'details' => [],
        'code_info' => null,
        'checks' => []
    ];
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 1. 检查激活码是否存在
        $stmt = $pdo->prepare("
            SELECT id, code, is_used, bound_user_id, expires_at, status, created_at, used_at
            FROM activation_codes 
            WHERE code = ?
        ");
        $stmt->execute([$activationCode]);
        $codeInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $result['code_info'] = $codeInfo;
        
        if (!$codeInfo) {
            $result['message'] = '激活码不存在';
            $result['checks'][] = ['step' => '激活码存在性检查', 'result' => '❌ 失败', 'detail' => '激活码不存在'];
            return $result;
        }
        $result['checks'][] = ['step' => '激活码存在性检查', 'result' => '✅ 通过', 'detail' => '激活码存在'];
        
        // 2. 检查激活码状态
        if ($codeInfo['status'] !== 'active') {
            $result['message'] = '激活码状态无效: ' . $codeInfo['status'];
            $result['checks'][] = ['step' => '激活码状态检查', 'result' => '❌ 失败', 'detail' => "状态: {$codeInfo['status']}"];
            return $result;
        }
        $result['checks'][] = ['step' => '激活码状态检查', 'result' => '✅ 通过', 'detail' => '状态: active'];
        
        // 3. 检查是否已使用（使用修复后的逻辑）
        if ($codeInfo['is_used']) {
            // 检查数据一致性：如果is_used=1但bound_user_id为空，说明数据不一致
            if ($codeInfo['bound_user_id'] === null || $codeInfo['bound_user_id'] === '') {
                // 数据不一致，记录错误并提供修复建议
                $result['message'] = '激活码状态异常，请联系管理员或稍后重试';
                $result['checks'][] = ['step' => '数据一致性检查', 'result' => '❌ 失败', 'detail' => 'is_used=1但bound_user_id为空'];
                return $result;
            }
            
            // 使用严格比较避免类型转换问题
            if ((int)$codeInfo['bound_user_id'] === (int)$mockUserId) {
                $result['message'] = '您已经绑定过这个激活码了';
                $result['checks'][] = ['step' => '重复绑定检查', 'result' => '❌ 失败', 'detail' => '已绑定给当前用户'];
                return $result;
            } else {
                $result['message'] = '激活码已被其他用户使用';
                $result['checks'][] = ['step' => '使用状态检查', 'result' => '❌ 失败', 'detail' => '已被其他用户使用'];
                return $result;
            }
        }
        $result['checks'][] = ['step' => '使用状态检查', 'result' => '✅ 通过', 'detail' => 'is_used=0，未使用'];
        
        // 4. 检查是否过期
        if (strtotime($codeInfo['expires_at']) <= time()) {
            $result['message'] = '激活码已过期';
            $result['checks'][] = ['step' => '过期检查', 'result' => '❌ 失败', 'detail' => "过期时间: {$codeInfo['expires_at']}"];
            return $result;
        }
        $result['checks'][] = ['step' => '过期检查', 'result' => '✅ 通过', 'detail' => "过期时间: {$codeInfo['expires_at']}"];
        
        // 5. 模拟绑定操作（不实际执行）
        $result['success'] = true;
        $result['message'] = '激活码可以正常绑定！';
        $result['checks'][] = ['step' => '绑定模拟', 'result' => '✅ 成功', 'detail' => '所有检查通过，可以绑定'];
        
        return $result;
        
    } catch (Exception $e) {
        $result['message'] = '测试失败: ' . $e->getMessage();
        $result['checks'][] = ['step' => '异常处理', 'result' => '❌ 错误', 'detail' => $e->getMessage()];
        return $result;
    }
}

// 执行测试
$testResult = testBindingAPI($testCode);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>绑定API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
        .warning-box { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; }
        .error-box { background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .mono { font-family: monospace; background: #f5f5f5; padding: 2px 4px; }
        .step-pass { color: green; }
        .step-fail { color: red; }
    </style>
</head>
<body>
    <h1>🧪 激活码绑定API测试</h1>
    <p><strong>测试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    <p><strong>测试激活码:</strong> <code><?php echo $testCode; ?></code></p>
    <p><strong>模拟用户ID:</strong> 999</p>
    
    <div class="section">
        <h2>📋 测试步骤详情</h2>
        
        <table>
            <tr><th>步骤</th><th>结果</th><th>详情</th></tr>
            <?php foreach ($testResult['checks'] as $check): ?>
                <tr>
                    <td><?php echo htmlspecialchars($check['step']); ?></td>
                    <td class="<?php echo strpos($check['result'], '✅') !== false ? 'step-pass' : 'step-fail'; ?>">
                        <?php echo htmlspecialchars($check['result']); ?>
                    </td>
                    <td><?php echo htmlspecialchars($check['detail']); ?></td>
                </tr>
            <?php endforeach; ?>
        </table>
    </div>
    
    <div class="section">
        <h2>🎯 最终结果</h2>
        
        <div class="<?php echo $testResult['success'] ? 'highlight' : 'error-box'; ?>">
            <h3><?php echo $testResult['success'] ? '✅ 测试通过' : '❌ 测试失败'; ?></h3>
            <p><strong>API响应:</strong> <?php echo htmlspecialchars($testResult['message']); ?></p>
        </div>
    </div>
    
    <?php if ($testResult['code_info']): ?>
        <div class="section">
            <h2>📊 激活码信息</h2>
            
            <table>
                <tr><th>字段</th><th>值</th><th>类型检查</th></tr>
                <tr>
                    <td>is_used</td>
                    <td class="mono"><?php echo var_export($testResult['code_info']['is_used'], true); ?></td>
                    <td><?php echo $testResult['code_info']['is_used'] ? '❌ 已使用' : '✅ 未使用'; ?></td>
                </tr>
                <tr>
                    <td>bound_user_id</td>
                    <td class="mono"><?php echo var_export($testResult['code_info']['bound_user_id'], true); ?></td>
                    <td><?php echo $testResult['code_info']['bound_user_id'] === null ? '✅ 未绑定' : '❌ 已绑定'; ?></td>
                </tr>
                <tr>
                    <td>status</td>
                    <td class="mono"><?php echo htmlspecialchars($testResult['code_info']['status']); ?></td>
                    <td><?php echo $testResult['code_info']['status'] === 'active' ? '✅ 活跃' : '❌ 非活跃'; ?></td>
                </tr>
                <tr>
                    <td>expires_at</td>
                    <td class="mono"><?php echo htmlspecialchars($testResult['code_info']['expires_at']); ?></td>
                    <td><?php echo strtotime($testResult['code_info']['expires_at']) > time() ? '✅ 有效' : '❌ 过期'; ?></td>
                </tr>
            </table>
        </div>
    <?php endif; ?>
    
    <div class="section">
        <h2>🔧 问题排查建议</h2>
        
        <?php if ($testResult['success']): ?>
            <div class="highlight">
                <h4>✅ API逻辑正常</h4>
                <p>绑定API的所有检查都通过了。如果用户仍然遇到问题，可能是：</p>
                <ul>
                    <li>🔄 <strong>浏览器缓存</strong>: 建议用户清除浏览器缓存或使用无痕模式</li>
                    <li>🔐 <strong>登录状态</strong>: 检查用户是否正确登录</li>
                    <li>🌐 <strong>网络问题</strong>: 检查网络连接和服务器状态</li>
                    <li>📱 <strong>前端问题</strong>: 检查JavaScript是否正常工作</li>
                </ul>
            </div>
        <?php else: ?>
            <div class="error-box">
                <h4>❌ 发现问题</h4>
                <p>绑定API检查失败，需要进一步排查：</p>
                <ul>
                    <li>检查激活码数据是否正确</li>
                    <li>检查数据库连接是否正常</li>
                    <li>检查服务器时间设置</li>
                </ul>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="section">
        <h2>🔗 相关工具</h2>
        <ul>
            <li><a href="debug-activation-code.php">激活码数据调试</a></li>
            <li><a href="activation-code-diagnosis.php">激活码状态诊断</a></li>
            <li><a href="../new-lock.html">用户登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
