<?php
/**
 * Session清理工具
 * 清理过期和无效的user_sessions记录
 */

header('Content-Type: text/html; charset=utf-8');

$dbPath = '../server/user_system.db3';

$actionResult = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'cleanup_expired') {
        $actionResult = cleanupExpiredSessions();
    } elseif ($action === 'cleanup_inactive') {
        $actionResult = cleanupInactiveSessions();
    } elseif ($action === 'limit_user_sessions') {
        $actionResult = limitUserSessions();
    } elseif ($action === 'full_cleanup') {
        $actionResult = performFullCleanup();
    }
}

function cleanupExpiredSessions() {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE expires_at < datetime('now')");
        $stmt->execute();
        $deletedCount = $stmt->rowCount();
        
        return [
            'success' => true,
            'deleted_count' => $deletedCount,
            'message' => "清理了 {$deletedCount} 个过期会话"
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

function cleanupInactiveSessions() {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("
            DELETE FROM user_sessions 
            WHERE is_active = 0 AND created_at < datetime('now', '-7 days')
        ");
        $stmt->execute();
        $deletedCount = $stmt->rowCount();
        
        return [
            'success' => true,
            'deleted_count' => $deletedCount,
            'message' => "清理了 {$deletedCount} 个非活跃会话"
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

function limitUserSessions() {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $pdo->beginTransaction();
        
        // 获取所有用户
        $stmt = $pdo->query("SELECT DISTINCT user_id FROM user_sessions");
        $users = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $totalDeleted = 0;
        
        foreach ($users as $userId) {
            // 保留每用户最新的3个会话
            $stmt = $pdo->prepare("
                DELETE FROM user_sessions 
                WHERE user_id = ? AND id NOT IN (
                    SELECT id FROM (
                        SELECT id FROM user_sessions 
                        WHERE user_id = ? 
                        ORDER BY created_at DESC 
                        LIMIT 3
                    )
                )
            ");
            $stmt->execute([$userId, $userId]);
            $totalDeleted += $stmt->rowCount();
        }
        
        $pdo->commit();
        
        return [
            'success' => true,
            'deleted_count' => $totalDeleted,
            'message' => "限制用户会话数，删除了 {$totalDeleted} 个旧会话"
        ];
        
    } catch (Exception $e) {
        if (isset($pdo)) {
            $pdo->rollBack();
        }
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

function performFullCleanup() {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $pdo->beginTransaction();
        
        $totalDeleted = 0;
        
        // 1. 清理过期会话
        $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE expires_at < datetime('now')");
        $stmt->execute();
        $totalDeleted += $stmt->rowCount();
        
        // 2. 清理非活跃会话
        $stmt = $pdo->prepare("
            DELETE FROM user_sessions 
            WHERE is_active = 0 AND created_at < datetime('now', '-7 days')
        ");
        $stmt->execute();
        $totalDeleted += $stmt->rowCount();
        
        // 3. 限制每用户会话数
        $stmt = $pdo->query("SELECT DISTINCT user_id FROM user_sessions");
        $users = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($users as $userId) {
            $stmt = $pdo->prepare("
                DELETE FROM user_sessions 
                WHERE user_id = ? AND id NOT IN (
                    SELECT id FROM (
                        SELECT id FROM user_sessions 
                        WHERE user_id = ? 
                        ORDER BY created_at DESC 
                        LIMIT 3
                    )
                )
            ");
            $stmt->execute([$userId, $userId]);
            $totalDeleted += $stmt->rowCount();
        }
        
        // 4. 优化数据库
        $pdo->exec("VACUUM");
        
        $pdo->commit();
        
        return [
            'success' => true,
            'deleted_count' => $totalDeleted,
            'message' => "完整清理完成，删除了 {$totalDeleted} 个会话记录"
        ];
        
    } catch (Exception $e) {
        if (isset($pdo)) {
            $pdo->rollBack();
        }
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

function getSessionStats() {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stats = [];
        
        // 基本统计
        $stmt = $pdo->query("SELECT COUNT(*) FROM user_sessions");
        $stats['total'] = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM user_sessions WHERE is_active = 1");
        $stats['active'] = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM user_sessions WHERE expires_at < datetime('now')");
        $stats['expired'] = $stmt->fetchColumn();
        
        $stmt = $pdo->query("
            SELECT COUNT(*) FROM user_sessions 
            WHERE is_active = 0 AND expires_at > datetime('now')
        ");
        $stats['inactive'] = $stmt->fetchColumn();
        
        return $stats;
        
    } catch (Exception $e) {
        return ['error' => $e->getMessage()];
    }
}

$stats = getSessionStats();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session清理工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        button { padding: 8px 16px; margin: 5px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
        button.danger { background: #f44336; }
        button.danger:hover { background: #da190b; }
        button.warning { background: #ff9800; }
        button.warning:hover { background: #e68900; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
        .warning-box { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; }
    </style>
</head>
<body>
    <h1>🧹 Session清理工具</h1>
    <p><strong>清理时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <?php if ($actionResult): ?>
    <div class="test-section">
        <h2>📋 操作结果</h2>
        <?php if ($actionResult['success']): ?>
            <div class="success">
                <h3>✅ 操作成功</h3>
                <p><?php echo htmlspecialchars($actionResult['message']); ?></p>
                <p><strong>删除记录数:</strong> <?php echo number_format($actionResult['deleted_count']); ?> 条</p>
            </div>
        <?php else: ?>
            <div class="error">
                <h3>❌ 操作失败</h3>
                <p><?php echo htmlspecialchars($actionResult['error']); ?></p>
            </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>
    
    <div class="test-section">
        <h2>📊 当前Session统计</h2>
        
        <?php if (isset($stats['error'])): ?>
            <p class="error">❌ 获取统计失败: <?php echo htmlspecialchars($stats['error']); ?></p>
        <?php else: ?>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3><?php echo number_format($stats['total']); ?></h3>
                    <p>总会话数</p>
                </div>
                <div class="stat-card">
                    <h3 class="success"><?php echo number_format($stats['active']); ?></h3>
                    <p>活跃会话</p>
                </div>
                <div class="stat-card">
                    <h3 class="<?php echo $stats['expired'] > 0 ? 'warning' : 'success'; ?>">
                        <?php echo number_format($stats['expired']); ?>
                    </h3>
                    <p>过期会话</p>
                </div>
                <div class="stat-card">
                    <h3 class="info"><?php echo number_format($stats['inactive']); ?></h3>
                    <p>非活跃会话</p>
                </div>
            </div>
            
            <?php
            $wasteRatio = $stats['total'] > 0 ? 
                round((($stats['expired'] + $stats['inactive']) / $stats['total']) * 100, 1) : 0;
            ?>
            
            <?php if ($wasteRatio > 30): ?>
            <div class="warning-box">
                <h3>⚠️ 存储浪费警告</h3>
                <p><strong>无效会话占比:</strong> <?php echo $wasteRatio; ?>%</p>
                <p>建议立即执行清理操作以优化存储空间。</p>
            </div>
            <?php else: ?>
            <div class="highlight">
                <h3>✅ 存储状态良好</h3>
                <p><strong>无效会话占比:</strong> <?php echo $wasteRatio; ?>%</p>
                <p>当前存储效率良好，可选择性执行清理。</p>
            </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>🛠️ 清理操作</h2>
        
        <div class="warning-box">
            <h3>⚠️ 操作说明</h3>
            <p><strong>清理过期会话:</strong> 删除已过期的会话记录</p>
            <p><strong>清理非活跃会话:</strong> 删除7天前被挤掉的会话</p>
            <p><strong>限制用户会话数:</strong> 每用户最多保留3个最新会话</p>
            <p><strong>完整清理:</strong> 执行所有清理操作并优化数据库</p>
            <p><strong>注意:</strong> 清理操作不可逆，请谨慎操作</p>
        </div>
        
        <h3>单项清理操作:</h3>
        <form method="POST" style="display: inline;">
            <input type="hidden" name="action" value="cleanup_expired">
            <button type="submit" class="warning" onclick="return confirm('确定要清理过期会话吗？')">
                清理过期会话 (<?php echo $stats['expired'] ?? 0; ?> 个)
            </button>
        </form>
        
        <form method="POST" style="display: inline;">
            <input type="hidden" name="action" value="cleanup_inactive">
            <button type="submit" class="warning" onclick="return confirm('确定要清理非活跃会话吗？')">
                清理非活跃会话 (<?php echo $stats['inactive'] ?? 0; ?> 个)
            </button>
        </form>
        
        <form method="POST" style="display: inline;">
            <input type="hidden" name="action" value="limit_user_sessions">
            <button type="submit" class="warning" onclick="return confirm('确定要限制用户会话数吗？每用户只保留最新3个会话。')">
                限制用户会话数
            </button>
        </form>
        
        <h3>完整清理:</h3>
        <form method="POST" style="display: inline;">
            <input type="hidden" name="action" value="full_cleanup">
            <button type="submit" class="danger" onclick="return confirm('确定要执行完整清理吗？这将执行所有清理操作！')">
                完整清理
            </button>
        </form>
    </div>
    
    <div class="test-section">
        <h2>📋 清理建议</h2>
        
        <?php if ($stats['expired'] > 0): ?>
        <div class="warning-box">
            <h4>🔴 立即清理建议</h4>
            <p>发现 <?php echo $stats['expired']; ?> 个过期会话，建议立即清理。</p>
        </div>
        <?php endif; ?>
        
        <?php if ($stats['inactive'] > 20): ?>
        <div class="warning-box">
            <h4>🟡 定期清理建议</h4>
            <p>发现 <?php echo $stats['inactive']; ?> 个非活跃会话，建议定期清理。</p>
        </div>
        <?php endif; ?>
        
        <?php if ($stats['total'] > 100): ?>
        <div class="warning-box">
            <h4>🟠 会话数量建议</h4>
            <p>总会话数 <?php echo $stats['total']; ?> 较多，建议限制每用户会话数。</p>
        </div>
        <?php endif; ?>
        
        <h3>自动清理建议:</h3>
        <ul>
            <li><strong>定时任务:</strong> 每天凌晨执行过期会话清理</li>
            <li><strong>登录时清理:</strong> 用户登录时随机触发清理</li>
            <li><strong>会话限制:</strong> 新登录时自动清理用户旧会话</li>
            <li><strong>监控告警:</strong> 会话数超过阈值时发送告警</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关工具</h2>
        <ul>
            <li><a href="session-analysis.php">Session存储分析</a></li>
            <li><a href="login-logs-manager.php">登录日志管理</a></li>
            <li><a href="security-check.php">安全检查工具</a></li>
            <li><a href="../new-lock.html">返回登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
