<?php
/**
 * login_logs表清理工具
 * 自动清理旧的登录日志记录，保持数据库性能
 */

// 数据库配置
$dbPath = 'user_system.db3';

/**
 * 清理配置
 */
$cleanupConfig = [
    'max_records' => 1000,          // 最大保留记录数
    'keep_days' => 30,              // 保留最近N天的记录
    'batch_size' => 100,            // 批量删除大小
    'keep_important_logs' => true   // 是否保留重要日志（成功登录、密码重置等）
];

/**
 * 清理login_logs表
 */
function cleanupLoginLogs($config) {
    global $dbPath;
    
    $result = [
        'success' => false,
        'deleted_count' => 0,
        'remaining_count' => 0,
        'cleanup_methods' => [],
        'errors' => []
    ];
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 开始事务
        $pdo->beginTransaction();
        
        // 获取当前记录总数
        $stmt = $pdo->query("SELECT COUNT(*) FROM login_logs");
        $totalRecords = $stmt->fetchColumn();
        
        echo "📊 当前login_logs记录总数: $totalRecords\n";
        
        $deletedCount = 0;
        
        // 方法1: 按时间清理 - 删除超过指定天数的记录
        if ($config['keep_days'] > 0) {
            $cutoffDate = date('Y-m-d H:i:s', time() - ($config['keep_days'] * 24 * 3600));
            
            if ($config['keep_important_logs']) {
                // 保留重要日志，只删除失败的登录尝试
                $stmt = $pdo->prepare("
                    DELETE FROM login_logs 
                    WHERE login_time < ? 
                    AND status IN ('failed', 'blocked')
                ");
            } else {
                // 删除所有超过时间的记录
                $stmt = $pdo->prepare("
                    DELETE FROM login_logs 
                    WHERE login_time < ?
                ");
            }
            
            $stmt->execute([$cutoffDate]);
            $deletedByTime = $stmt->rowCount();
            $deletedCount += $deletedByTime;
            
            if ($deletedByTime > 0) {
                $result['cleanup_methods'][] = "按时间清理: 删除了 {$deletedByTime} 条超过 {$config['keep_days']} 天的记录";
                echo "🗑️  按时间清理: 删除了 {$deletedByTime} 条记录\n";
            }
        }
        
        // 方法2: 按数量清理 - 如果记录数仍然超过限制，删除最旧的记录
        $stmt = $pdo->query("SELECT COUNT(*) FROM login_logs");
        $currentCount = $stmt->fetchColumn();
        
        if ($currentCount > $config['max_records']) {
            $excessCount = $currentCount - $config['max_records'];
            
            if ($config['keep_important_logs']) {
                // 优先删除失败的登录记录
                $stmt = $pdo->prepare("
                    DELETE FROM login_logs 
                    WHERE id IN (
                        SELECT id FROM login_logs 
                        WHERE status IN ('failed', 'blocked')
                        ORDER BY login_time ASC 
                        LIMIT ?
                    )
                ");
                $stmt->execute([$excessCount]);
                $deletedByCount = $stmt->rowCount();
                
                // 如果还是超过限制，删除最旧的记录
                $stmt = $pdo->query("SELECT COUNT(*) FROM login_logs");
                $remainingCount = $stmt->fetchColumn();
                
                if ($remainingCount > $config['max_records']) {
                    $stillExcess = $remainingCount - $config['max_records'];
                    $stmt = $pdo->prepare("
                        DELETE FROM login_logs 
                        WHERE id IN (
                            SELECT id FROM login_logs 
                            ORDER BY login_time ASC 
                            LIMIT ?
                        )
                    ");
                    $stmt->execute([$stillExcess]);
                    $deletedByCount += $stmt->rowCount();
                }
            } else {
                // 直接删除最旧的记录
                $stmt = $pdo->prepare("
                    DELETE FROM login_logs 
                    WHERE id IN (
                        SELECT id FROM login_logs 
                        ORDER BY login_time ASC 
                        LIMIT ?
                    )
                ");
                $stmt->execute([$excessCount]);
                $deletedByCount = $stmt->rowCount();
            }
            
            $deletedCount += $deletedByCount;
            
            if ($deletedByCount > 0) {
                $result['cleanup_methods'][] = "按数量清理: 删除了 {$deletedByCount} 条最旧的记录";
                echo "🗑️  按数量清理: 删除了 {$deletedByCount} 条记录\n";
            }
        }
        
        // 提交事务
        $pdo->commit();
        
        // 获取清理后的记录数
        $stmt = $pdo->query("SELECT COUNT(*) FROM login_logs");
        $finalCount = $stmt->fetchColumn();
        
        // 优化数据库
        $pdo->exec("VACUUM");
        
        $result['success'] = true;
        $result['deleted_count'] = $deletedCount;
        $result['remaining_count'] = $finalCount;
        
        echo "✅ 清理完成: 删除了 {$deletedCount} 条记录，剩余 {$finalCount} 条记录\n";
        
    } catch (Exception $e) {
        if (isset($pdo)) {
            $pdo->rollBack();
        }
        $result['errors'][] = $e->getMessage();
        echo "❌ 清理失败: " . $e->getMessage() . "\n";
    }
    
    return $result;
}

/**
 * 获取login_logs统计信息
 */
function getLoginLogsStats() {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stats = [];
        
        // 总记录数
        $stmt = $pdo->query("SELECT COUNT(*) FROM login_logs");
        $stats['total_records'] = $stmt->fetchColumn();
        
        // 按状态统计
        $stmt = $pdo->query("
            SELECT status, COUNT(*) as count 
            FROM login_logs 
            GROUP BY status 
            ORDER BY count DESC
        ");
        $stats['by_status'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 最新和最旧的记录时间
        $stmt = $pdo->query("SELECT MIN(login_time) as oldest, MAX(login_time) as newest FROM login_logs");
        $timeRange = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['oldest_record'] = $timeRange['oldest'];
        $stats['newest_record'] = $timeRange['newest'];
        
        // 最近7天的记录数
        $stmt = $pdo->query("
            SELECT COUNT(*) FROM login_logs 
            WHERE login_time >= datetime('now', '-7 days')
        ");
        $stats['last_7_days'] = $stmt->fetchColumn();
        
        // 最近30天的记录数
        $stmt = $pdo->query("
            SELECT COUNT(*) FROM login_logs 
            WHERE login_time >= datetime('now', '-30 days')
        ");
        $stats['last_30_days'] = $stmt->fetchColumn();
        
        // 数据库文件大小
        if (file_exists($dbPath)) {
            $stats['db_size'] = filesize($dbPath);
            $stats['db_size_mb'] = round($stats['db_size'] / 1024 / 1024, 2);
        }
        
        return $stats;
        
    } catch (Exception $e) {
        return ['error' => $e->getMessage()];
    }
}

/**
 * 创建自动清理任务
 */
function createCleanupTask($config) {
    $taskContent = "#!/bin/bash
# login_logs自动清理任务
# 每天凌晨2点执行

# 切换到项目目录
cd " . dirname(__FILE__) . "

# 执行清理
php login-logs-cleaner.php --auto

# 记录执行时间
echo \"$(date): login_logs清理任务执行完成\" >> cleanup.log
";
    
    file_put_contents('cleanup-task.sh', $taskContent);
    chmod('cleanup-task.sh', 0755);
    
    echo "📝 已创建自动清理任务脚本: cleanup-task.sh\n";
    echo "💡 添加到crontab的命令:\n";
    echo "   0 2 * * * " . dirname(__FILE__) . "/cleanup-task.sh\n";
}

// 主程序
if (php_sapi_name() === 'cli') {
    // 命令行模式
    echo "🧹 login_logs表清理工具\n";
    echo "========================\n";
    
    $autoMode = in_array('--auto', $argv);
    $statsOnly = in_array('--stats', $argv);
    $createTask = in_array('--create-task', $argv);
    
    if ($statsOnly) {
        // 只显示统计信息
        echo "📊 获取统计信息...\n";
        $stats = getLoginLogsStats();
        
        if (isset($stats['error'])) {
            echo "❌ 获取统计信息失败: " . $stats['error'] . "\n";
            exit(1);
        }
        
        echo "\n📋 login_logs表统计信息:\n";
        echo "总记录数: " . number_format($stats['total_records']) . "\n";
        echo "最旧记录: " . ($stats['oldest_record'] ?? '无') . "\n";
        echo "最新记录: " . ($stats['newest_record'] ?? '无') . "\n";
        echo "最近7天: " . number_format($stats['last_7_days']) . " 条\n";
        echo "最近30天: " . number_format($stats['last_30_days']) . " 条\n";
        echo "数据库大小: " . ($stats['db_size_mb'] ?? 0) . " MB\n";
        
        echo "\n按状态统计:\n";
        foreach ($stats['by_status'] as $status) {
            echo "  {$status['status']}: " . number_format($status['count']) . " 条\n";
        }
        
    } elseif ($createTask) {
        // 创建自动清理任务
        createCleanupTask($cleanupConfig);
        
    } else {
        // 执行清理
        if (!$autoMode) {
            echo "清理配置:\n";
            echo "  最大保留记录数: " . number_format($cleanupConfig['max_records']) . "\n";
            echo "  保留天数: {$cleanupConfig['keep_days']} 天\n";
            echo "  保留重要日志: " . ($cleanupConfig['keep_important_logs'] ? '是' : '否') . "\n";
            echo "\n";
        }
        
        $result = cleanupLoginLogs($cleanupConfig);
        
        if (!$result['success']) {
            echo "❌ 清理失败:\n";
            foreach ($result['errors'] as $error) {
                echo "  - $error\n";
            }
            exit(1);
        }
        
        if (!$autoMode) {
            echo "\n📋 清理结果:\n";
            foreach ($result['cleanup_methods'] as $method) {
                echo "  - $method\n";
            }
        }
    }
    
} else {
    // Web模式 - 返回JSON
    header('Content-Type: application/json; charset=utf-8');
    
    $action = $_GET['action'] ?? 'stats';
    
    if ($action === 'cleanup') {
        $result = cleanupLoginLogs($cleanupConfig);
        echo json_encode($result, JSON_UNESCAPED_UNICODE);
    } else {
        $stats = getLoginLogsStats();
        echo json_encode($stats, JSON_UNESCAPED_UNICODE);
    }
}
?>
