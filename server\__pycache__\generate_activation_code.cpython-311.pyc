�

    ��h�'  �                   �   � d Z ddlZddlZddlZddlmZmZ ddlZddlZddlZ G d� d�  �        Z	d� Z
edk    r e
�   �          dS dS )u�   
新用户认证系统 - 激活码生成脚本
生成30天有效期的激活码，格式参考主流商业软件
激活码格式: XXXXX-XXXXX-XXXXX-XXXXX (20位字符，4组，每组5位)
支持新的用户认证数据库结构
�    N)�datetime�	timedeltac                   �R   � e Zd Zdd�Zdd�Zd� Zd� Zd� Zdd	�Zdd�Z	d� Z
d
� Zd� ZdS )�ActivationCodeGeneratorNc                 �  � |�gt           j        �                    t           j        �                    t          �  �        �  �        }t           j        �                    |d�  �        | _        n|| _        t          d| j        � ��  �         t           j        �                    | j        �  �        st          d| j        � ��  �         d| _	        d S )N�user_system.db3u   🔍 使用数据库: u)   ⚠️ 警告: 数据库文件不存在: � ABCDEFGHJKLMNPQRSTUVWXYZ23456789)
�os�path�dirname�abspath�__file__�join�db_path�print�exists�charset)�selfr   �current_dirs      �Z   G:\temp_code_project\test_answer\test_倒计时\test\..\server\generate_activation_code.py�__init__z ActivationCodeGenerator.__init__   s�   � ��?��'�/�/�"�'�/�/�(�*C�*C�D�D�K��7�<�<��5F�G�G�D�L�L�"�D�L�
�5�t�|�5�5�6�6�6� �w�~�~�d�l�+�+� 	N��L�d�l�L�L�M�M�M� :�����    �   c                 �`   � � d�                     � fd�t          |�  �        D �   �         �  �        S )u0   生成激活码片段（使用安全随机数）� c              3   �J   �K  � | ]}t          j        �j        �  �        V � �d S �N)�secrets�choicer   ��.0�_r   s     �r   �	<genexpr>z@ActivationCodeGenerator.generate_code_segment.<locals>.<genexpr>'   s/   �� � � �K�K��w�~�d�l�3�3�K�K�K�K�K�Kr   )r   �range)r   �lengths   ` r   �generate_code_segmentz-ActivationCodeGenerator.generate_code_segment%   s0   �� ��w�w�K�K�K�K�U�6�]�]�K�K�K�K�K�Kr   c                 �d   � � � fd�t          d�  �        D �   �         }d�                    |�  �        S )u5   生成完整激活码 格式: XXXXX-XXXXX-XXXXX-XXXXXc                 �8   �� g | ]}��                     �   �         ��S � )r&   r    s     �r   �
<listcomp>zDActivationCodeGenerator.generate_activation_code.<locals>.<listcomp>+   s%   �� �C�C�C�Q�D�.�.�0�0�C�C�Cr   �   �-)r$   r   )r   �segmentss   ` r   �generate_activation_codez0ActivationCodeGenerator.generate_activation_code)   s4   �� �C�C�C�C�%��(�(�C�C�C���x�x��!�!�!r   c                 ��   � � t          |�  �        dk    rdS |�                    d�  �        }t          |�  �        dk    rdS |D ]6}t          |�  �        dk    r dS t          � fd�|D �   �         �  �        s dS �7dS )u   验证激活码格式�   Fr,   r+   r   c              3   �*   �K  � | ]
}|�j         v V � �d S r   )r   )r!   �cr   s     �r   r#   z?ActivationCodeGenerator.validate_code_format.<locals>.<genexpr>:   s*   �� � � �7�7�Q�q�D�L�(�7�7�7�7�7�7r   T)�len�split�all)r   �code�parts�parts   `   r   �validate_code_formatz,ActivationCodeGenerator.validate_code_format.   s�   �� ��t�9�9��?�?��5��
�
�3�����u�:�:��?�?��5�� 	� 	�D��4�y�y�A�~�~��u�u��7�7�7�7�$�7�7�7�7�7� 
��u�u�
� �tr   c                 ��   � t          j        | j        �  �        }|�                    �   �         }|�                    d|f�  �         |�                    �   �         d         dk    }|�                    �   �          |S )u   检查激活码是否已存在z4SELECT COUNT(*) FROM activation_codes WHERE code = ?r   )�sqlite3�connectr   �cursor�execute�fetchone�close)r   r6   �connr=   r   s        r   �code_existsz#ActivationCodeGenerator.code_exists?   sc   � ���t�|�,�,�����������M�PT�w�W�W�W����"�"�1�%��)���
�
�����
r   �   c                 �  � 	 | �                     �   �         }| �                    |�  �        sn�+t          j        �   �         }|t	          |��  �        z   }t          j        | j        �  �        }|�                    �   �         }	 |�	                    d||�
                    d�  �        f�  �         |�                    �   �          t          d�  �         t          d|� ��  �         t          d|�
                    d�  �        � ��  �         t          d|�
                    d�  �        � ��  �         t          d	|� d
��  �         ||||d�|�
                    �   �          S # t          $ rE}t          d|� ��  �         |�                    �   �          Y d
}~|�
                    �   �          d
S d
}~ww xY w# |�
                    �   �          w xY w)u*   创建新的激活码并保存到数据库T)�daysz}
                INSERT INTO activation_codes (code, expires_at, status)
                VALUES (?, ?, 'active')
            z%Y-%m-%d %H:%M:%Su   ✅ 激活码生成成功:u      激活码: u      创建时间: u      过期时间: u      有效期: u    天)r6   �
created_at�
expires_at�
valid_daysu   ❌ 激活码保存失败: N)r.   rB   r   �nowr   r;   r<   r   r=   r>   �strftime�commitr   r@   �	Exception�rollback)r   rH   r6   rF   rG   rA   r=   �es           r   �create_activation_codez.ActivationCodeGenerator.create_activation_codeJ   s�  � �	��0�0�2�2�D��#�#�D�)�)� 
��	� �\�^�^�
��)��"<�"<�"<�<�
� ��t�|�,�,��������	��N�N� � �
�+�+�,?�@�@�A�
C� 
C� 
C�
 
�K�K�M�M�M��/�0�0�0��)�4�)�)�*�*�*��P�j�&9�&9�:M�&N�&N�P�P�Q�Q�Q��P�j�&9�&9�:M�&N�&N�P�P�Q�Q�Q��3�:�3�3�3�4�4�4� �(�(�(�	� � 
�J�J�L�L�L�L��
 � 	� 	� 	��3��3�3�4�4�4��M�M�O�O�O��4�4�4� 
�J�J�L�L�L�L�L�����
	����� 
�J�J�L�L�L�L���s+   �CE �
F(�#&F#�	F+ �#F(�(F+ �+G�   c                 �  � t          d|� d��  �         g }t          |�  �        D ]G}t          d|dz   � d|� d��  �         | �                    |�  �        }|r|�                    |�  �         �Ht          dt	          |�  �        � d��  �         |S )	u   批量生成激活码u   🔄 开始批量生成 u    个激活码...u   
📝 生成第 rP   �/u    个激活码:u'   
✅ 批量生成完成，成功生成 u
    个激活码)r   r$   rO   �appendr3   )r   �countrH   �codes�i�results         r   �batch_generatez&ActivationCodeGenerator.batch_generatew   s�   � �
�@��@�@�@�A�A�A����u��� 	%� 	%�A��A�a��c�A�A�E�A�A�A�B�B�B��0�0��<�<�F�� 
%����V�$�$�$��
�R��U���R�R�R�S�S�S��r   c           
      �2  � t          j        | j        �  �        }|�                    �   �         }|�                    d�  �         |�                    �   �         }|�                    �   �          |r�t          dt          |�  �        � d��  �         t          d�  �         t          dd�ddd	�dd
d	�ddd�dd
d��	�  �         t          d�  �         |D ]7\  }}}}}}	|rdnd}
|	r|	nd}t          |d�d|d	�d|d	�d|
d�d|d��	�  �         �8nt          d�  �         |S )u   列出所有有效的激活码a6  
            SELECT ac.code, ac.created_at, ac.expires_at, ac.is_used, ac.status, u.username
            FROM activation_codes ac
            LEFT JOIN users u ON ac.bound_user_id = u.id
            WHERE ac.status = 'active' AND ac.expires_at > datetime('now')
            ORDER BY ac.created_at DESC
        u   
📋 当前有效激活码 (u    个):zZ------------------------------------------------------------------------------------------�	   激活码�<25� u   创建时间�<20�   过期时间u   状态z<10u   绑定用户�<15u	   已使用u	   未使用�	   未绑定u   📭 暂无有效激活码�	r;   r<   r   r=   r>   �fetchallr@   r   r3   )r   rA   r=   rU   r6   �created�expires�used�status�username�used_status�
bound_users               r   �list_active_codesz)ActivationCodeGenerator.list_active_codes�   sy  � ���t�|�,�,����������� � 	
� 	
� 	
� ���!�!���
�
����� 	0��E�3�u�:�:�E�E�E�F�F�F��(�O�O�O��[�t�t�t�~�t�t�t�N�t�t�t�QY�t�t�t�`n�t�t�t�u�u�u��(�O�O�O�BG� 
d� 
d�>��g�w��f�h�-1�B�k�k�{��)1�B�X�X�{�
���b�b�b�G�b�b�b�'�b�b�b��b�b�b�R\�b�b�b�c�c�c�c�
d�
 
�.�/�/�/��r   c           
      �B  � t          j        | j        �  �        }|�                    �   �         }|�                    d�  �         |�                    �   �         }|�                    �   �          |r�t          dt          |�  �        � d��  �         t          d�  �         t          dd�ddd	�dd
d�ddd	�dd
d��	�  �         t          d�  �         |D ]?\  }}}}}}	|r|nd}
|r
|dd�         nd}t          |d�d|d	�d|d�d|
d	�d|d��	�  �         �@nt          d�  �         |S )u*   列出所有用户及其绑定的激活码z�
            SELECT u.username, u.email, u.created_at, ac.code, ac.expires_at, ac.status
            FROM users u
            LEFT JOIN activation_codes ac ON ac.bound_user_id = u.id
            ORDER BY u.created_at DESC
        u&   
👥 用户及激活码绑定情况 (u    条记录):zd----------------------------------------------------------------------------------------------------u	   用户名r_   r\   u   邮箱r[   u   注册时间r]   rZ   r^   r`   N�
   r,   u   📭 暂无用户数据ra   )r   rA   r=   �usersrg   �emailrc   r6   rd   rf   �code_display�expires_displays               r   �list_users_with_codesz-ActivationCodeGenerator.list_users_with_codes�   s�  � ���t�|�,�,����������� � 	
� 	
� 	
� ���!�!���
�
����� 	-��T�C��J�J�T�T�T�U�U�U��)�����[�q�q�q�x�q�q�q�n�q�q�q�;�q�q�q�]k�q�q�q�r�r�r��)����CH� 
l� 
l�?��%��$���'+�<�t�t���29�"B�'�#�2�#�,�,�s����j�j�j��j�j�j�G�j�j�j�,�j�j�j�Ud�j�j�j�k�k�k�k�
l�
 
�+�,�,�,��r   c                 �@  � t          j        | j        �  �        }|�                    �   �         }i }|�                    d�  �         |�                    �   �         d         |d<   |�                    d�  �         |�                    �   �         d         |d<   |�                    d�  �         |�                    �   �         d         |d<   |�                    d�  �         |�                    �   �         d         |d	<   |�                    d
�  �         |�                    �   �         d         |d<   |�                    d�  �         |�                    �   �         d         |d
<   |�                    �   �          t          d�  �         t          d�  �         t          d|d         � ��  �         t          d|d         � ��  �         t          d|d         � ��  �         t          d|d	         � ��  �         t          d|d         � ��  �         t          d|d
         � ��  �         |S )u   获取数据库统计信息zSELECT COUNT(*) FROM usersr   �total_usersz%SELECT COUNT(*) FROM activation_codes�total_codesz^SELECT COUNT(*) FROM activation_codes WHERE status = 'active' AND expires_at > datetime('now')�active_codesz7SELECT COUNT(*) FROM activation_codes WHERE is_used = 1�
used_codeszISELECT COUNT(*) FROM activation_codes WHERE expires_at <= datetime('now')�
expired_codesz6SELECT COUNT(*) FROM user_sessions WHERE is_active = 1�active_sessionsu   
📊 数据库统计信息:z(----------------------------------------u   总用户数: u   总激活码数: u   有效激活码: u   已使用激活码: u   已过期激活码: u   活跃会话数: )r;   r<   r   r=   r>   r?   r@   r   )r   rA   r=   �statss       r   �get_database_statsz*ActivationCodeGenerator.get_database_stats�   s  � ���t�|�,�,�������� �� 	���3�4�4�4�%���0�0��3��m�� 	���>�?�?�?�%���0�0��3��m�����w�x�x�x� &��� 1� 1�!� 4��n�����P�Q�Q�Q�$�o�o�/�/��2��l�����b�c�c�c�!'���!2�!2�1�!5��o�� 	���O�P�P�P�#)�?�?�#4�#4�Q�#7��� ��
�
����
�.�/�/�/�
�h����
�5�u�]�3�5�5�6�6�6�
�8�%�
�"6�8�8�9�9�9�
�9�%��"7�9�9�:�:�:�
�:�U�<�%8�:�:�;�;�;�
�=�U�?�%;�=�=�>�>�>�
�<�%�(9�":�<�<�=�=�=��r   r   )r   )rC   )rP   rC   )
�__name__�
__module__�__qualname__r   r&   r.   r9   rB   rO   rX   rj   rq   rz   r)   r   r   r   r      s�   � � � � � �:� :� :� :�$L� L� L� L�"� "� "�
� � �"	� 	� 	�+� +� +� +�Z� � � �� � �>� � �<(� (� (� (� (r   r   c                  ��  � t           j        �                    t           j        �                    t          �  �        �  �        } t           j        �                    | d�  �        }t           j        �                    |�  �        s2t          d�  �         t          d�  �         t          d|� ��  �         dS t          |�  �        }t          d�  �         t          d�  �         	 t          d	�  �         t          d
�  �         t          d�  �         t          d�  �         t          d
�  �         t          d�  �         t          d�  �         t          d�  �        �
                    �   �         }|dk    r]t          d�  �        �
                    �   �         }|�                    �   �         rt          |�  �        nd}|�
                    |�  �         �n-|dk    r�t          d�  �        �
                    �   �         }|�                    �   �         st          d�  �         ��:t          d�  �        �
                    �   �         }|�                    �   �         rt          |�  �        nd}|�                    t          |�  �        |�  �         nw|dk    r|�                    �   �          n\|dk    r|�                    �   �          nA|dk    r|�                    �   �          n&|dk    rt          d�  �         dS t          d�  �         ��)u	   主函数r   uF   ❌ 数据库不存在，请先运行 create_db.php 初始化数据库u      命令: php create_db.phpu      期望路径: Nu/   🔐 新用户认证系统 - 激活码生成器z<============================================================Tu   
请选择操作:u   1. 生成单个激活码u   2. 批量生成激活码u   3. 查看有效激活码u   4. 查看用户绑定情况u   5. 数据库统计信息u	   6. 退出u   
请输入选项 (1-6): �1u%   请输入有效天数 (默认30天): rC   �2u   请输入生成数量: u   ❌ 请输入有效数字�3�4�5�6u   👋 再见!u"   ❌ 无效选项，请重新选择)r
   r   r   r
   r   r   r   r   r   �input�strip�isdigit�intrO   rX   rj   rq   rz   )r   r   �	generatorr   rE   rT   s         r   �mainr�   �   s�  � � �'�/�/�"�'�/�/�(�";�";�<�<�K��g�l�l�;�(9�:�:�G� �7�>�>�'�"�"� �
�V�W�W�W�
�,�-�-�-�
�+�'�+�+�,�,�,���'��0�0�I�	�
;�<�<�<�	�(�O�O�O�)8�
�"�#�#�#�
�(�)�)�)�
�(�)�)�)�
�(�)�)�)�
�+�,�,�,�
�(�)�)�)�
�k�����2�3�3�9�9�;�;���S�=�=��@�A�A�G�G�I�I�D� $�����6�3�t�9�9�9�B�D��,�,�T�2�2�2�2�
�s�]�]��3�4�4�:�:�<�<�E��=�=�?�?� 
��1�2�2�2���@�A�A�G�G�I�I�D� $�����6�3�t�9�9�9�B�D��$�$�S��Z�Z��6�6�6�6�
�s�]�]��'�'�)�)�)�)�
�s�]�]��+�+�-�-�-�-�
�s�]�]��(�(�*�*�*�*�
�s�]�]��.�!�!�!��E� 
�6�7�7�7�S)8r   �__main__)�__doc__r;   �random�stringr   r   �hashlibr
   r   r   r�   r{   r)   r   r   �<module>r�      s�   ��� � ���� 
�
�
�
� 
�
�
�
� (� (� (� (� (� (� (� (� ���� 	�	�	�	� ����X� X� X� X� X� X� X� X�t;8� ;8� ;8�z �z����D�F�F�F�F�F� �r   