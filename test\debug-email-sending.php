<?php
/**
 * 邮件发送详细调试工具
 * 用于诊断SMTP邮件发送问题
 */

header('Content-Type: text/html; charset=utf-8');

// 处理测试请求
$testResult = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_email'])) {
    $testEmail = $_POST['test_email'] ?? '';
    $testSubject = $_POST['test_subject'] ?? '测试邮件';
    $testBody = $_POST['test_body'] ?? '这是一封测试邮件，发送时间：' . date('Y-m-d H:i:s');
    
    $testResult = testEmailSending($testEmail, $testSubject, $testBody);
}

function testEmailSending($to, $subject, $body) {
    $result = [
        'start_time' => microtime(true),
        'steps' => [],
        'success' => false,
        'error' => null
    ];
    
    try {
        // 步骤1: 加载配置
        $result['steps'][] = ['step' => '加载邮件配置', 'status' => 'start', 'time' => microtime(true)];
        require_once '../server/email-config.php';
        $config = getEmailConfig();
        $result['steps'][] = ['step' => '加载邮件配置', 'status' => 'success', 'time' => microtime(true), 'data' => [
            'provider' => $GLOBALS['emailConfig']['provider'] ?? 'unknown',
            'smtp_host' => $config['smtp_host'],
            'smtp_port' => $config['smtp_port'],
            'smtp_secure' => $config['smtp_secure'],
            'from_email' => $config['from_email']
        ]];
        
        // 步骤2: 测试SMTP连接
        $result['steps'][] = ['step' => '测试SMTP连接', 'status' => 'start', 'time' => microtime(true)];
        $socket = testSMTPConnection($config);
        $result['steps'][] = ['step' => '测试SMTP连接', 'status' => 'success', 'time' => microtime(true)];
        
        // 步骤3: 测试SMTP认证
        $result['steps'][] = ['step' => '测试SMTP认证', 'status' => 'start', 'time' => microtime(true)];
        $authResult = testSMTPAuth($socket, $config);
        $result['steps'][] = ['step' => '测试SMTP认证', 'status' => 'success', 'time' => microtime(true), 'data' => $authResult];
        
        // 步骤4: 发送测试邮件
        $result['steps'][] = ['step' => '发送测试邮件', 'status' => 'start', 'time' => microtime(true)];
        require_once '../server/email-sender.php';
        $emailSender = new EmailSender();
        $sendResult = $emailSender->sendEmail($to, $subject, $body, true);
        $result['steps'][] = ['step' => '发送测试邮件', 'status' => $sendResult['success'] ? 'success' : 'error', 'time' => microtime(true), 'data' => $sendResult];
        
        $result['success'] = $sendResult['success'];
        if (!$sendResult['success']) {
            $result['error'] = $sendResult['message'];
        }
        
    } catch (Exception $e) {
        $result['error'] = $e->getMessage();
        $result['steps'][] = ['step' => '异常', 'status' => 'error', 'time' => microtime(true), 'error' => $e->getMessage()];
    }
    
    $result['end_time'] = microtime(true);
    $result['total_time'] = $result['end_time'] - $result['start_time'];
    
    return $result;
}

function testSMTPConnection($config) {
    $host = $config['smtp_host'];
    $port = $config['smtp_port'];
    $secure = $config['smtp_secure'];
    
    $context = stream_context_create([
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false,
            'allow_self_signed' => true
        ]
    ]);
    
    if ($secure === 'ssl') {
        $host = "ssl://$host";
    }
    
    $socket = stream_socket_client("$host:$port", $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context);
    
    if (!$socket) {
        throw new Exception("SMTP连接失败: $errstr ($errno)");
    }
    
    // 读取服务器欢迎消息
    $response = fgets($socket);
    if (substr($response, 0, 3) !== '220') {
        fclose($socket);
        throw new Exception("SMTP服务器响应异常: $response");
    }
    
    return $socket;
}

function testSMTPAuth($socket, $config) {
    // 发送EHLO命令
    fwrite($socket, "EHLO localhost\r\n");
    $response = '';
    while ($line = fgets($socket)) {
        $response .= $line;
        if (substr($line, 3, 1) === ' ') break;
    }
    
    if (substr($response, 0, 3) !== '250') {
        fclose($socket);
        throw new Exception("EHLO命令失败: $response");
    }
    
    // 如果是TLS，启动TLS
    if ($config['smtp_secure'] === 'tls') {
        fwrite($socket, "STARTTLS\r\n");
        $response = fgets($socket);
        if (substr($response, 0, 3) !== '220') {
            fclose($socket);
            throw new Exception("STARTTLS失败: $response");
        }
        
        if (!stream_socket_enable_crypto($socket, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
            fclose($socket);
            throw new Exception("TLS加密启动失败");
        }
        
        // 重新发送EHLO
        fwrite($socket, "EHLO localhost\r\n");
        $response = '';
        while ($line = fgets($socket)) {
            $response .= $line;
            if (substr($line, 3, 1) === ' ') break;
        }
    }
    
    // 开始认证
    fwrite($socket, "AUTH LOGIN\r\n");
    $response = fgets($socket);
    if (substr($response, 0, 3) !== '334') {
        fclose($socket);
        throw new Exception("AUTH LOGIN失败: $response");
    }
    
    // 发送用户名
    fwrite($socket, base64_encode($config['username']) . "\r\n");
    $response = fgets($socket);
    if (substr($response, 0, 3) !== '334') {
        fclose($socket);
        throw new Exception("用户名认证失败: $response");
    }
    
    // 发送密码
    fwrite($socket, base64_encode($config['password']) . "\r\n");
    $response = fgets($socket);
    if (substr($response, 0, 3) !== '235') {
        fclose($socket);
        throw new Exception("密码认证失败: $response");
    }
    
    fclose($socket);
    return ['auth_success' => true, 'message' => 'SMTP认证成功'];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件发送调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
        input, textarea, button { padding: 8px; margin: 5px; }
        button { background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
        .step { margin: 10px 0; padding: 10px; border-left: 4px solid #ddd; }
        .step.success { border-left-color: #4CAF50; background: #f0f8f0; }
        .step.error { border-left-color: #f44336; background: #fdf0f0; }
        .step.start { border-left-color: #2196F3; background: #f0f7ff; }
    </style>
</head>
<body>
    <h1>📧 邮件发送调试工具</h1>
    <p><strong>调试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="test-section">
        <h2>📊 当前邮件配置</h2>
        <?php
        try {
            require_once '../server/email-config.php';
            $config = getEmailConfig();
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>配置项</th><th>值</th></tr>";
            echo "<tr><td>服务商</td><td>" . ($GLOBALS['emailConfig']['provider'] ?? 'unknown') . "</td></tr>";
            echo "<tr><td>SMTP主机</td><td>" . $config['smtp_host'] . "</td></tr>";
            echo "<tr><td>SMTP端口</td><td>" . $config['smtp_port'] . "</td></tr>";
            echo "<tr><td>加密方式</td><td>" . $config['smtp_secure'] . "</td></tr>";
            echo "<tr><td>用户名</td><td>" . $config['username'] . "</td></tr>";
            echo "<tr><td>密码</td><td>" . str_repeat('*', strlen($config['password'])) . "</td></tr>";
            echo "<tr><td>发件人</td><td>" . $config['from_email'] . "</td></tr>";
            echo "</table>";
        } catch (Exception $e) {
            echo "<p class='error'>❌ 配置加载失败: " . $e->getMessage() . "</p>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>🧪 邮件发送测试</h2>
        <form method="POST">
            <div>
                <label>收件人邮箱:</label><br>
                <input type="email" name="test_email" value="<?php echo $_POST['test_email'] ?? '<EMAIL>'; ?>" required style="width: 300px;">
                <small>建议先测试发件人自己的邮箱</small>
            </div>
            <div>
                <label>邮件主题:</label><br>
                <input type="text" name="test_subject" value="<?php echo $_POST['test_subject'] ?? '密码重置测试邮件'; ?>" required style="width: 300px;">
            </div>
            <div>
                <label>邮件内容:</label><br>
                <textarea name="test_body" rows="5" style="width: 300px;"><?php echo $_POST['test_body'] ?? '这是一封测试邮件，用于验证SMTP邮件发送功能。\n\n发送时间：' . date('Y-m-d H:i:s'); ?></textarea>
            </div>
            <div>
                <button type="submit" name="test_email">发送测试邮件</button>
            </div>
        </form>
    </div>
    
    <?php if ($testResult): ?>
    <div class="test-section">
        <h2>📋 测试结果</h2>
        
        <div class="step <?php echo $testResult['success'] ? 'success' : 'error'; ?>">
            <h3><?php echo $testResult['success'] ? '✅ 测试成功' : '❌ 测试失败'; ?></h3>
            <p><strong>总耗时:</strong> <?php echo number_format($testResult['total_time'], 2); ?> 秒</p>
            <?php if ($testResult['error']): ?>
                <p><strong>错误信息:</strong> <?php echo htmlspecialchars($testResult['error']); ?></p>
            <?php endif; ?>
        </div>
        
        <h3>详细步骤:</h3>
        <?php foreach ($testResult['steps'] as $step): ?>
            <div class="step <?php echo $step['status']; ?>">
                <h4><?php echo $step['step']; ?> - <?php echo ucfirst($step['status']); ?></h4>
                <?php if (isset($step['data'])): ?>
                    <pre><?php echo htmlspecialchars(json_encode($step['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
                <?php endif; ?>
                <?php if (isset($step['error'])): ?>
                    <p class="error">错误: <?php echo htmlspecialchars($step['error']); ?></p>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>
    
    <div class="test-section">
        <h2>🔍 常见问题排查</h2>
        
        <h3>1. 网络连接问题</h3>
        <p>测试是否能连接到163邮箱SMTP服务器:</p>
        <pre>telnet smtp.163.com 465</pre>
        
        <h3>2. 防火墙问题</h3>
        <p>确保以下端口未被防火墙阻止:</p>
        <ul>
            <li>端口 465 (SSL)</li>
            <li>端口 587 (TLS)</li>
        </ul>
        
        <h3>3. 163邮箱授权码</h3>
        <p>确认163邮箱授权码设置:</p>
        <ul>
            <li>登录163邮箱</li>
            <li>设置 → POP3/SMTP/IMAP</li>
            <li>开启SMTP服务</li>
            <li>获取授权码(不是登录密码)</li>
        </ul>
        
        <h3>4. 邮件被拦截</h3>
        <p>检查以下位置:</p>
        <ul>
            <li>垃圾邮件文件夹</li>
            <li>已删除邮件</li>
            <li>邮件过滤规则</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关链接</h2>
        <ul>
            <li><a href="test-forgot-password-fixed.php">测试密码找回功能</a></li>
            <li><a href="error-log-test.php">PHP错误日志测试</a></li>
            <li><a href="../new-lock.html">返回登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
