<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码找回测试 - 简化版</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="email"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus {
            border-color: #4CAF50;
            outline: none;
        }
        .captcha-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .captcha-input {
            flex: 1;
        }
        .captcha-img {
            height: 44px;
            border: 2px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
        }
        .btn {
            width: 100%;
            padding: 15px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 10px;
        }
        .btn:hover {
            background: #45a049;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .message {
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .debug {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .info {
            background: #e3f2fd;
            color: #0d47a1;
            border: 1px solid #bbdefb;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 密码找回测试</h1>
        
        <div class="info">
            <strong>📋 测试说明：</strong><br>
            • 用户名：ataehee1<br>
            • 邮箱：<EMAIL><br>
            • 验证码：点击图片刷新
        </div>

        <form id="forgotPasswordForm">
            <div class="form-group">
                <label for="username">用户名：</label>
                <input type="text" id="username" name="username" value="ataehee1" required>
            </div>

            <div class="form-group">
                <label for="email">邮箱地址：</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>

            <div class="form-group">
                <label for="captcha">验证码：</label>
                <div class="captcha-group">
                    <input type="text" id="captcha" name="captcha" class="captcha-input" placeholder="请输入验证码" required>
                    <img id="captchaImg" class="captcha-img" src="../api/captcha.php" alt="验证码" onclick="refreshCaptcha()">
                </div>
            </div>

            <button type="submit" class="btn" id="submitBtn">发送重置邮件</button>
        </form>

        <div id="message" class="message"></div>
        <div id="debug" class="debug" style="display: none;"></div>
    </div>

    <script>
        // 刷新验证码
        function refreshCaptcha() {
            const img = document.getElementById('captchaImg');
            img.src = '../api/captcha.php?' + new Date().getTime();
        }

        // 显示消息
        function showMessage(text, type = 'info') {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';
        }

        // 显示调试信息
        function showDebug(data) {
            const debugDiv = document.getElementById('debug');
            debugDiv.textContent = JSON.stringify(data, null, 2);
            debugDiv.style.display = 'block';
        }

        // 表单提交处理
        document.getElementById('forgotPasswordForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.textContent;
            
            // 禁用按钮
            submitBtn.disabled = true;
            submitBtn.textContent = '发送中...';
            
            // 隐藏之前的消息
            document.getElementById('message').style.display = 'none';
            document.getElementById('debug').style.display = 'none';
            
            try {
                // 获取表单数据
                const formData = new FormData();
                formData.append('username', document.getElementById('username').value);
                formData.append('email', document.getElementById('email').value);
                formData.append('captcha', document.getElementById('captcha').value);
                
                console.log('发送请求到:', '../api/forgot-password.php');
                console.log('表单数据:', {
                    username: document.getElementById('username').value,
                    email: document.getElementById('email').value,
                    captcha: document.getElementById('captcha').value
                });
                
                // 发送请求
                const response = await fetch('../api/forgot-password.php', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('响应状态:', response.status);
                console.log('响应头:', response.headers);
                
                const responseText = await response.text();
                console.log('响应原始内容:', responseText);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON解析错误:', parseError);
                    showMessage('服务器响应格式错误', 'error');
                    showDebug({
                        error: 'JSON解析失败',
                        responseText: responseText,
                        parseError: parseError.message
                    });
                    return;
                }
                
                console.log('解析后的数据:', data);
                showDebug(data);
                
                if (data.success) {
                    showMessage('✅ ' + data.message, 'success');
                    // 清空验证码
                    document.getElementById('captcha').value = '';
                    refreshCaptcha();
                } else {
                    showMessage('❌ ' + data.message, 'error');
                    // 刷新验证码
                    refreshCaptcha();
                }
                
            } catch (error) {
                console.error('请求错误:', error);
                showMessage('网络错误：' + error.message, 'error');
                showDebug({
                    error: '网络请求失败',
                    message: error.message,
                    stack: error.stack
                });
            } finally {
                // 恢复按钮
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            }
        });

        // 页面加载完成后刷新验证码
        document.addEventListener('DOMContentLoaded', function() {
            refreshCaptcha();
        });
    </script>
</body>
</html>
