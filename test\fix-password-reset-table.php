<?php
/**
 * 修复密码重置表结构
 */

echo "🔧 修复密码重置表结构...\n\n";

try {
    // 连接数据库
    $db = new PDO('sqlite:../server/user_system.db3');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "1. 检查当前表结构:\n";
    echo "==================\n";
    
    // 检查表是否存在
    $stmt = $db->query("SELECT name FROM sqlite_master WHERE type='table' AND name='password_resets'");
    $tableExists = $stmt->fetch();
    
    if ($tableExists) {
        echo "✅ password_resets 表存在\n";
        
        // 检查表结构
        $stmt = $db->query("PRAGMA table_info(password_resets)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "当前字段:\n";
        foreach ($columns as $column) {
            echo "   - " . $column['name'] . " (" . $column['type'] . ")\n";
        }
        
        // 检查是否有reset_token字段
        $hasResetToken = false;
        foreach ($columns as $column) {
            if ($column['name'] === 'reset_token') {
                $hasResetToken = true;
                break;
            }
        }
        
        if (!$hasResetToken) {
            echo "\n❌ 缺少 reset_token 字段，需要重建表\n";
            
            // 备份现有数据
            echo "\n2. 备份现有数据:\n";
            echo "==================\n";
            $stmt = $db->query("SELECT * FROM password_resets");
            $existingData = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "备份了 " . count($existingData) . " 条记录\n";
            
            // 删除旧表
            $db->exec("DROP TABLE password_resets");
            echo "✅ 删除旧表\n";
        } else {
            echo "✅ reset_token 字段存在\n";
        }
    } else {
        echo "❌ password_resets 表不存在\n";
    }
    
    echo "\n3. 创建新表结构:\n";
    echo "==================\n";
    
    // 创建新的密码重置表
    $sql = "
    CREATE TABLE IF NOT EXISTS password_resets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        username VARCHAR(50),
        email VARCHAR(100) NOT NULL,
        reset_token VARCHAR(255) NOT NULL UNIQUE,
        expires_at DATETIME NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_used BOOLEAN DEFAULT 0,
        used_at DATETIME NULL
    )";
    
    $db->exec($sql);
    echo "✅ 创建新表结构\n";
    
    // 创建索引
    $db->exec("CREATE INDEX IF NOT EXISTS idx_password_resets_token ON password_resets(reset_token)");
    $db->exec("CREATE INDEX IF NOT EXISTS idx_password_resets_email ON password_resets(email)");
    $db->exec("CREATE INDEX IF NOT EXISTS idx_password_resets_expires ON password_resets(expires_at)");
    echo "✅ 创建索引\n";
    
    echo "\n4. 验证新表结构:\n";
    echo "==================\n";
    
    $stmt = $db->query("PRAGMA table_info(password_resets)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        echo "   ✅ " . $column['name'] . " (" . $column['type'] . ")\n";
    }
    
    echo "\n🎉 密码重置表结构修复完成！\n";
    
} catch (Exception $e) {
    echo "❌ 修复失败: " . $e->getMessage() . "\n";
    exit(1);
}
?>
