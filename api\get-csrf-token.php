<?php
/**
 * 获取CSRF令牌API
 */

session_start();
header('Content-Type: application/json; charset=utf-8');

require_once 'csrf-protection.php';

try {
    $token = getCSRFToken();
    
    echo json_encode([
        'success' => true,
        'csrf_token' => $token
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '获取CSRF令牌失败',
        'error' => $e->getMessage()
    ]);
}
?>
