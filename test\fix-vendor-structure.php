<?php
/**
 * 修复vendor目录结构
 * 完整复制PHPMailer的vendor结构，解决路径问题
 */

header('Content-Type: text/html; charset=utf-8');

$fixResult = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_vendor'])) {
    $fixResult = fixVendorStructure();
}

function fixVendorStructure() {
    $result = [
        'success' => false,
        'copied_files' => [],
        'errors' => [],
        'total_files' => 0,
        'directories_created' => []
    ];
    
    try {
        $sourceBase = '../../test_email_system/vendor';
        $targetBase = '../vendor';
        
        // 检查源目录
        if (!is_dir($sourceBase)) {
            throw new Exception("源vendor目录不存在: $sourceBase");
        }
        
        // 清理并重新创建目标目录
        if (is_dir($targetBase)) {
            removeDirectory($targetBase);
        }
        mkdir($targetBase, 0755, true);
        $result['directories_created'][] = $targetBase;
        
        // 递归复制整个vendor目录
        $copyResult = copyDirectoryRecursive($sourceBase, $targetBase);
        $result['copied_files'] = $copyResult['files'];
        $result['total_files'] = $copyResult['count'];
        $result['errors'] = $copyResult['errors'];
        $result['directories_created'] = array_merge($result['directories_created'], $copyResult['directories']);
        
        $result['success'] = empty($result['errors']);
        
    } catch (Exception $e) {
        $result['errors'][] = "异常: " . $e->getMessage();
    }
    
    return $result;
}

function removeDirectory($dir) {
    if (!is_dir($dir)) {
        return;
    }
    
    $files = array_diff(scandir($dir), array('.', '..'));
    foreach ($files as $file) {
        $path = $dir . DIRECTORY_SEPARATOR . $file;
        if (is_dir($path)) {
            removeDirectory($path);
        } else {
            unlink($path);
        }
    }
    rmdir($dir);
}

function copyDirectoryRecursive($source, $target) {
    $result = ['files' => [], 'count' => 0, 'errors' => [], 'directories' => []];
    
    if (!is_dir($target)) {
        mkdir($target, 0755, true);
        $result['directories'][] = $target;
    }
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::SELF_FIRST
    );
    
    foreach ($iterator as $item) {
        $targetPath = $target . DIRECTORY_SEPARATOR . $iterator->getSubPathName();
        
        if ($item->isDir()) {
            if (!is_dir($targetPath)) {
                mkdir($targetPath, 0755, true);
                $result['directories'][] = $targetPath;
            }
        } else {
            $targetDir = dirname($targetPath);
            if (!is_dir($targetDir)) {
                mkdir($targetDir, 0755, true);
                $result['directories'][] = $targetDir;
            }
            
            if (copy($item, $targetPath)) {
                $result['files'][] = $iterator->getSubPathName();
                $result['count']++;
            } else {
                $result['errors'][] = "复制失败: " . $item . " -> " . $targetPath;
            }
        }
    }
    
    return $result;
}

function checkVendorStructure() {
    $vendorBase = '../vendor';
    $requiredPaths = [
        'autoload.php',
        'composer/autoload_real.php',
        'composer/autoload_static.php',
        'composer/ClassLoader.php',
        'composer/autoload_psr4.php',
        'composer/autoload_classmap.php',
        'composer/autoload_namespaces.php',
        'phpmailer/phpmailer/src/PHPMailer.php'
    ];
    
    $status = [];
    foreach ($requiredPaths as $path) {
        $fullPath = $vendorBase . '/' . $path;
        $status[$path] = file_exists($fullPath);
    }
    
    return $status;
}

$vendorStatus = checkVendorStructure();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复vendor目录结构</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; max-height: 300px; overflow-y: auto; }
        button { padding: 8px 16px; margin: 5px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
        .highlight { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>🔧 修复vendor目录结构</h1>
    <p><strong>修复时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="highlight">
        <h3>⚠️ 问题分析</h3>
        <p><strong>错误信息:</strong> <code>require_once(D:\www\test_daojishi\vendor/composer/autoload_real.php): failed to open stream</code></p>
        <p><strong>问题原因:</strong> vendor/composer目录结构不完整，缺少必要的Composer文件</p>
        <p><strong>解决方案:</strong> 完整复制test_email_system的vendor目录结构</p>
    </div>
    
    <div class="test-section">
        <h2>📊 当前vendor目录状态</h2>
        <table>
            <tr>
                <th>必需文件/目录</th>
                <th>状态</th>
            </tr>
            <?php foreach ($vendorStatus as $path => $exists): ?>
                <tr>
                    <td><?php echo htmlspecialchars($path); ?></td>
                    <td>
                        <?php if ($exists): ?>
                            <span class="success">✅ 存在</span>
                        <?php else: ?>
                            <span class="error">❌ 缺失</span>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endforeach; ?>
        </table>
        
        <?php
        $missingCount = count(array_filter($vendorStatus, function($exists) { return !$exists; }));
        if ($missingCount > 0):
        ?>
            <p class="error">❌ 发现 <?php echo $missingCount; ?> 个缺失的文件/目录</p>
        <?php else: ?>
            <p class="success">✅ 所有必需文件都存在</p>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>🔧 修复vendor结构</h2>
        <?php if (empty($fixResult)): ?>
            <p>点击下面的按钮完整重建vendor目录结构：</p>
            <form method="POST">
                <button type="submit" name="fix_vendor">修复vendor目录结构</button>
            </form>
            <p class="warning">⚠️ 注意：此操作会删除现有的vendor目录并重新创建</p>
        <?php else: ?>
            <h3>修复结果:</h3>
            
            <?php if ($fixResult['success']): ?>
                <div class="success">
                    <h4>✅ vendor目录修复成功!</h4>
                    <p><strong>复制的文件:</strong> <?php echo $fixResult['total_files']; ?> 个</p>
                    <p><strong>创建的目录:</strong> <?php echo count($fixResult['directories_created']); ?> 个</p>
                </div>
            <?php else: ?>
                <div class="error">
                    <h4>❌ 修复过程中出现错误</h4>
                    <p><strong>复制的文件:</strong> <?php echo count($fixResult['copied_files']); ?> 个</p>
                    <p><strong>错误数量:</strong> <?php echo count($fixResult['errors']); ?> 个</p>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($fixResult['errors'])): ?>
                <h4>错误信息:</h4>
                <pre class="error"><?php echo implode("\n", $fixResult['errors']); ?></pre>
            <?php endif; ?>
            
            <?php if ($fixResult['success']): ?>
                <h4>创建的主要目录:</h4>
                <ul>
                    <?php
                    $mainDirs = array_filter($fixResult['directories_created'], function($dir) {
                        return substr_count($dir, DIRECTORY_SEPARATOR) <= 2;
                    });
                    foreach (array_slice($mainDirs, 0, 10) as $dir):
                    ?>
                        <li><?php echo htmlspecialchars($dir); ?></li>
                    <?php endforeach; ?>
                    <?php if (count($fixResult['directories_created']) > 10): ?>
                        <li>... 还有 <?php echo count($fixResult['directories_created']) - 10; ?> 个目录</li>
                    <?php endif; ?>
                </ul>
            <?php endif; ?>
            
            <form method="POST">
                <button type="submit" name="fix_vendor">重新修复</button>
            </form>
        <?php endif; ?>
    </div>
    
    <?php if (!empty($fixResult) && $fixResult['success']): ?>
    <div class="test-section">
        <h2>✅ 下一步测试</h2>
        <p>vendor目录修复成功！现在可以测试PHPMailer功能：</p>
        <ol>
            <li><strong>验证结构:</strong> <a href="test-local-phpmailer.php">测试本地PHPMailer</a></li>
            <li><strong>测试邮件:</strong> <a href="test-working-email-sender.php">测试邮件发送器</a></li>
            <li><strong>测试密码找回:</strong> <a href="../new-lock.html">登录页面测试</a></li>
        </ol>
    </div>
    <?php endif; ?>
    
    <div class="test-section">
        <h2>📋 技术说明</h2>
        
        <h3>Windows路径问题:</h3>
        <p>原错误显示路径混合了正反斜杠：</p>
        <code>D:\www\test_daojishi\vendor/composer/autoload_real.php</code>
        
        <h3>解决方案:</h3>
        <ul>
            <li>完整复制test_email_system的vendor目录</li>
            <li>确保所有Composer文件都存在</li>
            <li>保持原始的目录结构和文件权限</li>
        </ul>
        
        <h3>复制的关键文件:</h3>
        <ul>
            <li><strong>composer/autoload_real.php</strong> - Composer自动加载核心</li>
            <li><strong>composer/autoload_static.php</strong> - 静态自动加载映射</li>
            <li><strong>composer/ClassLoader.php</strong> - 类加载器</li>
            <li><strong>phpmailer/</strong> - PHPMailer库文件</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关链接</h2>
        <ul>
            <li><a href="test-local-phpmailer.php">测试本地PHPMailer</a></li>
            <li><a href="final-verification.php">最终验证</a></li>
            <li><a href="test-working-email-sender.php">测试邮件发送器</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
