<?php
/**
 * 全项目PHPMailer路径引用更新工具
 * 搜索并更新整个项目中所有的test_email_system/vendor/autoload.php引用
 */

header('Content-Type: text/html; charset=utf-8');

$searchResult = null;
$updateResult = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['search_references'])) {
        $searchResult = searchAllReferences();
    } elseif (isset($_POST['update_all_references'])) {
        $updateResult = updateAllReferences();
    }
}

function searchAllReferences() {
    $result = [
        'found_files' => [],
        'total_references' => 0,
        'search_patterns' => [
            'test_email_system/vendor/autoload.php',
            '../../test_email_system/vendor/autoload.php',
            '../test_email_system/vendor/autoload.php',
            'test_email_system\\vendor\\autoload.php',
            '..\\..\\test_email_system\\vendor\\autoload.php',
            '..\\test_email_system\\vendor\\autoload.php'
        ]
    ];
    
    try {
        $projectRoot = dirname(__DIR__);
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($projectRoot, RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && preg_match('/\.(php|html|js|css|json|md)$/i', $file->getFilename())) {
                $filePath = $file->getPathname();
                $relativePath = str_replace($projectRoot . DIRECTORY_SEPARATOR, '', $filePath);
                
                // 跳过vendor目录和一些不需要检查的目录
                if (strpos($relativePath, 'vendor' . DIRECTORY_SEPARATOR) === 0 ||
                    strpos($relativePath, '.git' . DIRECTORY_SEPARATOR) === 0 ||
                    strpos($relativePath, 'node_modules' . DIRECTORY_SEPARATOR) === 0) {
                    continue;
                }
                
                $content = file_get_contents($filePath);
                $foundPatterns = [];
                
                foreach ($result['search_patterns'] as $pattern) {
                    if (strpos($content, $pattern) !== false) {
                        $foundPatterns[] = $pattern;
                        $result['total_references']++;
                    }
                }
                
                if (!empty($foundPatterns)) {
                    $result['found_files'][] = [
                        'file' => $relativePath,
                        'absolute_path' => $filePath,
                        'patterns' => $foundPatterns,
                        'size' => filesize($filePath)
                    ];
                }
            }
        }
        
    } catch (Exception $e) {
        $result['error'] = $e->getMessage();
    }
    
    return $result;
}

function updateAllReferences() {
    $result = [
        'success' => false,
        'updated_files' => [],
        'errors' => [],
        'total_changes' => 0
    ];
    
    try {
        // 首先搜索所有引用
        $searchResult = searchAllReferences();
        
        if (empty($searchResult['found_files'])) {
            $result['message'] = '没有找到需要更新的文件';
            $result['success'] = true;
            return $result;
        }
        
        foreach ($searchResult['found_files'] as $fileInfo) {
            $filePath = $fileInfo['absolute_path'];
            $relativePath = $fileInfo['file'];
            
            // 备份原文件
            $backupPath = $filePath . '.backup.' . date('Y-m-d-H-i-s');
            if (!copy($filePath, $backupPath)) {
                $result['errors'][] = "无法备份文件: $relativePath";
                continue;
            }
            
            $content = file_get_contents($filePath);
            $originalContent = $content;
            $fileChanges = 0;
            
            // 根据文件位置确定正确的相对路径
            $depth = substr_count($relativePath, DIRECTORY_SEPARATOR);
            $correctPath = str_repeat('../', $depth) . 'vendor/autoload.php';
            
            // 替换所有可能的模式
            $patterns = [
                'test_email_system/vendor/autoload.php',
                '../../test_email_system/vendor/autoload.php',
                '../test_email_system/vendor/autoload.php',
                '../../../test_email_system/vendor/autoload.php',
                'test_email_system\\vendor\\autoload.php',
                '..\\..\\test_email_system\\vendor\\autoload.php',
                '..\\test_email_system\\vendor\\autoload.php',
                '..\\..\\..\\test_email_system\\vendor\\autoload.php'
            ];
            
            foreach ($patterns as $pattern) {
                $newContent = str_replace($pattern, $correctPath, $content);
                if ($newContent !== $content) {
                    $content = $newContent;
                    $fileChanges++;
                }
            }
            
            // 如果有变化，写回文件
            if ($content !== $originalContent) {
                if (file_put_contents($filePath, $content)) {
                    $result['updated_files'][] = [
                        'file' => $relativePath,
                        'changes' => $fileChanges,
                        'new_path' => $correctPath,
                        'backup' => basename($backupPath)
                    ];
                    $result['total_changes'] += $fileChanges;
                } else {
                    $result['errors'][] = "无法写入文件: $relativePath";
                    // 恢复备份
                    copy($backupPath, $filePath);
                }
            } else {
                // 删除不必要的备份
                unlink($backupPath);
            }
        }
        
        $result['success'] = empty($result['errors']);
        
    } catch (Exception $e) {
        $result['errors'][] = "异常: " . $e->getMessage();
    }
    
    return $result;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全项目PHPMailer路径更新</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; max-height: 400px; overflow-y: auto; }
        button { padding: 8px 16px; margin: 5px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
        .highlight { background: #ffffcc; padding: 15px; border-left: 4px solid #ffeb3b; margin: 15px 0; }
        .file-item { background: #f9f9f9; padding: 10px; margin: 5px 0; border-left: 3px solid #2196F3; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .pattern { font-family: monospace; background: #e8e8e8; padding: 2px 4px; }
    </style>
</head>
<body>
    <h1>🔍 全项目PHPMailer路径更新工具</h1>
    <p><strong>操作时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="highlight">
        <h3>🎯 工具功能</h3>
        <p><strong>搜索范围:</strong> 整个项目目录（排除vendor、.git、node_modules）</p>
        <p><strong>文件类型:</strong> .php, .html, .js, .css, .json, .md</p>
        <p><strong>搜索模式:</strong> 所有可能的test_email_system/vendor/autoload.php引用</p>
        <p><strong>更新策略:</strong> 根据文件位置自动计算正确的相对路径</p>
    </div>
    
    <div class="test-section">
        <h2>🔍 第一步：搜索所有引用</h2>
        <form method="POST">
            <p>首先搜索项目中所有的PHPMailer外部引用：</p>
            <button type="submit" name="search_references">搜索所有引用</button>
        </form>
        
        <?php if ($searchResult): ?>
            <h3>搜索结果:</h3>
            
            <?php if (isset($searchResult['error'])): ?>
                <p class="error">❌ 搜索失败: <?php echo htmlspecialchars($searchResult['error']); ?></p>
            <?php else: ?>
                <p><strong>找到的文件:</strong> <?php echo count($searchResult['found_files']); ?> 个</p>
                <p><strong>总引用数:</strong> <?php echo $searchResult['total_references']; ?> 处</p>
                
                <?php if (!empty($searchResult['found_files'])): ?>
                    <h4>包含引用的文件:</h4>
                    <table>
                        <tr>
                            <th>文件路径</th>
                            <th>文件大小</th>
                            <th>找到的模式</th>
                        </tr>
                        <?php foreach ($searchResult['found_files'] as $fileInfo): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($fileInfo['file']); ?></td>
                                <td><?php echo number_format($fileInfo['size']); ?> 字节</td>
                                <td>
                                    <?php foreach ($fileInfo['patterns'] as $pattern): ?>
                                        <span class="pattern"><?php echo htmlspecialchars($pattern); ?></span><br>
                                    <?php endforeach; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                    
                    <div class="highlight">
                        <h4>⚠️ 准备更新</h4>
                        <p>找到了需要更新的文件。点击下面的按钮开始批量更新：</p>
                        <form method="POST">
                            <button type="submit" name="update_all_references" style="background: #ff9800;">批量更新所有引用</button>
                        </form>
                    </div>
                <?php else: ?>
                    <p class="success">✅ 没有找到需要更新的引用，所有路径都是正确的！</p>
                <?php endif; ?>
            <?php endif; ?>
        <?php endif; ?>
    </div>
    
    <?php if ($updateResult): ?>
    <div class="test-section">
        <h2>🔧 第二步：批量更新结果</h2>
        
        <?php if ($updateResult['success']): ?>
            <div class="success">
                <h4>✅ 批量更新成功!</h4>
                <p><strong>更新的文件:</strong> <?php echo count($updateResult['updated_files']); ?> 个</p>
                <p><strong>总变更数:</strong> <?php echo $updateResult['total_changes']; ?> 处</p>
            </div>
        <?php else: ?>
            <div class="error">
                <h4>❌ 更新过程中出现错误</h4>
                <p><strong>成功更新:</strong> <?php echo count($updateResult['updated_files']); ?> 个文件</p>
                <p><strong>错误数量:</strong> <?php echo count($updateResult['errors']); ?> 个</p>
            </div>
        <?php endif; ?>
        
        <?php if (isset($updateResult['message'])): ?>
            <p class="info"><?php echo $updateResult['message']; ?></p>
        <?php endif; ?>
        
        <?php if (!empty($updateResult['updated_files'])): ?>
            <h4>更新详情:</h4>
            <table>
                <tr>
                    <th>文件路径</th>
                    <th>变更数</th>
                    <th>新路径</th>
                    <th>备份文件</th>
                </tr>
                <?php foreach ($updateResult['updated_files'] as $fileInfo): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($fileInfo['file']); ?></td>
                        <td><?php echo $fileInfo['changes']; ?></td>
                        <td><span class="pattern"><?php echo htmlspecialchars($fileInfo['new_path']); ?></span></td>
                        <td><?php echo htmlspecialchars($fileInfo['backup']); ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        <?php endif; ?>
        
        <?php if (!empty($updateResult['errors'])): ?>
            <h4>错误信息:</h4>
            <pre class="error"><?php echo implode("\n", $updateResult['errors']); ?></pre>
        <?php endif; ?>
    </div>
    <?php endif; ?>
    
    <div class="test-section">
        <h2>📋 更新说明</h2>
        
        <h3>搜索的模式:</h3>
        <ul>
            <li><span class="pattern">test_email_system/vendor/autoload.php</span></li>
            <li><span class="pattern">../../test_email_system/vendor/autoload.php</span></li>
            <li><span class="pattern">../test_email_system/vendor/autoload.php</span></li>
            <li><span class="pattern">test_email_system\vendor\autoload.php</span> (Windows路径)</li>
            <li>以及其他可能的变体</li>
        </ul>
        
        <h3>更新策略:</h3>
        <ul>
            <li>根据文件在项目中的位置自动计算正确的相对路径</li>
            <li>统一更新为 <span class="pattern">vendor/autoload.php</span> 的相对路径</li>
            <li>自动创建备份文件（.backup.时间戳）</li>
            <li>如果更新失败，自动恢复备份</li>
        </ul>
        
        <h3>安全措施:</h3>
        <ul>
            <li>✅ 更新前自动备份原文件</li>
            <li>✅ 跳过vendor、.git等系统目录</li>
            <li>✅ 只处理代码相关文件类型</li>
            <li>✅ 更新失败时自动恢复</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关链接</h2>
        <ul>
            <li><a href="copy-phpmailer.php">复制PHPMailer文件</a></li>
            <li><a href="test-local-phpmailer.php">测试本地PHPMailer</a></li>
            <li><a href="test-working-email-sender.php">测试工作的邮件发送器</a></li>
            <li><a href="../new-lock.html">返回登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
