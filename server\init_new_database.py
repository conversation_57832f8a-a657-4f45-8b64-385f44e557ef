#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新用户登录系统数据库初始化脚本
使用SQLite3创建.db3格式数据库
"""

import sqlite3
import os
import hashlib
import secrets
from datetime import datetime, timedelta

def create_database():
    """创建新的数据库"""
    db_path = 'user_system.db3'
    
    # 删除旧数据库
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f"已删除旧数据库: {db_path}")
    
    # 创建新数据库连接
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 读取并执行SQL脚本
    with open('new_database_schema.sql', 'r', encoding='utf-8') as f:
        sql_script = f.read()
    
    # 执行SQL脚本
    cursor.executescript(sql_script)
    
    print(f"✅ 数据库创建成功: {db_path}")
    return conn

def generate_activation_codes(conn, count=10):
    """生成测试激活码"""
    cursor = conn.cursor()
    
    def generate_code():
        """生成激活码"""
        charset = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'
        segments = []
        for _ in range(4):
            segment = ''.join(secrets.choice(charset) for _ in range(5))
            segments.append(segment)
        return '-'.join(segments)
    
    codes = []
    for _ in range(count):
        code = generate_code()
        expires_at = datetime.now() + timedelta(days=30)
        
        cursor.execute("""
            INSERT INTO activation_codes (code, expires_at, status)
            VALUES (?, ?, 'active')
        """, (code, expires_at.strftime('%Y-%m-%d %H:%M:%S')))
        
        codes.append(code)
    
    conn.commit()
    print(f"✅ 生成了 {count} 个激活码")
    return codes

def create_test_user(conn):
    """创建测试用户"""
    cursor = conn.cursor()
    
    # 创建测试用户
    username = 'testuser'
    password = 'test123'
    password_hash = hashlib.sha256(password.encode()).hexdigest()
    
    try:
        cursor.execute("""
            INSERT INTO users (username, password_hash, email, status)
            VALUES (?, ?, ?, 'active')
        """, (username, password_hash, '<EMAIL>'))
        
        conn.commit()
        print(f"✅ 创建测试用户: {username} / {password}")
        return True
    except sqlite3.IntegrityError:
        print(f"⚠️ 测试用户已存在: {username}")
        return False

def verify_database(conn):
    """验证数据库结构"""
    cursor = conn.cursor()
    
    # 检查表是否存在
    tables = [
        'users', 'activation_codes', 'user_sessions', 
        'login_logs', 'captcha_codes', 'payment_records', 'system_config'
    ]
    
    print("\n📋 数据库表检查:")
    for table in tables:
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
        if cursor.fetchone():
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"  ✅ {table}: {count} 条记录")
        else:
            print(f"  ❌ {table}: 表不存在")
    
    # 检查激活码
    cursor.execute("SELECT code FROM activation_codes WHERE status='active' LIMIT 3")
    codes = cursor.fetchall()
    if codes:
        print(f"\n🔑 示例激活码:")
        for code in codes:
            print(f"  {code[0]}")

def main():
    """主函数"""
    print("🚀 开始初始化新用户登录系统数据库...")
    
    try:
        # 创建数据库
        conn = create_database()
        
        # 生成激活码
        codes = generate_activation_codes(conn, 10)
        
        # 创建测试用户
        create_test_user(conn)
        
        # 验证数据库
        verify_database(conn)
        
        print(f"\n🎉 数据库初始化完成!")
        print(f"数据库文件: user_system.db3")
        print(f"测试用户: testuser / test123")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
