<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全屏显示测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html {
            width: 100%;
            height: 100%;
            min-width: 100vw;
            min-height: 100vh;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            background: linear-gradient(135deg, #ff4757 0%, #ff3838 25%, #ff6b7a 50%, #ff4757 75%, #ff2f3a 100%);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .test-content {
            text-align: center;
            color: white;
            z-index: 100;
        }
        
        .test-title {
            font-size: 4vw;
            margin-bottom: 2vh;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .test-info {
            font-size: 2vw;
            margin-bottom: 1vh;
        }
        
        .debug-info {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
            z-index: 200;
        }
    </style>
</head>
<body>
    <div class="debug-info" id="debugInfo">
        加载中...
    </div>
    
    <div class="test-content">
        <h1 class="test-title">全屏显示测试</h1>
        <div class="test-info">如果您能看到这个红色背景占满整个屏幕，说明显示正常</div>
        <div class="test-info">如果只看到一个小的红色区域，说明存在显示问题</div>
    </div>

    <script>
        function updateDebugInfo() {
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.innerHTML = `
                视口尺寸: ${window.innerWidth} x ${window.innerHeight}<br>
                屏幕尺寸: ${screen.width} x ${screen.height}<br>
                设备像素比: ${window.devicePixelRatio}<br>
                Body尺寸: ${document.body.offsetWidth} x ${document.body.offsetHeight}<br>
                HTML尺寸: ${document.documentElement.offsetWidth} x ${document.documentElement.offsetHeight}<br>
                缩放级别: ${Math.round(window.devicePixelRatio * 100)}%<br>
                用户代理: ${navigator.userAgent.substring(0, 50)}...
            `;
        }
        
        // 页面加载完成后更新调试信息
        window.addEventListener('load', updateDebugInfo);
        window.addEventListener('resize', updateDebugInfo);
        
        // 立即更新一次
        updateDebugInfo();
    </script>
</body>
</html>
