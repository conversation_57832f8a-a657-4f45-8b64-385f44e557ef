-- 新的用户登录系统数据库结构设计
-- SQLite3 数据库 (.db3格式)

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,          -- 用户名 (唯一)
    password_hash VARCHAR(255) NOT NULL,           -- 密码哈希
    email VARCHAR(100),                            -- 邮箱 (可选)
    qq_number VARCHAR(20),                         -- QQ号 (可选)
    preferred_brand VARCHAR(20) DEFAULT 'wuling',  -- 用户偏好品牌
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 注册时间
    status VARCHAR(20) DEFAULT 'active',           -- 状态: active, disabled, banned
    last_login DATETIME,                           -- 最后登录时间
    login_count INTEGER DEFAULT 0                  -- 登录次数
);

-- 激活码表
CREATE TABLE IF NOT EXISTS activation_codes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code VARCHAR(25) NOT NULL UNIQUE,              -- 激活码 (格式: XXXXX-XXXXX-XXXXX-XXXXX)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    expires_at DATETIME NOT NULL,                  -- 过期时间 (30天有效期)
    is_used BOOLEAN DEFAULT FALSE,                 -- 是否已使用
    used_at DATETIME,                              -- 使用时间
    bound_user_id INTEGER,                         -- 绑定的用户ID
    status VARCHAR(20) DEFAULT 'active',           -- 状态: active, used, expired
    FOREIGN KEY (bound_user_id) REFERENCES users(id)
);

-- 用户会话表 (管理登录状态)
CREATE TABLE IF NOT EXISTS user_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,                      -- 用户ID
    session_token VARCHAR(255) NOT NULL UNIQUE,    -- 会话令牌
    device_info TEXT,                              -- 设备信息
    ip_address VARCHAR(45),                        -- IP地址
    user_agent TEXT,                               -- 浏览器用户代理
    current_brand VARCHAR(20) DEFAULT 'wuling',    -- 当前访问的品牌
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    last_access DATETIME DEFAULT CURRENT_TIMESTAMP,-- 最后访问时间
    expires_at DATETIME NOT NULL,                  -- 过期时间
    is_active BOOLEAN DEFAULT TRUE,                -- 是否活跃
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 登录日志表
CREATE TABLE IF NOT EXISTS login_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,                               -- 用户ID (可为空，登录失败时)
    username VARCHAR(50),                          -- 尝试登录的用户名
    ip_address VARCHAR(45),                        -- IP地址
    user_agent TEXT,                               -- 浏览器用户代理
    login_time DATETIME DEFAULT CURRENT_TIMESTAMP, -- 登录时间
    status VARCHAR(20),                            -- 状态: success, failed, blocked
    failure_reason VARCHAR(100),                   -- 失败原因
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 验证码表 (图形验证码)
CREATE TABLE IF NOT EXISTS captcha_codes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code_hash VARCHAR(255) NOT NULL,               -- 验证码哈希
    session_id VARCHAR(255) NOT NULL,              -- 会话ID
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    expires_at DATETIME NOT NULL,                  -- 过期时间 (5分钟)
    is_used BOOLEAN DEFAULT FALSE                  -- 是否已使用
);

-- 支付记录表
CREATE TABLE IF NOT EXISTS payment_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,                               -- 用户ID
    order_id VARCHAR(50) UNIQUE,                   -- 订单号
    amount DECIMAL(10,2) DEFAULT 19.90,            -- 付款金额
    payment_method VARCHAR(20) DEFAULT 'alipay',   -- 支付方式
    payment_time DATETIME,                         -- 付款时间
    activation_code VARCHAR(25),                   -- 分配的激活码
    status VARCHAR(20) DEFAULT 'pending',          -- 状态: pending, confirmed, issued
    notes TEXT,                                    -- 备注
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 品牌访问日志表
CREATE TABLE IF NOT EXISTS brand_access_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,                               -- 用户ID (可为空，未登录访问)
    brand_path VARCHAR(20) NOT NULL,               -- 品牌路径标识
    brand_name VARCHAR(50),                        -- 品牌名称
    page_type VARCHAR(20),                         -- 页面类型: standard, tv
    ip_address VARCHAR(45),                        -- IP地址
    user_agent TEXT,                               -- 浏览器用户代理
    request_uri TEXT,                              -- 请求URI
    access_time DATETIME DEFAULT CURRENT_TIMESTAMP,-- 访问时间
    session_id VARCHAR(255),                       -- 会话ID
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE,       -- 配置键
    config_value TEXT,                             -- 配置值
    description TEXT,                              -- 描述
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_brand ON users(preferred_brand);
CREATE INDEX IF NOT EXISTS idx_activation_codes_code ON activation_codes(code);
CREATE INDEX IF NOT EXISTS idx_activation_codes_status ON activation_codes(status);
CREATE INDEX IF NOT EXISTS idx_activation_codes_user ON activation_codes(bound_user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_sessions_user ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_active ON user_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_sessions_brand ON user_sessions(current_brand);
CREATE INDEX IF NOT EXISTS idx_login_logs_user ON login_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_login_logs_time ON login_logs(login_time);
CREATE INDEX IF NOT EXISTS idx_captcha_session ON captcha_codes(session_id);
CREATE INDEX IF NOT EXISTS idx_payment_user ON payment_records(user_id);
CREATE INDEX IF NOT EXISTS idx_brand_access_brand ON brand_access_logs(brand_path);
CREATE INDEX IF NOT EXISTS idx_brand_access_time ON brand_access_logs(access_time);
CREATE INDEX IF NOT EXISTS idx_brand_access_user ON brand_access_logs(user_id);

-- 插入默认系统配置
INSERT OR IGNORE INTO system_config (config_key, config_value, description) VALUES
('session_timeout', '7776000', '会话超时时间(秒) - 90天，适合商家广告长期展示'),
('captcha_timeout', '300', '验证码超时时间(秒) - 5分钟'),
('activation_code_validity', '2592000', '激活码有效期(秒) - 30天'),
('max_login_attempts', '5', '最大登录尝试次数'),
('login_block_time', '1800', '登录失败锁定时间(秒) - 30分钟');
