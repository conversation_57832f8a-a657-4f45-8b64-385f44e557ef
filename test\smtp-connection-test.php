<?php
/**
 * SMTP连接测试脚本
 * 用于快速测试SMTP服务器连接
 */

echo "SMTP连接测试开始...\n";

// 163邮箱配置
$configs = [
    '163' => [
        'host' => 'smtp.163.com',
        'port' => 465,
        'secure' => 'ssl'
    ],
    'qq' => [
        'host' => 'smtp.qq.com',
        'port' => 587,
        'secure' => 'tls'
    ],
    'gmail' => [
        'host' => 'smtp.gmail.com',
        'port' => 587,
        'secure' => 'tls'
    ]
];

foreach ($configs as $name => $config) {
    echo "\n测试 $name 邮箱 ({$config['host']}:{$config['port']})...\n";
    
    $start = microtime(true);
    
    try {
        $context = stream_context_create([
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            ]
        ]);
        
        $host = $config['host'];
        if ($config['secure'] === 'ssl') {
            $host = "ssl://$host";
        }
        
        $socket = stream_socket_client("$host:{$config['port']}", $errno, $errstr, 10, STREAM_CLIENT_CONNECT, $context);
        
        if ($socket) {
            $response = fgets($socket);
            fclose($socket);
            
            $time = number_format((microtime(true) - $start) * 1000, 2);
            echo "✅ 连接成功 ({$time}ms)\n";
            echo "   服务器响应: " . trim($response) . "\n";
        } else {
            $time = number_format((microtime(true) - $start) * 1000, 2);
            echo "❌ 连接失败 ({$time}ms)\n";
            echo "   错误: $errstr ($errno)\n";
        }
        
    } catch (Exception $e) {
        $time = number_format((microtime(true) - $start) * 1000, 2);
        echo "❌ 异常 ({$time}ms)\n";
        echo "   错误: " . $e->getMessage() . "\n";
    }
}

echo "\n测试完成。\n";
echo "\n建议:\n";
echo "1. 如果所有连接都失败，检查网络和防火墙设置\n";
echo "2. 如果某个服务商连接成功，可以切换到该服务商\n";
echo "3. 如果连接成功但认证失败，检查邮箱授权码\n";
?>
