<?php
/**
 * 简单的PHPMailer测试
 * 验证vendor修复是否成功
 */

echo "<h1>PHPMailer测试</h1>\n";
echo "<p>测试时间: " . date('Y-m-d H:i:s') . "</p>\n";

try {
    echo "<h2>步骤1: 检查vendor目录</h2>\n";
    $vendorPath = 'vendor/autoload.php';
    if (!file_exists($vendorPath)) {
        throw new Exception("vendor/autoload.php不存在");
    }
    echo "<p style='color: green;'>✅ vendor/autoload.php存在</p>\n";
    
    echo "<h2>步骤2: 加载PHPMailer</h2>\n";
    require_once $vendorPath;
    echo "<p style='color: green;'>✅ autoload.php加载成功</p>\n";
    
    echo "<h2>步骤3: 检查PHPMailer类</h2>\n";
    if (!class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
        throw new Exception("PHPMailer类不存在");
    }
    echo "<p style='color: green;'>✅ PHPMailer类存在</p>\n";
    
    echo "<h2>步骤4: 创建PHPMailer实例</h2>\n";
    $mail = new PHPMailer\PHPMailer\PHPMailer(true);
    echo "<p style='color: green;'>✅ PHPMailer实例创建成功</p>\n";
    echo "<p>版本: " . $mail::VERSION . "</p>\n";
    
    echo "<h2>步骤5: 测试SMTP配置</h2>\n";
    $mail->isSMTP();
    $mail->Host = 'smtp.163.com';
    $mail->SMTPAuth = true;
    $mail->Username = '<EMAIL>';
    $mail->Password = 'BJXfKJw32HgDSMSg';
    $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
    $mail->Port = 465;
    $mail->CharSet = 'UTF-8';
    echo "<p style='color: green;'>✅ SMTP配置成功</p>\n";
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0;'>";
    echo "<h3>🎉 vendor修复成功！</h3>";
    echo "<p>PHPMailer可以正常加载和使用，Windows路径问题已解决。</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 错误: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>文件: " . $e->getFile() . "</p>\n";
    echo "<p>行号: " . $e->getLine() . "</p>\n";
}

echo "<h2>下一步测试</h2>\n";
echo "<p>现在可以测试密码找回功能了！</p>\n";
?>
