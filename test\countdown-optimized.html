<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化版本倒计时</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #ff4444 0%, #cc0000 50%, #990000 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
        }
        
        .title {
            font-size: 24px;
            margin-bottom: 20px;
            text-align: center;
            /* 移除动画，减少GPU负载 */
        }
        
        .countdown-container {
            display: flex;
            gap: 15px;
            margin: 20px 0;
        }
        
        .countdown-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            min-width: 80px;
            /* 优化：使用will-change提示浏览器 */
            will-change: contents;
        }
        
        .countdown-number {
            font-size: 36px;
            font-weight: bold;
            color: #ffff00;
            /* 简化阴影，减少GPU负载 */
            text-shadow: 
                2px 2px 4px rgba(0, 0, 0, 0.7),
                0 0 15px #ffff00;
            /* 移除发光动画 */
        }
        
        .countdown-label {
            font-size: 12px;
            color: #ffff00;
            margin-top: 5px;
            font-weight: bold;
        }
        
        .performance-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            color: #66ff66;
        }
        
        /* 优化：减少重绘和回流 */
        .countdown-number,
        .countdown-label {
            transform: translateZ(0);
            backface-visibility: hidden;
        }
    </style>
</head>
<body>
    <div class="performance-info">
        优化版本: 50ms更新 (20fps)<br>
        低GPU负载 | 智能DOM更新
    </div>
    
    <div class="title">优化版本倒计时 - 智能更新</div>
    
    <div class="countdown-container">
        <div class="countdown-item">
            <span class="countdown-number" id="days">00</span>
            <span class="countdown-label">天</span>
        </div>
        <div class="countdown-item">
            <span class="countdown-number" id="hours">00</span>
            <span class="countdown-label">小时</span>
        </div>
        <div class="countdown-item">
            <span class="countdown-number" id="minutes">00</span>
            <span class="countdown-label">分钟</span>
        </div>
        <div class="countdown-item">
            <span class="countdown-number" id="seconds">00</span>
            <span class="countdown-label">秒</span>
        </div>
        <div class="countdown-item">
            <span class="countdown-number" id="milliseconds">000</span>
            <span class="countdown-label">毫秒</span>
        </div>
    </div>

    <script>
        // 优化版本倒计时 - 智能更新系统
        class OptimizedCountdown {
            constructor(targetDate) {
                this.targetDate = new Date(targetDate).getTime();
                this.elements = {
                    days: document.getElementById('days'),
                    hours: document.getElementById('hours'),
                    minutes: document.getElementById('minutes'),
                    seconds: document.getElementById('seconds'),
                    milliseconds: document.getElementById('milliseconds')
                };
                
                // 缓存上次的值，避免不必要的DOM更新
                this.lastValues = {
                    days: '',
                    hours: '',
                    minutes: '',
                    seconds: '',
                    milliseconds: ''
                };
                
                // 性能优化：预计算填充字符串
                this.padCache = {};
                for (let i = 0; i < 1000; i++) {
                    this.padCache[i] = {
                        '2': i.toString().padStart(2, '0'),
                        '3': i.toString().padStart(3, '0')
                    };
                }
                
                this.init();
            }
            
            init() {
                this.isRunning = true;
                this.lastUpdateTime = 0;
                this.updateInterval = 50; // 50ms更新一次，降低频率
                
                this.update();
            }
            
            update() {
                if (!this.isRunning) return;
                
                const currentTime = performance.now();
                
                // 限制更新频率
                if (currentTime - this.lastUpdateTime >= this.updateInterval) {
                    this.updateCountdown();
                    this.lastUpdateTime = currentTime;
                }
                
                requestAnimationFrame(() => this.update());
            }
            
            updateCountdown() {
                const now = Date.now();
                const distance = this.targetDate - now;
                
                if (distance < 0) {
                    this.setFinalValues();
                    return;
                }
                
                // 批量计算所有值
                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);
                const milliseconds = Math.floor(distance % 1000);
                
                // 使用缓存的填充字符串
                const newValues = {
                    days: this.padCache[Math.min(days, 999)]?.['2'] || days.toString().padStart(2, '0'),
                    hours: this.padCache[hours]?.['2'] || hours.toString().padStart(2, '0'),
                    minutes: this.padCache[minutes]?.['2'] || minutes.toString().padStart(2, '0'),
                    seconds: this.padCache[seconds]?.['2'] || seconds.toString().padStart(2, '0'),
                    milliseconds: this.padCache[milliseconds]?.['3'] || milliseconds.toString().padStart(3, '0')
                };
                
                // 只更新发生变化的元素
                this.updateChangedElements(newValues);
            }
            
            updateChangedElements(newValues) {
                for (const [key, newValue] of Object.entries(newValues)) {
                    if (this.lastValues[key] !== newValue) {
                        this.elements[key].textContent = newValue;
                        this.lastValues[key] = newValue;
                    }
                }
            }
            
            setFinalValues() {
                const finalValues = {
                    days: '00',
                    hours: '00',
                    minutes: '00',
                    seconds: '00',
                    milliseconds: '000'
                };
                
                this.updateChangedElements(finalValues);
                this.isRunning = false;
            }
        }
        
        // 页面可见性API优化
        let countdown;
        
        function initCountdown() {
            countdown = new OptimizedCountdown('2025-08-01 00:00:00');
        }
        
        // 页面可见性变化处理
        document.addEventListener('visibilitychange', function() {
            if (countdown) {
                if (document.hidden) {
                    countdown.isRunning = false;
                } else {
                    countdown.isRunning = true;
                    countdown.update();
                }
            }
        });
        
        // 初始化
        initCountdown();
        
        console.log('🟢 优化版本倒计时启动 - 50ms更新间隔，智能DOM更新');
    </script>
</body>
</html>
