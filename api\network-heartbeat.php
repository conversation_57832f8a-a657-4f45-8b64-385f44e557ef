<?php
/**
 * 网络心跳检测API
 * 用于检测客户端网络状态，防止断网绕过单设备登录
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit();
}

// 数据库配置
$dbPath = '../server/user_system.db3';

/**
 * 验证会话令牌并更新心跳时间
 */
function updateHeartbeat($sessionToken, $clientInfo) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // 随机触发session自动清理（3%概率，心跳频繁所以概率低一些）
        require_once '../server/session-auto-cleaner.php';
        randomSessionCleanup($dbPath, 3);

        // 验证会话并更新心跳时间
        $stmt = $pdo->prepare("
            UPDATE user_sessions 
            SET last_access = datetime('now'),
                ip_address = ?,
                user_agent = ?
            WHERE session_token = ? 
            AND is_active = 1 
            AND expires_at > datetime('now')
        ");
        
        $stmt->execute([
            $clientInfo['ip_address'] ?? 'unknown',
            $clientInfo['user_agent'] ?? 'unknown',
            $sessionToken
        ]);
        
        if ($stmt->rowCount() > 0) {
            // 获取会话信息
            $stmt = $pdo->prepare("
                SELECT us.user_id, us.created_at, u.username
                FROM user_sessions us
                JOIN users u ON us.user_id = u.id
                WHERE us.session_token = ? AND us.is_active = 1
            ");
            $stmt->execute([$sessionToken]);
            $sessionInfo = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return [
                'valid' => true,
                'user_id' => $sessionInfo['user_id'],
                'username' => $sessionInfo['username'],
                'session_created' => $sessionInfo['created_at']
            ];
        } else {
            return ['valid' => false, 'reason' => '会话无效或已过期'];
        }
        
    } catch (Exception $e) {
        error_log("心跳更新失败: " . $e->getMessage());
        return ['valid' => false, 'reason' => '服务器错误'];
    }
}

/**
 * 检查是否有其他活跃会话
 */
function checkOtherActiveSessions($sessionToken, $userId) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count
            FROM user_sessions 
            WHERE user_id = ? 
            AND session_token != ? 
            AND is_active = 1 
            AND expires_at > datetime('now')
            AND last_access > datetime('now', '-2 minutes')
        ");
        $stmt->execute([$userId, $sessionToken]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'] > 0;
        
    } catch (Exception $e) {
        error_log("检查其他会话失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 记录网络状态日志
 */
function logNetworkStatus($sessionToken, $status, $details = null) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("
            INSERT INTO login_logs (username, ip_address, user_agent, status, failure_reason)
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            "heartbeat:$sessionToken",
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            $status,
            $details
        ]);
        
    } catch (Exception $e) {
        error_log("记录网络状态日志失败: " . $e->getMessage());
    }
}

try {
    // 获取请求数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('无效的JSON数据');
    }
    
    // 验证必需字段
    if (!isset($data['session_token']) || empty(trim($data['session_token']))) {
        throw new Exception('缺少会话令牌');
    }
    
    $sessionToken = trim($data['session_token']);
    $clientInfo = [
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'timestamp' => time(),
        'online_status' => $data['online_status'] ?? true,
        'last_offline_duration' => $data['last_offline_duration'] ?? 0
    ];
    
    // 更新心跳
    $heartbeatResult = updateHeartbeat($sessionToken, $clientInfo);
    
    if (!$heartbeatResult['valid']) {
        logNetworkStatus($sessionToken, 'heartbeat_failed', $heartbeatResult['reason']);
        throw new Exception($heartbeatResult['reason']);
    }
    
    // 检查是否有其他活跃会话
    $hasOtherSessions = checkOtherActiveSessions($sessionToken, $heartbeatResult['user_id']);
    
    if ($hasOtherSessions) {
        logNetworkStatus($sessionToken, 'multiple_sessions_detected', '检测到其他活跃会话');
        
        // 将当前会话设为非活跃
        $pdo = new PDO("sqlite:$dbPath");
        $stmt = $pdo->prepare("UPDATE user_sessions SET is_active = 0 WHERE session_token = ?");
        $stmt->execute([$sessionToken]);
        
        echo json_encode([
            'success' => false,
            'code' => 'MULTIPLE_SESSIONS',
            'message' => '检测到其他设备登录，当前会话已失效'
        ]);
        exit();
    }
    
    // 检查离线时间是否过长
    if (isset($data['last_offline_duration']) && $data['last_offline_duration'] > 60) {
        logNetworkStatus($sessionToken, 'long_offline_detected', "离线时间: {$data['last_offline_duration']}秒");
        
        echo json_encode([
            'success' => false,
            'code' => 'LONG_OFFLINE',
            'message' => '离线时间过长，会话已失效'
        ]);
        exit();
    }
    
    // 记录正常心跳
    logNetworkStatus($sessionToken, 'heartbeat_success', '网络状态正常');
    
    echo json_encode([
        'success' => true,
        'message' => '心跳检测正常',
        'data' => [
            'username' => $heartbeatResult['username'],
            'server_time' => time(),
            'session_valid' => true
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
