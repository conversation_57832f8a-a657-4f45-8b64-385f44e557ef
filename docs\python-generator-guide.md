# Python激活码生成器使用指南

## 📍 工具位置
**主要工具**: `server/generate_activation_code.py`

## 🚀 快速开始

### 1. 运行生成器
```bash
cd server
python generate_activation_code.py
```

### 2. 功能菜单
```
🔐 新用户认证系统 - 激活码生成器
============================================================

请选择操作:
1. 生成单个激活码
2. 批量生成激活码
3. 查看有效激活码
4. 查看用户绑定情况
5. 数据库统计信息
6. 退出
```

## 🔧 主要功能

### 1. 生成单个激活码
- 默认有效期：30天
- 可自定义有效期
- 自动检查重复
- 使用安全随机数生成

### 2. 批量生成激活码
- 可指定生成数量
- 可自定义有效期
- 批量操作，提高效率

### 3. 查看有效激活码
- 显示所有未过期的激活码
- 显示绑定用户信息
- 显示使用状态

### 4. 查看用户绑定情况
- 显示所有用户
- 显示用户邮箱
- 显示绑定的激活码

### 5. 数据库统计信息
- 总用户数
- 总激活码数
- 有效激活码数
- 已使用激活码数
- 已过期激活码数
- 活跃会话数

## 📊 激活码格式

**格式**: `XXXXX-XXXXX-XXXXX-XXXXX`
- 总长度：23个字符（20个字符 + 3个连字符）
- 4个段，每段5个字符
- 字符集：`ABCDEFGHJKLMNPQRSTUVWXYZ23456789`
- 排除容易混淆的字符：`0, O, I, 1, l`

**示例**: `6T92U-RFTEY-546GZ-BFXMM`

## 🔒 安全特性

1. **安全随机数**: 使用 `secrets` 模块生成高质量随机数
2. **重复检查**: 自动检查激活码是否已存在
3. **字符过滤**: 排除容易混淆的字符
4. **数据库事务**: 确保数据一致性

## 📈 使用示例

### 生成单个激活码
```
请选择操作: 1
请输入有效天数 (默认30天): 60

✅ 激活码生成成功:
   激活码: 6T92U-RFTEY-546GZ-BFXMM
   创建时间: 2025-07-23 17:45:27
   过期时间: 2025-09-21 17:45:27
   有效期: 60 天
```

### 批量生成激活码
```
请选择操作: 2
请输入生成数量: 5
请输入有效天数 (默认30天): 30

🔄 开始批量生成 5 个激活码...

📝 生成第 1/5 个激活码:
✅ 激活码生成成功: ABC12-DEF34-GHI56-JKL78
...
✅ 批量生成完成，成功生成 5 个激活码
```

### 查看激活码列表
```
📋 当前有效激活码 (10 个):
------------------------------------------------------------------------------------------
激活码                       创建时间                 过期时间                 状态         绑定用户
------------------------------------------------------------------------------------------
6T92U-RFTEY-546GZ-BFXMM   2025-07-23 09:45:27  2025-08-22 17:45:27  未使用        未绑定
43DJ3-2UYFL-JJXAA-6BCM6   2025-07-23 09:14:22  2025-08-22 09:14:22  已使用        testuser
...
```

## 🗄️ 数据库要求

- **数据库文件**: `server/user_system.db3`
- **数据库类型**: SQLite3
- **必需表**: `activation_codes`, `users`, `user_sessions`

## ⚠️ 注意事项

1. **数据库路径**: 工具会自动检测数据库路径
2. **权限要求**: 需要对数据库文件的读写权限
3. **Python版本**: 建议使用 Python 3.6+
4. **依赖模块**: 使用标准库，无需额外安装

## 🔧 故障排除

### 数据库不存在
```
❌ 数据库不存在，请先运行 create_db.php 初始化数据库
   命令: php create_db.php
```

**解决方法**: 先运行 `php create_db.php` 初始化数据库

### 权限错误
**解决方法**: 确保对 `server/user_system.db3` 文件有读写权限

### 导入错误
**解决方法**: 确保在 `server` 目录下运行脚本

## 📝 更新记录

### v2.0 (2025-07-23)
- ✅ 支持新的用户认证数据库结构
- ✅ 默认有效期改为30天
- ✅ 添加用户绑定情况查看
- ✅ 添加数据库统计功能
- ✅ 使用安全随机数生成
- ✅ 自动路径检测

### v1.0 (原版)
- 基础激活码生成功能
- 支持旧数据库结构
