/* 倒计时网站样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    width: 100vw !important;
    height: 100vh !important;
    min-width: 100vw !important;
    min-height: 100vh !important;
    margin: 0 !important;
    padding: 0 !important;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    width: 100vw !important;
    height: 100vh !important;
    min-width: 100vw !important;
    min-height: 100vh !important;
    overflow: hidden;
    background: linear-gradient(135deg, #ff4757 0%, #ff3838 25%, #ff6b7a 50%, #ff4757 75%, #ff2f3a 100%) !important;
    position: relative;
    margin: 0 !important;
    padding: 0 !important;
}

/* 装饰线条和图案 */
body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
        linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.05) 50%, transparent 60%);
    background-size: 50px 50px, 50px 50px, 100px 100px;
    animation: backgroundMove 20s linear infinite;
    z-index: 1;
}

@keyframes backgroundMove {
    0% { background-position: 0 0, 0 0, 0 0; }
    100% { background-position: 50px 50px, -50px -50px, 100px 100px; }
}

/* Logo容器 */
.logo-container {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 1000;
}

.logo {
    max-width: 69px;  /* 60px * 1.15 = 69px */
    max-height: 46px; /* 40px * 1.15 = 46px */
    display: block;
    margin-left: 20px;
    margin-top: 20px;
    object-fit: contain;
    transition: all 0.3s ease;
}

/* 主要内容区域 */
.main-content {
    position: absolute;
    top: calc(40% - 20px); /* 向上移动20px */
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 100;
}

.main-title {
    color: white;
    font-size: 3.84vw; /* 3.2vw * 1.2 = 3.84vw */
    font-weight: bold;
    margin-bottom: 2.88vh; /* 2.4vh * 1.2 = 2.88vh */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    animation: titlePulse 2s ease-in-out infinite alternate;
    white-space: nowrap;
}

@keyframes titlePulse {
    0% { transform: scale(1); }
    100% { transform: scale(1.05); }
}

.warning-title {
    color: white;
    font-size: 2.28vw; /* 1.9vw * 1.2 = 2.28vw */
    font-weight: bold;
    margin-top: 2.88vh; /* 2.4vh * 1.2 = 2.88vh */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    text-align: center;
}

.warning-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.6) 50%,
        transparent 100%);
    animation: shine 3s ease-in-out infinite;
}

@keyframes shine {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

.highlight-number {
    color: #ffff00;
    font-weight: bold;
    font-size: 3.5rem; /* 增大字体从2.5rem到3.5rem */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7), 0 0 10px #ffff00;
    display: inline-block;
    transform: scale(1.2); /* 额外放大20% */
    margin: 0 22px; /* 左右增加15px间距，避免覆盖其他文字 */
}

/* 倒计时容器 */
.countdown-container {
    display: flex;
    justify-content: center;
    gap: 1.8vw;
    flex-wrap: nowrap;
    align-items: center;
    min-width: 100%;
    overflow-x: auto;
}

.countdown-item {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 15px;
    padding: 1.8vh 1.2vw;
    min-width: 7.2vw;
    border: 3px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.countdown-number {
    display: block;
    font-size: 4.56vw; /* 3.8vw * 1.2 = 4.56vw */
    font-weight: bold;
    color: #ffff00;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    line-height: 1;
    animation: numberGlow 2s ease-in-out infinite alternate;
}

@keyframes numberGlow {
    0% { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7), 0 0 10px #ffff00; }
    100% { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7), 0 0 20px #ffff00, 0 0 30px #ffff00; }
}

.countdown-label {
    display: block;
    font-size: 1.44vw;
    color: #ffff00;
    margin-top: 0.96vh;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    font-weight: bold;
}

/* 汽车图片容器 */
.car-container {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 100;
}

.car-image {
    max-width: 57.6vw; /* 48vw * 1.2 = 57.6vw */
    max-height: 27.6vh; /* 23vh * 1.2 = 27.6vh */
    width: auto;
    height: auto;
    display: block;
    margin-bottom: 9vh;
    transform: translateY(-25px); /* -15px - 10px = -25px */
}

/* 角落指示器样式 */
.corner-indicator {
    position: fixed;
    width: 50px;
    height: 50px;
    background-color: #FFD700;
    border: 2px solid #FFA500;
    z-index: 9999;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.8);
}

.corner-indicator.top-left {
    top: 10px;
    left: 10px;
}

.corner-indicator.top-right {
    top: 10px;
    right: 10px;
}

.corner-indicator.bottom-left {
    bottom: 10px;
    left: 10px;
}

.corner-indicator.bottom-right {
    bottom: 10px;
    right: 10px;
}

/* 4K电视和超大屏幕优化 - 保持与1920x1080相同的视觉比例 */
@media (min-width: 2560px) {
    .main-title {
        font-size: 3.84vw; /* 3.2vw * 1.2 = 3.84vw */
        margin-bottom: 2.88vh; /* 2.4vh * 1.2 = 2.88vh */
        white-space: nowrap;
    }

    .warning-title {
        font-size: 2.28vw; /* 1.9vw * 1.2 = 2.28vw */
        white-space: nowrap;
    }

    .countdown-number {
        font-size: 4.56vw; /* 3.8vw * 1.2 = 4.56vw */
    }

    .countdown-label {
        font-size: 1.44vw;
    }

    .car-image {
        max-width: 57.6vw; /* 48vw * 1.2 = 57.6vw */
        max-height: 27.6vh; /* 23vh * 1.2 = 27.6vh */
        transform: translateY(-25px); /* -15px - 10px = -25px */
    }

    .logo {
        max-width: 6.9vw;  /* 6vw * 1.15 = 6.9vw */
        max-height: 4.6vh; /* 4vh * 1.15 = 4.6vh */
        margin-left: 1vw;
        margin-top: 1vh;
    }

    .countdown-container {
        gap: 1.8vw;
    }

    .countdown-item {
        min-width: 7.2vw;
        padding: 1.8vh 1.2vw;
    }
}

/* 响应式设计 - 区分真实移动设备和桌面缩放 */

/* 真实移动设备适配 - 使用触摸检测 */
@media (max-width: 768px) and (pointer: coarse) {
    .main-title {
        font-size: 2rem;
        margin-bottom: 20px;
    }

    .countdown-container {
        gap: 15px;
    }

    .countdown-item {
        min-width: 80px;
        padding: 15px;
    }

    .countdown-number {
        font-size: 2.5rem;
    }

    .countdown-label {
        font-size: 1rem;
    }

    .car-image {
        max-width: 480px; /* 400px * 1.2 = 480px */
        max-height: 240px; /* 200px * 1.2 = 240px */
        transform: translateY(-10px); /* 向上移动10px */
    }

    .logo {
        max-width: 115px; /* 100px * 1.15 = 115px */
        max-height: 69px; /* 60px * 1.15 = 69px */
        margin-left: 5px;
        margin-top: 5px;
    }
}

/* 小屏移动设备适配 */
@media (max-width: 480px) and (pointer: coarse) {
    .main-title {
        font-size: 1.5rem;
    }

    .countdown-number {
        font-size: 2rem;
    }

    .countdown-item {
        min-width: 60px;
        padding: 10px;
    }

    .logo {
        max-width: 92px; /* 80px * 1.15 = 92px */
        max-height: 58px; /* 50px * 1.15 = 58px */
        margin-left: 3px;
        margin-top: 3px;
    }
}

/* 桌面版小窗口适配 - 保持视口单位的响应性 */
@media (max-width: 768px) and (pointer: fine) {
    .countdown-container {
        gap: 1.2vw;
    }

    .countdown-item {
        min-width: 6vw;
        padding: 1.2vw;
    }

    .logo {
        max-width: 9.2vw; /* 8vw * 1.15 = 9.2vw */
        max-height: 5.8vh; /* 5vh * 1.15 = 5.8vh */
        margin-left: 0.5vw;
        margin-top: 0.5vh;
    }
}

/* 超小屏幕优化 */
@media (max-width: 320px) {
    .logo {
        max-width: 69px; /* 60px * 1.15 = 69px */
        max-height: 46px; /* 40px * 1.15 = 46px */
        margin-left: 2px;
        margin-top: 2px;
    }
}

/* 大屏幕优化 */
@media (min-width: 1440px) and (max-width: 1919px) {
    .logo {
        max-width: 92px; /* 80px * 1.15 = 92px */
        max-height: 58px; /* 50px * 1.15 = 58px */
        margin-left: 20px;
        margin-top: 20px;
    }
}

/* 用户图标样式 */
.user-icon-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
}

.user-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #333;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    animation: fadeToTransparent 3s ease-in-out forwards;
}

.user-icon:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
    opacity: 1 !important;
}

.user-menu {
    position: absolute;
    top: 50px;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    border-radius: 8px;
    padding: 15px;
    min-width: 250px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.user-icon-container:hover .user-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-info {
    margin-bottom: 10px;
}

.username {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
}

.version-info {
    font-size: 12px;
    color: #4CAF50;
    margin-bottom: 5px;
}

.activation-code {
    font-size: 12px;
    font-family: 'Courier New', monospace;
    background: rgba(255, 255, 255, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    margin-bottom: 5px;
}

.expiry-date {
    font-size: 11px;
    color: #ccc;
}

.logout-btn {
    background: #f44336;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    width: 100%;
    transition: background 0.3s ease;
}

.logout-btn:hover {
    background: #d32f2f;
}

/* 版本切换图标样式 */
.version-toggle-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.version-toggle {
    min-width: 120px;
    height: 40px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
    opacity: 0.3;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    padding: 0 8px;
    white-space: nowrap;
    color: white;
}

.version-toggle:hover {
    opacity: 0.9;
    transform: scale(1.05);
    background: rgba(0, 0, 0, 0.9);
}

/* 3秒后自动降低透明度的动画 */
@keyframes fadeToTransparent {
    0% { opacity: 1; }
    90% { opacity: 1; }
    100% { opacity: 0.6; }
}

/* 小屏幕响应式优化 - 保持比例缩放 */
@media (max-width: 1440px) {
    /* 保持原有的视口单位比例，确保元素按比例缩放 */
    .user-icon-container {
        top: 2vh;
        right: 3vw;
    }

    .version-toggle-container {
        bottom: 5vh;
        right: 3vw;
    }

    /* 汽车图片在小屏幕下适当限制最大宽度 */
    .car-image {
        max-width: min(57.6vw, 90vw);
        height: auto;
    }
}

/* 移动设备优化 - 保持比例但适配触摸操作 */
@media (max-width: 768px) {
    .user-icon {
        width: 35px;
        height: 35px;
    }

    .version-toggle {
        min-width: 100px;
        height: 35px;
        font-size: 10px;
        padding: 0 6px;
    }

    .car-image {
        max-width: min(57.6vw, 95vw);
        height: auto;
    }
}

/* 超大屏幕优化 */
@media (min-width: 1920px) and (max-width: 2559px) {
    .logo {
        max-width: 115px; /* 100px * 1.15 = 115px */
        max-height: 69px; /* 60px * 1.15 = 69px */
        margin-left: 20px;
        margin-top: 20px;
    }
}
