<?php
/**
 * 检查激活码系统
 */

$dbPath = '../server/user_system.db3';

try {
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>激活码系统检查</h2>";
    
    // 检查激活码表是否存在
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='activation_codes'");
    if ($stmt->fetch()) {
        echo "<p>✅ activation_codes表存在</p>";
        
        // 查询所有激活码
        $stmt = $pdo->query("SELECT * FROM activation_codes ORDER BY created_at DESC LIMIT 10");
        $codes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>最新10个激活码：</h3>";
        if (empty($codes)) {
            echo "<p>❌ 没有找到任何激活码</p>";
            echo "<p>需要生成激活码用于测试</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>激活码</th><th>状态</th><th>用户ID</th><th>过期时间</th><th>创建时间</th></tr>";
            foreach ($codes as $code) {
                $status = $code['is_used'] ? '已使用' : '未使用';
                $isExpired = strtotime($code['expires_at']) < time();
                if (!$code['is_used'] && $isExpired) {
                    $status = '已过期';
                }
                
                $rowColor = $code['is_used'] ? '#ffcccc' : ($isExpired ? '#ffffcc' : '#ccffcc');
                
                echo "<tr style='background-color: $rowColor;'>";
                echo "<td>{$code['id']}</td>";
                echo "<td><strong>{$code['activation_code']}</strong></td>";
                echo "<td>$status</td>";
                echo "<td>{$code['user_id']}</td>";
                echo "<td>{$code['expires_at']}</td>";
                echo "<td>{$code['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // 查找可用的激活码
        $stmt = $pdo->query("
            SELECT activation_code, expires_at 
            FROM activation_codes 
            WHERE is_used = 0 AND expires_at > datetime('now')
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $availableCode = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($availableCode) {
            echo "<div style='background: #d4edda; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
            echo "<h3>✅ 可用的激活码：</h3>";
            echo "<p style='font-size: 24px; font-weight: bold; color: #155724;'>{$availableCode['activation_code']}</p>";
            echo "<p>过期时间：{$availableCode['expires_at']}</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
            echo "<h3>❌ 没有可用的激活码</h3>";
            echo "<p>需要生成新的激活码用于测试</p>";
            echo "</div>";
        }
        
    } else {
        echo "<p>❌ activation_codes表不存在</p>";
        
        // 显示所有表
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<h3>数据库中的所有表：</h3>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
    }
    
    echo "<hr>";
    echo "<h3>操作链接：</h3>";
    echo "<p><a href='generate-test-activation-code.php'>生成测试激活码</a></p>";
    echo "<p><a href='javascript:location.reload()'>刷新页面</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ 数据库错误: " . $e->getMessage() . "</p>";
}
?>
