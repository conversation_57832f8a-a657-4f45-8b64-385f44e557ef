<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <title>版本切换测试 - 标准版</title>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Logo显示区域 -->
    <div class="logo-container">
        <img src="../images/logo.png" alt="Logo" class="logo">
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
        <h1 class="main-title">版本切换测试 - 标准版</h1>
        
        <div class="countdown-container">
            <div class="countdown-item">
                <span class="countdown-number" id="days">05</span>
                <span class="countdown-label">天</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number" id="hours">07</span>
                <span class="countdown-label">小时</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number" id="minutes">28</span>
                <span class="countdown-label">分钟</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number" id="seconds">30</span>
                <span class="countdown-label">秒</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number" id="milliseconds">000</span>
                <span class="countdown-label">毫秒</span>
            </div>
        </div>
        
        <h2 class="warning-title">
            测试版本切换功能
        </h2>
    </div>

    <!-- 汽车图片 -->
    <div class="car-container">
        <img src="../images/upload1.png" alt="汽车" class="car-image">
    </div>

    <!-- 测试控制面板 -->
    <div style="position: fixed; top: 20px; right: 20px; background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 10px; z-index: 1000;">
        <h3>🧪 版本切换测试</h3>
        <p>当前页面: <strong>标准版</strong></p>
        <button id="switchToTV" style="background: #007bff; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; margin: 5px;">
            📺 切换到TV版
        </button>
        <button id="resetDetection" style="background: #28a745; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; margin: 5px;">
            🔄 重置检测
        </button>
        <div id="testStatus" style="margin-top: 10px; font-size: 12px;"></div>
    </div>

    <!-- 加载智能设备检测器 -->
    <script src="../js/smart-device-detector.js"></script>
    
    <script>
        // 等待设备检测器加载完成
        window.addEventListener('DOMContentLoaded', function() {
            const switchToTVBtn = document.getElementById('switchToTV');
            const resetDetectionBtn = document.getElementById('resetDetection');
            const testStatus = document.getElementById('testStatus');
            
            function updateStatus() {
                const manualSwitch = sessionStorage.getItem('manual_version_switch');
                const preferredVersion = sessionStorage.getItem('preferred_version');
                const redirectDone = sessionStorage.getItem('device_redirect_done');
                
                testStatus.innerHTML = `
                    手动切换: ${manualSwitch || 'null'}<br>
                    偏好版本: ${preferredVersion || 'null'}<br>
                    重定向完成: ${redirectDone || 'null'}
                `;
            }
            
            // 初始状态
            updateStatus();
            
            // 切换到TV版本
            switchToTVBtn.addEventListener('click', function() {
                console.log('🧪 测试：手动切换到TV版本');
                
                if (window.smartDeviceDetector) {
                    window.smartDeviceDetector.manualSwitchToTV();
                } else {
                    console.error('智能设备检测器未加载');
                    // 备用方案
                    sessionStorage.setItem('manual_version_switch', 'true');
                    sessionStorage.setItem('preferred_version', 'test-version-switch-tv.html');
                    window.location.href = 'test-version-switch-tv.html';
                }
                
                updateStatus();
            });
            
            // 重置检测
            resetDetectionBtn.addEventListener('click', function() {
                console.log('🧪 测试：重置自动检测');
                
                if (window.smartDeviceDetector) {
                    window.smartDeviceDetector.resetAutoDetection();
                } else {
                    sessionStorage.removeItem('manual_version_switch');
                    sessionStorage.removeItem('preferred_version');
                    sessionStorage.removeItem('device_redirect_done');
                }
                
                updateStatus();
            });
            
            // 每秒更新状态
            setInterval(updateStatus, 1000);
        });
    </script>
</body>
</html>
