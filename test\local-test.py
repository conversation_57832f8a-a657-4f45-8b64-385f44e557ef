#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地测试脚本 - 快速启动付费解锁系统测试环境
自动初始化数据库、生成测试激活码、启动服务器
"""

import os
import sys
import sqlite3
import random
import string
from datetime import datetime, timedelta
import threading
import time

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🧪 付费解锁系统 - 本地测试环境")
    print("=" * 60)

def create_test_database():
    """创建测试数据库"""
    print("📊 创建测试数据库...")
    
    # 确保server目录存在
    os.makedirs('../server', exist_ok=True)
    
    db_path = '../server/unlock_system.db'
    
    # 如果数据库已存在，删除重建
    if os.path.exists(db_path):
        os.remove(db_path)
        print("   🗑️ 删除旧数据库")
    
    # 创建数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建激活码表
    cursor.execute('''
        CREATE TABLE activation_codes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code VARCHAR(25) NOT NULL UNIQUE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            expires_at DATETIME NOT NULL,
            is_used BOOLEAN DEFAULT FALSE,
            device_fingerprint TEXT,
            used_at DATETIME,
            status VARCHAR(20) DEFAULT 'active'
        )
    ''')
    
    # 创建用户记录表
    cursor.execute('''
        CREATE TABLE user_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            activation_code VARCHAR(25) NOT NULL,
            device_fingerprint TEXT NOT NULL,
            device_info TEXT,
            ip_address VARCHAR(45),
            unlock_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_access DATETIME DEFAULT CURRENT_TIMESTAMP,
            access_count INTEGER DEFAULT 1,
            user_agent TEXT,
            screen_resolution VARCHAR(20)
        )
    ''')
    
    conn.commit()
    conn.close()
    
    print("   ✅ 测试数据库创建成功")

def generate_test_code():
    """生成测试激活码"""
    charset = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'
    segments = [''.join(random.choices(charset, k=5)) for _ in range(4)]
    return '-'.join(segments)

def create_test_activation_codes():
    """创建测试激活码"""
    print("🔑 生成测试激活码...")
    
    db_path = '../server/unlock_system.db'
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    test_codes = []
    
    # 生成5个测试激活码
    for i in range(5):
        code = generate_test_code()
        created_at = datetime.now()
        expires_at = created_at + timedelta(days=10)
        
        cursor.execute('''
            INSERT INTO activation_codes (code, created_at, expires_at, status)
            VALUES (?, ?, ?, 'active')
        ''', (code, created_at, expires_at))
        
        test_codes.append(code)
        print(f"   ✅ 测试激活码 {i+1}: {code}")
    
    conn.commit()
    conn.close()
    
    return test_codes

def start_simple_server():
    """启动简化的测试服务器"""
    print("🚀 启动测试服务器...")
    
    from http.server import HTTPServer, SimpleHTTPRequestHandler
    import json
    import urllib.parse
    
    class TestHandler(SimpleHTTPRequestHandler):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, directory='..', **kwargs)
        
        def do_POST(self):
            if self.path == '/api/validate-activation':
                self.handle_activation_validation()
            else:
                self.send_error(404)
        
        def do_GET(self):
            if self.path == '/api/stats':
                self.handle_stats()
            else:
                super().do_GET()
        
        def handle_activation_validation(self):
            try:
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode('utf-8'))
                
                activation_code = data.get('activationCode', '').strip().upper()
                device_fingerprint = data.get('deviceFingerprint', '')
                
                # 验证激活码
                db_path = '../server/unlock_system.db'
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM activation_codes 
                    WHERE code = ? AND status = 'active'
                ''', (activation_code,))
                
                code_record = cursor.fetchone()
                
                if not code_record:
                    response = {
                        'success': False,
                        'message': '激活码不存在或已失效'
                    }
                else:
                    # 检查是否过期
                    expires_at = datetime.fromisoformat(code_record[3])
                    if datetime.now() > expires_at:
                        response = {
                            'success': False,
                            'message': '激活码已过期'
                        }
                    else:
                        # 激活成功
                        if not code_record[4]:  # 未使用
                            # 标记为已使用
                            cursor.execute('''
                                UPDATE activation_codes 
                                SET is_used = TRUE, device_fingerprint = ?, used_at = ?, status = 'used'
                                WHERE code = ?
                            ''', (device_fingerprint, datetime.now(), activation_code))
                            
                            # 记录用户信息
                            cursor.execute('''
                                INSERT INTO user_records 
                                (activation_code, device_fingerprint, device_info, ip_address, user_agent, screen_resolution)
                                VALUES (?, ?, ?, ?, ?, ?)
                            ''', (
                                activation_code, device_fingerprint, data.get('deviceInfo', ''),
                                self.client_address[0], data.get('userAgent', ''), data.get('screenResolution', '')
                            ))
                            
                            conn.commit()
                        
                        response = {
                            'success': True,
                            'message': '激活成功！欢迎使用',
                            'data': {
                                'expiresAt': int(expires_at.timestamp() * 1000),
                                'isReturningUser': code_record[4]
                            }
                        }
                
                conn.close()
                
                # 发送响应
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'POST, GET, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                self.end_headers()
                
                self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
                
            except Exception as e:
                print(f"❌ 处理激活码验证时出错: {e}")
                self.send_error(500)
        
        def handle_stats(self):
            try:
                db_path = '../server/unlock_system.db'
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                cursor.execute("SELECT COUNT(*) FROM activation_codes")
                total_codes = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM activation_codes WHERE is_used = TRUE")
                used_codes = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM activation_codes WHERE status = 'active' AND expires_at > datetime('now')")
                active_codes = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM user_records")
                total_users = cursor.fetchone()[0]
                
                conn.close()
                
                response = {
                    'success': True,
                    'data': {
                        'totalCodes': total_codes,
                        'usedCodes': used_codes,
                        'activeCodes': active_codes,
                        'totalUsers': total_users
                    }
                }
                
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                
                self.wfile.write(json.dumps(response).encode('utf-8'))
                
            except Exception as e:
                print(f"❌ 获取统计信息时出错: {e}")
                self.send_error(500)
        
        def do_OPTIONS(self):
            self.send_response(200)
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'POST, GET, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            self.end_headers()
    
    # 启动服务器
    server = HTTPServer(('localhost', 8080), TestHandler)
    print("   🌐 服务器启动成功: http://localhost:8080")
    print("   📱 锁屏页面: http://localhost:8080/lock.html")
    print("   🏠 主页面: http://localhost:8080/index.php")
    print("   🧪 测试页面: http://localhost:8080/test/test-unlock-system.html")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        server.shutdown()

def main():
    """主函数"""
    print_banner()
    
    print("🎯 本地测试环境将执行以下操作:")
    print("   1. 创建测试数据库")
    print("   2. 生成5个测试激活码")
    print("   3. 启动本地测试服务器")
    print("   4. 提供完整的API支持")
    
    input("\n按回车键开始...")
    
    try:
        # 创建测试数据库
        create_test_database()
        
        # 生成测试激活码
        test_codes = create_test_activation_codes()
        
        print("\n🎉 测试环境准备完成！")
        print("\n📋 可用的测试激活码:")
        for i, code in enumerate(test_codes, 1):
            print(f"   {i}. {code}")
        
        print("\n💡 测试步骤:")
        print("   1. 访问 http://localhost:8080/lock.html")
        print("   2. 输入上述任意一个激活码")
        print("   3. 点击解锁按钮")
        print("   4. 成功后会跳转到主页面")
        
        print("\n🚀 启动测试服务器...")
        start_simple_server()
        
    except Exception as e:
        print(f"\n❌ 测试环境启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
