<?php
/**
 * 验证码生成API
 * 生成图形验证码并返回图片
 */

session_start();

// 数据库配置
$dbPath = '../server/user_system.db3';

/**
 * 生成随机验证码
 */
function generateCaptchaCode($length = 4) {
    $charset = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
    $code = '';
    for ($i = 0; $i < $length; $i++) {
        $code .= $charset[random_int(0, strlen($charset) - 1)];
    }
    return $code;
}

/**
 * 创建验证码图片
 */
function createCaptchaImage($code) {
    // 图片尺寸
    $width = 120;
    $height = 40;
    
    // 创建画布
    $image = imagecreate($width, $height);
    
    // 定义颜色
    $bgColor = imagecolorallocate($image, 240, 240, 240);
    $textColor = imagecolorallocate($image, 50, 50, 50);
    $lineColor = imagecolorallocate($image, 200, 200, 200);
    
    // 填充背景
    imagefill($image, 0, 0, $bgColor);
    
    // 添加干扰线
    for ($i = 0; $i < 5; $i++) {
        imageline($image, 
            random_int(0, $width), random_int(0, $height),
            random_int(0, $width), random_int(0, $height),
            $lineColor
        );
    }
    
    // 添加验证码文字
    $fontSize = 5;
    $x = 20;
    for ($i = 0; $i < strlen($code); $i++) {
        $y = random_int(8, 25);
        imagestring($image, $fontSize, $x, $y, $code[$i], $textColor);
        $x += 20;
    }
    
    // 添加干扰点
    for ($i = 0; $i < 50; $i++) {
        imagesetpixel($image, 
            random_int(0, $width), random_int(0, $height),
            $textColor
        );
    }
    
    return $image;
}

/**
 * 保存验证码到数据库
 */
function saveCaptchaToDatabase($code, $sessionId) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 清除过期的验证码
        $pdo->exec("DELETE FROM captcha_codes WHERE expires_at < datetime('now')");
        
        // 清除同一会话的旧验证码
        $stmt = $pdo->prepare("DELETE FROM captcha_codes WHERE session_id = ?");
        $stmt->execute([$sessionId]);
        
        // 保存新验证码
        $codeHash = hash('sha256', strtoupper($code));
        $expiresAt = date('Y-m-d H:i:s', time() + 300); // 5分钟过期

        // 检查是否有code_value字段（用于开发调试）
        $hasCodeValue = false;
        try {
            $stmt = $pdo->query("PRAGMA table_info(captcha_codes)");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($columns as $column) {
                if ($column['name'] === 'code_value') {
                    $hasCodeValue = true;
                    break;
                }
            }
        } catch (Exception $e) {
            // 忽略错误
        }

        if ($hasCodeValue) {
            // 如果有code_value字段，同时保存原始值（仅用于开发调试）
            $stmt = $pdo->prepare("
                INSERT INTO captcha_codes (code_hash, code_value, session_id, expires_at)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$codeHash, $code, $sessionId, $expiresAt]);
        } else {
            // 正常情况，只保存哈希值
            $stmt = $pdo->prepare("
                INSERT INTO captcha_codes (code_hash, session_id, expires_at)
                VALUES (?, ?, ?)
            ");
            $stmt->execute([$codeHash, $sessionId, $expiresAt]);
        }
        
        return true;
    } catch (Exception $e) {
        error_log("保存验证码失败: " . $e->getMessage());
        return false;
    }
}

try {
    // 生成验证码
    $captchaCode = generateCaptchaCode(4);
    
    // 获取会话ID
    $sessionId = session_id();
    
    // 保存到数据库
    if (!saveCaptchaToDatabase($captchaCode, $sessionId)) {
        throw new Exception('保存验证码失败');
    }
    
    // 创建验证码图片
    $image = createCaptchaImage($captchaCode);
    
    // 设置响应头
    header('Content-Type: image/png');
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // 输出图片
    imagepng($image);
    
    // 清理资源
    imagedestroy($image);
    
} catch (Exception $e) {
    // 错误时返回空白图片
    $image = imagecreate(120, 40);
    $bgColor = imagecolorallocate($image, 255, 255, 255);
    $textColor = imagecolorallocate($image, 255, 0, 0);
    imagefill($image, 0, 0, $bgColor);
    imagestring($image, 3, 30, 15, 'ERROR', $textColor);
    
    header('Content-Type: image/png');
    imagepng($image);
    imagedestroy($image);
}
?>
