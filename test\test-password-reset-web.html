<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码找回功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #28a745; }
        .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #dc3545; }
        .info { color: #0c5460; background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #17a2b8; }
        .warning { color: #856404; background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #ffc107; }
        
        input[type="text"], input[type="email"] {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        input[type="text"]:focus, input[type="email"]:focus {
            border-color: #667eea;
            outline: none;
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 16px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .form-group {
            margin: 20px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 密码找回功能测试</h1>
        
        <div class="info">
            <strong>📧 邮件配置状态：</strong><br>
            ✅ 163邮箱配置完成<br>
            ✅ SMTP连接测试通过<br>
            ✅ 邮件发送功能正常
        </div>

        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" value="ataehee1" placeholder="输入用户名">
        </div>

        <div class="form-group">
            <label for="email">邮箱地址:</label>
            <input type="email" id="email" value="<EMAIL>" placeholder="输入邮箱地址">
        </div>

        <div class="form-group">
            <label for="captcha">验证码:</label>
            <input type="text" id="captcha" placeholder="输入验证码">
            <img id="captchaImage" src="../api/captcha.php" alt="验证码" style="cursor: pointer; margin-left: 10px; vertical-align: middle;" onclick="refreshCaptcha()">
        </div>

        <div style="text-align: center;">
            <button onclick="sendResetEmail()" id="sendBtn">发送重置邮件</button>
            <button onclick="refreshCaptcha()">刷新验证码</button>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在发送邮件...</p>
        </div>

        <div id="result"></div>

        <div class="warning">
            <strong>⚠️ 测试说明：</strong><br>
            • 这是密码找回功能的完整测试<br>
            • 邮件将发送到指定的QQ邮箱<br>
            • 请检查邮箱收件箱和垃圾邮件文件夹<br>
            • 重置链接有效期为24小时
        </div>
    </div>

    <script>
        function refreshCaptcha() {
            const img = document.getElementById('captchaImage');
            img.src = '../api/captcha.php?' + new Date().getTime();
        }

        async function sendResetEmail() {
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            const captcha = document.getElementById('captcha').value;
            const resultDiv = document.getElementById('result');
            const sendBtn = document.getElementById('sendBtn');
            const loading = document.getElementById('loading');
            
            // 验证输入
            if (!username || !email || !captcha) {
                resultDiv.innerHTML = '<div class="error">❌ 请填写完整信息</div>';
                return;
            }
            
            if (!email.includes('@')) {
                resultDiv.innerHTML = '<div class="error">❌ 请输入有效的邮箱地址</div>';
                return;
            }
            
            // 显示加载状态
            sendBtn.disabled = true;
            loading.style.display = 'block';
            resultDiv.innerHTML = '';
            
            try {
                const response = await fetch('../api/forgot-password.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        email: email,
                        captcha: captcha
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ 密码重置邮件发送成功！<br><br>
                            <strong>📧 邮件详情：</strong><br>
                            • 收件人: ${email}<br>
                            • 发送时间: ${new Date().toLocaleString()}<br>
                            • 有效期: 24小时<br><br>
                            <strong>📋 下一步操作：</strong><br>
                            1. 检查邮箱收件箱<br>
                            2. 点击邮件中的重置链接<br>
                            3. 设置新密码
                        </div>
                    `;
                    
                    // 清空表单
                    document.getElementById('captcha').value = '';
                    refreshCaptcha();
                    
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 发送失败: ${data.message}</div>`;
                    refreshCaptcha();
                }
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
                refreshCaptcha();
            } finally {
                sendBtn.disabled = false;
                loading.style.display = 'none';
            }
        }
        
        // 页面加载时刷新验证码
        window.onload = function() {
            refreshCaptcha();
        };
    </script>
</body>
</html>
