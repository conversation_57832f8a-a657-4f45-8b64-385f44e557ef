# 📧 邮件配置指南

本指南将帮助您配置邮件发送功能，让用户能够收到密码重置邮件。

## 🎯 配置概述

系统支持多种邮件服务商：
- **QQ邮箱** (推荐，免费)
- **Gmail** (需要应用专用密码)
- **Outlook** (Microsoft邮箱，需要应用专用密码)
- **163邮箱** (免费)
- **阿里云邮箱** (企业邮箱)
- **自定义SMTP** (其他邮件服务商)

## 🚀 快速配置

### 第一步：选择邮件服务商

推荐使用 **QQ邮箱**，配置简单且稳定可靠。

### 第二步：获取邮箱授权码

#### QQ邮箱授权码获取
1. 登录 [QQ邮箱](https://mail.qq.com)
2. 点击 **设置** → **账户**
3. 找到 "POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
4. 开启 **IMAP/SMTP服务**
5. 按提示发送短信，获取 **授权码**
6. 保存授权码（16位字符）

#### Gmail应用专用密码获取
1. 开启 [两步验证](https://myaccount.google.com/security)
2. 访问 [应用专用密码](https://myaccount.google.com/apppasswords)
3. 选择 "邮件" 和 "其他设备"
4. 生成16位应用专用密码
5. 保存密码

#### Outlook邮箱配置
1. 直接使用您的Microsoft账户密码
2. 系统使用OAuth2/现代认证方式
3. 无需额外的应用专用密码
4. 如需启用POP/IMAP访问，请访问 [Outlook.com设置](https://go.microsoft.com/fwlink/p/?linkid=858201)
5. 在"转发和IMAP"中启用相应服务

#### 163邮箱授权码获取
1. 登录 [163邮箱](https://mail.163.com)
2. 点击 **设置** → **POP3/SMTP/IMAP**
3. 开启 **SMTP服务**
4. 按提示获取授权码
5. 保存授权码

### 第三步：配置邮件设置

编辑 `server/email-config.php` 文件：

```php
<?php
// 邮件配置示例 (QQ邮箱)
$emailConfig = [
    'provider' => 'qq',  // 使用QQ邮箱
    
    'qq' => [
        'smtp_host' => 'smtp.qq.com',
        'smtp_port' => 587,
        'smtp_secure' => 'tls',
        'username' => '<EMAIL>',     // 您的QQ邮箱
        'password' => 'your-auth-code',        // QQ邮箱授权码
        'from_email' => '<EMAIL>',   // 发件人邮箱
        'from_name' => '倒计时系统'
    ]
];
?>
```

### 第四步：测试邮件配置

1. 访问 `test/test-email-config.html`
2. 填写邮箱信息
3. 点击 "检查邮件配置"
4. 点击 "发送测试邮件"
5. 检查收件箱

## 📋 详细配置说明

### QQ邮箱配置
```php
'qq' => [
    'smtp_host' => 'smtp.qq.com',
    'smtp_port' => 587,
    'smtp_secure' => 'tls',
    'username' => '<EMAIL>',
    'password' => 'your-auth-code',  // 授权码，不是QQ密码
    'from_email' => '<EMAIL>',
    'from_name' => '倒计时系统'
]
```

### Gmail配置
```php
'gmail' => [
    'smtp_host' => 'smtp.gmail.com',
    'smtp_port' => 587,
    'smtp_secure' => 'tls',
    'username' => '<EMAIL>',
    'password' => 'your-app-password',  // 应用专用密码
    'from_email' => '<EMAIL>',
    'from_name' => '倒计时系统'
]
```

### Outlook配置
```php
'outlook' => [
    'smtp_host' => 'smtp-mail.outlook.com',
    'smtp_port' => 587,
    'smtp_secure' => 'tls',
    'username' => '<EMAIL>',  // 支持@outlook.com, @hotmail.com等
    'password' => 'your-microsoft-password', // Microsoft账户密码
    'from_email' => '<EMAIL>',
    'from_name' => '倒计时系统'
]
```

### 163邮箱配置
```php
'163' => [
    'smtp_host' => 'smtp.163.com',
    'smtp_port' => 25,
    'smtp_secure' => false,
    'username' => '<EMAIL>',
    'password' => 'your-auth-code',  // 授权码
    'from_email' => '<EMAIL>',
    'from_name' => '倒计时系统'
]
```

## 🔧 故障排除

### 常见问题

#### 1. "邮件配置无效"
- 检查邮箱地址格式
- 确认授权码正确
- 验证SMTP服务器设置

#### 2. "SMTP连接失败"
- 检查网络连接
- 确认SMTP端口未被阻塞
- 尝试不同的端口 (25, 465, 587)

#### 3. "认证失败"
- 确认使用授权码而不是登录密码
- 检查邮箱是否开启SMTP服务
- 验证用户名格式

#### 4. "邮件发送失败"
- 检查收件人邮箱格式
- 确认发件人邮箱有效
- 查看错误日志 `server/password_reset.log`

### 调试步骤

1. **检查配置文件**
   ```bash
   # 确认配置文件存在且可读
   ls -la server/email-config.php
   ```

2. **测试网络连接**
   ```bash
   # 测试SMTP服务器连接
   telnet smtp.qq.com 587
   ```

3. **查看日志文件**
   ```bash
   # 查看邮件发送日志
   tail -f server/password_reset.log
   tail -f server/email_test.log
   ```

4. **使用测试工具**
   - 访问 `test/test-email-config.html`
   - 使用 `api/test-email.php` API

## 📊 邮件模板

系统发送的密码重置邮件包含：
- 美观的HTML格式
- 重置密码按钮
- 安全提醒信息
- 30分钟有效期说明
- 系统品牌信息

## 🔒 安全建议

1. **保护授权码**
   - 不要在代码中硬编码授权码
   - 使用环境变量或配置文件
   - 定期更换授权码

2. **邮件安全**
   - 使用HTTPS链接
   - 设置合理的有效期
   - 记录邮件发送日志

3. **服务器安全**
   - 保护配置文件不被Web访问
   - 使用TLS/SSL加密连接
   - 监控邮件发送频率

## 📞 技术支持

如果遇到问题：
1. 查看 `server/password_reset.log` 日志
2. 使用 `test/test-email-config.html` 测试
3. 检查邮件服务商的SMTP设置
4. 确认防火墙和网络配置

---

**配置完成后，用户就可以通过密码找回功能收到重置邮件了！** 📧✨
