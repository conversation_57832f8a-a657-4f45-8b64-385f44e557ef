<?php
/**
 * 多品牌路由处理器
 * 处理品牌URL路由和重定向
 */

// 获取请求URI
$requestUri = $_SERVER['REQUEST_URI'] ?? '';
$requestPath = parse_url($requestUri, PHP_URL_PATH);

// 引入品牌配置
require_once 'config/brands.php';

// 解析品牌路径
function routeBrandRequest($path) {
    // 移除开头的斜杠
    $path = ltrim($path, '/');
    
    // 分割路径
    $segments = explode('/', $path);
    
    if (empty($segments[0])) {
        // 根目录访问，重定向到品牌选择页面
        header('Location: /brand-selector.html');
        exit;
    }
    
    $brandPath = $segments[0];
    $page = $segments[1] ?? 'index.php';
    
    // 验证品牌是否有效
    if (!isValidBrand($brandPath)) {
        // 无效品牌，重定向到品牌选择页面
        header('Location: /brand-selector.html');
        exit;
    }
    
    // 根据页面类型包含相应文件
    switch ($page) {
        case 'index.php':
        case '':
            // 设置品牌环境变量
            $_SERVER['BRAND_PATH'] = $brandPath;
            include 'index.php';
            break;
            
        case 'index-tv-optimized.php':
            // 设置品牌环境变量
            $_SERVER['BRAND_PATH'] = $brandPath;
            include 'index-tv-optimized.php';
            break;
            
        default:
            // 未知页面，返回404
            http_response_code(404);
            header('Location: /brand-selector.html');
            exit;
    }
}

// 检查是否通过查询参数指定品牌
if (isset($_GET['brand'])) {
    $brandPath = $_GET['brand'];
    $page = $_GET['page'] ?? 'index.php';

    if (isValidBrand($brandPath)) {
        $_SERVER['BRAND_PATH'] = $brandPath;

        switch ($page) {
            case 'index.php':
            case 'standard':
                include 'index.php';
                break;

            case 'index-tv-optimized.php':
            case 'tv':
                include 'index-tv-optimized.php';
                break;

            default:
                include 'index.php';
                break;
        }
        exit;
    }
}

// 检查是否为品牌路由请求
if (preg_match('/^\/([a-zA-Z0-9_-]+)\/(index\.php|index-tv-optimized\.php)?/', $requestPath, $matches)) {
    routeBrandRequest($requestPath);
} else {
    // 非品牌路由，检查是否为根目录
    if ($requestPath === '/' || $requestPath === '') {
        header('Location: /brand-selector.html');
        exit;
    }

    // 其他请求，继续正常处理
    // 这里可以添加其他路由逻辑
}
?>
