<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>163邮箱配置向导</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        input { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; }
        button { background: #007bff; color: white; border: none; padding: 12px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .step { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .step h3 { margin-top: 0; color: #007bff; }
        code { background: #f8f9fa; padding: 2px 5px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 163邮箱配置向导</h1>
        
        <div class="info">
            <strong>📋 配置步骤概览：</strong><br>
            1. 确认163邮箱已开启SMTP服务<br>
            2. 获取163邮箱授权码<br>
            3. 配置邮件系统<br>
            4. 测试邮件发送
        </div>

        <div class="step">
            <h3>🔧 第一步：163邮箱SMTP设置</h3>
            <div class="warning">
                <strong>⚠️ 重要提醒：</strong><br>
                • 163邮箱需要使用<strong>授权码</strong>，不是登录密码<br>
                • 必须先在163邮箱中开启POP3/SMTP服务<br>
                • 从您的截图看，您已经开启了服务并生成了授权码
            </div>
            
            <p><strong>163邮箱SMTP配置信息：</strong></p>
            <ul>
                <li>SMTP服务器: <code>smtp.163.com</code></li>
                <li>SMTP端口: <code>25</code> (无加密)</li>
                <li>认证方式: 用户名+授权码</li>
                <li>加密方式: 无需SSL/TLS</li>
            </ul>
        </div>

        <div class="step">
            <h3>📝 第二步：填写163邮箱信息</h3>
            <label>163邮箱地址:</label>
            <input type="email" id="email163" placeholder="例如: <EMAIL>">
            
            <label>163邮箱授权码:</label>
            <input type="password" id="authCode" placeholder="从163邮箱设置中获取的授权码">
            
            <div class="info">
                <strong>💡 如何获取163邮箱授权码：</strong><br>
                1. 登录163邮箱网页版<br>
                2. 进入"设置" → "POP3/SMTP/IMAP"<br>
                3. 开启"POP3/SMTP服务"<br>
                4. 点击"新增授权密码"生成授权码<br>
                5. 复制生成的授权码（不是登录密码）
            </div>
        </div>

        <div class="step">
            <h3>⚙️ 第三步：应用配置</h3>
            <button onclick="applyConfig()">应用163邮箱配置</button>
            <button onclick="testConfig()">测试邮件配置</button>
            <button onclick="sendTestEmail()">发送测试邮件</button>
        </div>

        <div id="result"></div>

        <div class="step">
            <h3>🔍 故障排除</h3>
            <div class="warning">
                <strong>常见问题：</strong><br>
                • <strong>认证失败</strong>：检查授权码是否正确，不要使用登录密码<br>
                • <strong>连接超时</strong>：检查网络连接和防火墙设置<br>
                • <strong>发送失败</strong>：确认163邮箱已开启SMTP服务<br>
                • <strong>被拒绝</strong>：检查发件人邮箱地址是否正确
            </div>
        </div>
    </div>

    <script>
        async function applyConfig() {
            const email = document.getElementById('email163').value;
            const authCode = document.getElementById('authCode').value;
            const resultDiv = document.getElementById('result');
            
            if (!email || !authCode) {
                resultDiv.innerHTML = '<div class="error">❌ 请填写完整的邮箱地址和授权码</div>';
                return;
            }
            
            if (!email.includes('@163.com')) {
                resultDiv.innerHTML = '<div class="error">❌ 请输入有效的163邮箱地址</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">🔄 正在应用配置...</div>';
            
            try {
                const response = await fetch('../api/update-email-config.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        provider: '163',
                        email: email,
                        password: authCode
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ 163邮箱配置成功！<br>
                        邮箱: ${email}<br>
                        SMTP: smtp.163.com:25<br>
                        现在可以测试邮件发送了</div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 配置失败: ${data.message}</div>`;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
            }
        }
        
        async function testConfig() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">🔄 测试邮件配置...</div>';
            
            try {
                const response = await fetch('../api/test-email.php?action=check');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ 邮件配置测试成功！<br>
                        服务商: ${data.data.provider}<br>
                        SMTP: ${data.data.smtp_host}<br>
                        发件人: ${data.data.from_email}</div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 配置测试失败: ${data.message}</div>`;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }
        
        async function sendTestEmail() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">🔄 发送测试邮件...</div>';
            
            try {
                const response = await fetch('../api/test-email.php?action=test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ 测试邮件发送成功！<br>
                        收件人: <EMAIL><br>
                        请检查邮箱收件箱</div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 发送失败: ${data.message}</div>`;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 发送失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
