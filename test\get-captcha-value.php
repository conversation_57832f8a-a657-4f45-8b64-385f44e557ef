<?php
/**
 * 获取当前会话的验证码值（用于测试）
 */

session_start();
$dbPath = '../server/user_system.db3';

try {
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $sessionId = session_id();
    
    echo "<h2>验证码调试信息</h2>";
    echo "<p><strong>当前会话ID:</strong> $sessionId</p>";
    
    // 查询当前会话的验证码
    $stmt = $pdo->prepare("
        SELECT code_value, code_hash, expires_at, is_used, created_at 
        FROM captcha_codes 
        WHERE session_id = ? 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$sessionId]);
    $captchas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($captchas)) {
        echo "<p>❌ 没有找到当前会话的验证码</p>";
        echo "<p>请先访问验证码API生成验证码：<a href='../api/captcha.php' target='_blank'>生成验证码</a></p>";
    } else {
        echo "<h3>验证码列表（最新5个）：</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>验证码值</th><th>哈希值</th><th>过期时间</th><th>是否使用</th><th>创建时间</th></tr>";
        
        foreach ($captchas as $captcha) {
            $isExpired = strtotime($captcha['expires_at']) < time();
            $status = $captcha['is_used'] ? '已使用' : ($isExpired ? '已过期' : '有效');
            $rowColor = $captcha['is_used'] ? '#ffcccc' : ($isExpired ? '#ffffcc' : '#ccffcc');
            
            echo "<tr style='background-color: $rowColor;'>";
            echo "<td><strong>{$captcha['code_value']}</strong></td>";
            echo "<td>" . substr($captcha['code_hash'], 0, 16) . "...</td>";
            echo "<td>{$captcha['expires_at']}</td>";
            echo "<td>$status</td>";
            echo "<td>{$captcha['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 显示最新的有效验证码
        $validCaptcha = null;
        foreach ($captchas as $captcha) {
            if (!$captcha['is_used'] && strtotime($captcha['expires_at']) > time()) {
                $validCaptcha = $captcha;
                break;
            }
        }
        
        if ($validCaptcha) {
            echo "<div style='background: #d4edda; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
            echo "<h3>✅ 当前有效验证码：</h3>";
            echo "<p style='font-size: 24px; font-weight: bold; color: #155724;'>{$validCaptcha['code_value']}</p>";
            echo "<p>过期时间：{$validCaptcha['expires_at']}</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
            echo "<h3>❌ 没有有效的验证码</h3>";
            echo "<p>请重新生成验证码：<a href='../api/captcha.php' target='_blank'>点击生成</a></p>";
            echo "</div>";
        }
    }
    
    echo "<hr>";
    echo "<h3>操作链接：</h3>";
    echo "<p><a href='../api/captcha.php' target='_blank'>生成新验证码</a></p>";
    echo "<p><a href='test-forgot-password-simple.html'>返回密码找回测试</a></p>";
    echo "<p><a href='javascript:location.reload()'>刷新此页面</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ 数据库错误: " . $e->getMessage() . "</p>";
}
?>
