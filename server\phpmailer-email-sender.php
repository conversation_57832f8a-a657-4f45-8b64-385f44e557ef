<?php
/**
 * 基于PHPMailer的邮件发送器
 * 使用本项目内的PHPMailer依赖
 */

// 引入PHPMailer (使用本项目的vendor)
require_once '../vendor/autoload.php';

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class PHPMailerEmailSender
{
    private $mail;
    private $config;
    
    public function __construct()
    {
        // 加载邮件配置 (使用本项目的配置)
        require_once 'email-config.php';
        $this->config = getEmailConfig();
        $this->mail = new PHPMailer(true);
        $this->setupSMTP();
    }
    
    /**
     * 设置SMTP配置
     */
    private function setupSMTP()
    {
        try {
            // 服务器设置
            $this->mail->isSMTP();
            $this->mail->Host = $this->config['smtp_host'];
            $this->mail->SMTPAuth = true;
            $this->mail->Username = $this->config['smtp_username'];
            $this->mail->Password = $this->config['smtp_password'];
            $this->mail->SMTPSecure = $this->config['smtp_secure'];
            $this->mail->Port = $this->config['smtp_port'];
            
            // 字符集设置
            $this->mail->CharSet = 'UTF-8';
            
            // 调试设置
            $this->mail->SMTPDebug = 0; // 生产环境关闭调试
            
        } catch (Exception $e) {
            throw new Exception("SMTP配置失败: " . $e->getMessage());
        }
    }
    
    /**
     * 发送邮件
     * @param string $to 收件人邮箱
     * @param string $subject 邮件主题
     * @param string $body 邮件内容
     * @param bool $isHTML 是否为HTML格式
     * @return array 发送结果
     */
    public function sendEmail($to, $subject, $body, $isHTML = true)
    {
        try {
            // 清除之前的收件人
            $this->mail->clearAddresses();
            $this->mail->clearAttachments();
            
            // 发件人设置
            $this->mail->setFrom($this->config['from_email'], $this->config['from_name']);
            
            // 收件人设置
            $this->mail->addAddress($to);
            
            // 邮件内容设置
            $this->mail->isHTML($isHTML);
            $this->mail->Subject = $subject;
            $this->mail->Body = $body;
            
            // 发送邮件
            $this->mail->send();
            
            return [
                'success' => true,
                'message' => '邮件发送成功'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '邮件发送失败: ' . $e->getMessage(),
                'debug' => [
                    'smtp_host' => $this->config['smtp_host'],
                    'smtp_port' => $this->config['smtp_port'],
                    'smtp_username' => $this->config['smtp_username'],
                    'error' => $e->getMessage()
                ]
            ];
        }
    }
    
    /**
     * 发送密码重置邮件
     * @param string $email 收件人邮箱
     * @param string $username 用户名
     * @param string $resetToken 重置令牌
     * @return array 发送结果
     */
    public function sendPasswordResetEmail($email, $username, $resetToken)
    {
        $resetLink = "http://" . ($_SERVER['HTTP_HOST'] ?? 'localhost') . "/reset-password.html?token=" . $resetToken;
        
        $subject = '🔐 密码重置请求 - 倒计时系统';
        
        $body = "
        <html>
        <head>
            <meta charset='UTF-8'>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
                .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
                .button { background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; margin: 20px 0; }
                .warning { background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 20px 0; }
                .footer { color: #666; font-size: 14px; text-align: center; margin-top: 30px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>🔐 密码重置请求</h1>
                </div>
                
                <div class='content'>
                    <p>亲爱的 <strong>{$username}</strong>，</p>
                    
                    <p>我们收到了您的密码重置请求。如果这是您本人的操作，请点击下面的按钮重置密码：</p>
                    
                    <div style='text-align: center;'>
                        <a href='{$resetLink}' class='button'>立即重置密码</a>
                    </div>
                    
                    <div class='warning'>
                        <strong>⚠️ 重要提醒：</strong><br>
                        • 此链接将在 <strong>30分钟</strong> 后失效<br>
                        • 如果您没有请求重置密码，请忽略此邮件<br>
                        • 为了您的账户安全，请不要将此链接分享给他人
                    </div>
                    
                    <p><strong>如果上面的按钮无法点击，请复制以下链接到浏览器地址栏：</strong></p>
                    <p style='background: #e9ecef; padding: 15px; border-radius: 5px; word-break: break-all; font-family: monospace;'>
                        {$resetLink}
                    </p>
                    
                    <div class='footer'>
                        <p>此邮件由倒计时系统自动发送，请勿回复。<br>
                        发送时间: " . date('Y-m-d H:i:s') . "</p>
                    </div>
                </div>
            </div>
        </body>
        </html>
        ";
        
        return $this->sendEmail($email, $subject, $body, true);
    }
}
?>
