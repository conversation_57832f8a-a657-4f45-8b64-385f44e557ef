<?php
/**
 * 直接测试密码找回API
 */

session_start();

echo "<h2>直接API测试</h2>";
echo "<p><strong>当前会话ID:</strong> " . session_id() . "</p>";

// 先生成验证码
echo "<h3>1. 生成验证码</h3>";
echo "<p><a href='../api/captcha.php' target='_blank'>点击生成验证码</a></p>";

// 检查当前会话的验证码
$dbPath = '../server/user_system.db3';
try {
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $sessionId = session_id();
    $stmt = $pdo->prepare("
        SELECT code_value, expires_at 
        FROM captcha_codes 
        WHERE session_id = ? AND is_used = 0 AND expires_at > datetime('now')
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$sessionId]);
    $captcha = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($captcha) {
        echo "<div style='background: #d4edda; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3>✅ 当前有效验证码：</h3>";
        echo "<p style='font-size: 24px; font-weight: bold; color: #155724;'>{$captcha['code_value']}</p>";
        echo "<p>过期时间：{$captcha['expires_at']}</p>";
        echo "</div>";
        
        // 显示测试表单
        echo "<h3>2. 测试密码找回API</h3>";
        echo "<form method='post' action='../api/forgot-password.php' target='_blank'>";
        echo "<p>用户名：<input type='text' name='username' value='ataehee1' style='padding: 5px; margin: 5px;'></p>";
        echo "<p>邮箱：<input type='email' name='email' value='<EMAIL>' style='padding: 5px; margin: 5px;'></p>";
        echo "<p>验证码：<input type='text' name='captcha' value='{$captcha['code_value']}' style='padding: 5px; margin: 5px;'></p>";
        echo "<p><input type='submit' value='发送重置邮件' style='padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;'></p>";
        echo "</form>";
        
        // JavaScript测试
        echo "<h3>3. JavaScript测试</h3>";
        echo "<button onclick='testWithJS()' style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;'>使用JavaScript测试</button>";
        echo "<div id='jsResult' style='margin-top: 20px;'></div>";
        
        echo "<script>
        async function testWithJS() {
            const resultDiv = document.getElementById('jsResult');
            resultDiv.innerHTML = '测试中...';
            
            try {
                const formData = new FormData();
                formData.append('username', 'ataehee1');
                formData.append('email', '<EMAIL>');
                formData.append('captcha', '{$captcha['code_value']}');
                
                const response = await fetch('../api/forgot-password.php', {
                    method: 'POST',
                    body: formData
                });
                
                const responseText = await response.text();
                console.log('响应状态:', response.status);
                console.log('响应内容:', responseText);
                
                resultDiv.innerHTML = '<h4>响应结果：</h4><pre>' + responseText + '</pre>';
                
                if (response.ok) {
                    resultDiv.style.background = '#d4edda';
                    resultDiv.style.color = '#155724';
                } else {
                    resultDiv.style.background = '#f8d7da';
                    resultDiv.style.color = '#721c24';
                }
                resultDiv.style.padding = '15px';
                resultDiv.style.borderRadius = '5px';
                
            } catch (error) {
                console.error('请求错误:', error);
                resultDiv.innerHTML = '<h4>错误：</h4><p>' + error.message + '</p>';
                resultDiv.style.background = '#f8d7da';
                resultDiv.style.color = '#721c24';
                resultDiv.style.padding = '15px';
                resultDiv.style.borderRadius = '5px';
            }
        }
        </script>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3>❌ 没有有效验证码</h3>";
        echo "<p>请先生成验证码，然后刷新此页面</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ 数据库错误: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='javascript:location.reload()'>刷新页面</a></p>";
?>
