<?php
/**
 * CSRF保护工具函数
 */

/**
 * 生成CSRF令牌
 */
function generateCSRFToken() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    $token = bin2hex(random_bytes(32));
    $_SESSION['csrf_token'] = $token;
    $_SESSION['csrf_token_time'] = time();
    
    return $token;
}

/**
 * 验证CSRF令牌
 */
function verifyCSRFToken($token) {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // 检查令牌是否存在
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
        return false;
    }
    
    // 检查令牌是否过期 (30分钟)
    if (time() - $_SESSION['csrf_token_time'] > 1800) {
        unset($_SESSION['csrf_token']);
        unset($_SESSION['csrf_token_time']);
        return false;
    }
    
    // 使用hash_equals防止时序攻击
    $isValid = hash_equals($_SESSION['csrf_token'], $token);
    
    if ($isValid) {
        // 令牌使用后立即失效
        unset($_SESSION['csrf_token']);
        unset($_SESSION['csrf_token_time']);
    }
    
    return $isValid;
}

/**
 * 获取当前CSRF令牌
 */
function getCSRFToken() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // 如果没有令牌或已过期，生成新的
    if (!isset($_SESSION['csrf_token']) || 
        !isset($_SESSION['csrf_token_time']) || 
        time() - $_SESSION['csrf_token_time'] > 1800) {
        return generateCSRFToken();
    }
    
    return $_SESSION['csrf_token'];
}

/**
 * 输出CSRF令牌的隐藏表单字段
 */
function csrfTokenField() {
    $token = getCSRFToken();
    return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token, ENT_QUOTES, 'UTF-8') . '">';
}

/**
 * 验证请求中的CSRF令牌
 */
function validateCSRFFromRequest() {
    $token = null;
    
    // 从POST数据中获取令牌
    if (isset($_POST['csrf_token'])) {
        $token = $_POST['csrf_token'];
    }
    // 从JSON数据中获取令牌
    elseif ($_SERVER['CONTENT_TYPE'] === 'application/json') {
        $input = json_decode(file_get_contents('php://input'), true);
        if (isset($input['csrf_token'])) {
            $token = $input['csrf_token'];
        }
    }
    // 从HTTP头中获取令牌
    elseif (isset($_SERVER['HTTP_X_CSRF_TOKEN'])) {
        $token = $_SERVER['HTTP_X_CSRF_TOKEN'];
    }
    
    if (!$token) {
        throw new Exception('缺少CSRF令牌');
    }
    
    if (!verifyCSRFToken($token)) {
        throw new Exception('CSRF令牌验证失败');
    }
    
    return true;
}
?>
