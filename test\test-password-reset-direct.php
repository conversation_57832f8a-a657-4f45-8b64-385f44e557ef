<?php
/**
 * 直接测试密码找回核心功能
 */

echo "🔐 直接测试密码找回核心功能...\n\n";

// 包含必要的文件
require_once '../server/email-config.php';
require_once '../server/email-sender.php';

// 测试数据
$username = 'ataehee1';
$email = '<EMAIL>';

echo "1. 测试用户验证:\n";
echo "==================\n";

try {
    // 连接数据库
    $db = new PDO('sqlite:../server/user_system.db3');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 查询用户
    $stmt = $db->prepare("SELECT id, username, email FROM users WHERE username = ? AND email = ?");
    $stmt->execute([$username, $email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "✅ 用户验证成功\n";
        echo "   用户ID: " . $user['id'] . "\n";
        echo "   用户名: " . $user['username'] . "\n";
        echo "   邮箱: " . $user['email'] . "\n";
    } else {
        echo "❌ 用户不存在或邮箱不匹配\n";
        exit(1);
    }
    
} catch (Exception $e) {
    echo "❌ 数据库查询失败: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n" . str_repeat("=", 50) . "\n\n";

echo "2. 生成重置令牌:\n";
echo "==================\n";

try {
    // 生成重置令牌
    $resetToken = bin2hex(random_bytes(32));
    $expiresAt = date('Y-m-d H:i:s', time() + 24 * 3600); // 24小时后过期
    
    echo "✅ 重置令牌生成成功\n";
    echo "   令牌: " . substr($resetToken, 0, 16) . "...\n";
    echo "   过期时间: $expiresAt\n";
    
    // 保存到数据库（适配现有表结构）
    $stmt = $db->prepare("
        INSERT OR REPLACE INTO password_resets (user_id, username, email, reset_token, expires_at, created_at, is_used)
        VALUES (?, ?, ?, ?, ?, ?, 0)
    ");
    $stmt->execute([$user['id'], $user['username'], $user['email'], $resetToken, $expiresAt, date('Y-m-d H:i:s')]);
    
    echo "✅ 令牌已保存到数据库\n";
    
} catch (Exception $e) {
    echo "❌ 令牌生成失败: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n" . str_repeat("=", 50) . "\n\n";

echo "3. 发送重置邮件:\n";
echo "==================\n";

try {
    $emailSender = new EmailSender();
    
    // 构造重置链接
    $resetLink = "http://localhost/reset-password.php?token=" . $resetToken;
    
    // 邮件主题和内容
    $subject = '🔐 密码重置请求 - 倒计时系统';
    
    $body = "
    <html>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
            .button { background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; display: inline-block; margin: 20px 0; }
            .warning { background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 20px 0; }
            .footer { color: #666; font-size: 14px; text-align: center; margin-top: 30px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>🔐 密码重置请求</h1>
            </div>
            
            <div class='content'>
                <p>亲爱的 <strong>{$username}</strong>，</p>
                
                <p>我们收到了您的密码重置请求。如果这是您本人的操作，请点击下面的按钮重置密码：</p>
                
                <div style='text-align: center;'>
                    <a href='{$resetLink}' class='button'>立即重置密码</a>
                </div>
                
                <div class='warning'>
                    <strong>⚠️ 重要提醒：</strong><br>
                    • 此链接将在 <strong>24小时</strong> 后失效<br>
                    • 如果您没有请求重置密码，请忽略此邮件<br>
                    • 为了您的账户安全，请不要将此链接分享给他人
                </div>
                
                <p><strong>如果上面的按钮无法点击，请复制以下链接到浏览器地址栏：</strong></p>
                <p style='background: #e9ecef; padding: 15px; border-radius: 5px; word-break: break-all; font-family: monospace;'>
                    {$resetLink}
                </p>
                
                <div class='footer'>
                    <p>此邮件由倒计时系统自动发送，请勿回复。<br>
                    发送时间: " . date('Y-m-d H:i:s') . "</p>
                </div>
            </div>
        </div>
    </body>
    </html>
    ";
    
    echo "发送密码重置邮件...\n";
    echo "收件人: $email\n";
    echo "主题: $subject\n";
    echo "重置链接: $resetLink\n\n";
    
    $result = $emailSender->sendEmail($email, $subject, $body, true);
    
    if ($result['success']) {
        echo "✅ 密码重置邮件发送成功！\n";
        echo "   消息: " . $result['message'] . "\n";
        echo "   请检查邮箱: $email\n";
        
        // 记录日志
        $logMessage = date('Y-m-d H:i:s') . " - 密码重置邮件发送成功 - 用户: $username, 邮箱: $email, 令牌: " . substr($resetToken, 0, 8) . "...\n";
        file_put_contents('../server/password_reset.log', $logMessage, FILE_APPEND);
        
    } else {
        echo "❌ 邮件发送失败: " . $result['message'] . "\n";
        if (isset($result['debug'])) {
            echo "   调试信息: " . $result['debug'] . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ 发送异常: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 50) . "\n\n";

echo "4. 验证数据库记录:\n";
echo "==================\n";

try {
    // 查询重置记录（适配现有表结构）
    $stmt = $db->prepare("
        SELECT * FROM password_resets WHERE reset_token = ?
    ");
    $stmt->execute([$resetToken]);
    $resetRecord = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($resetRecord) {
        echo "✅ 数据库记录验证成功\n";
        echo "   用户: " . $resetRecord['username'] . "\n";
        echo "   邮箱: " . $resetRecord['email'] . "\n";
        echo "   令牌: " . substr($resetRecord['reset_token'], 0, 16) . "...\n";
        echo "   创建时间: " . $resetRecord['created_at'] . "\n";
        echo "   过期时间: " . $resetRecord['expires_at'] . "\n";
        echo "   状态: " . ($resetRecord['is_used'] ? '已使用' : '未使用') . "\n";
    } else {
        echo "❌ 数据库记录未找到\n";
    }
    
} catch (Exception $e) {
    echo "❌ 数据库验证失败: " . $e->getMessage() . "\n";
}

echo "\n🎉 密码找回功能测试完成！\n";
echo "📧 如果邮件发送成功，请检查邮箱 $email 的收件箱\n";
echo "🔗 重置链接: $resetLink\n";
?>
