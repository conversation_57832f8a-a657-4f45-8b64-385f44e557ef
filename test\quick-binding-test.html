<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速绑定测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
        .error-box { background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        input[type="text"] { padding: 8px; margin: 5px; width: 300px; }
        #result { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>🧪 快速绑定测试</h1>
    <p><strong>测试时间:</strong> <span id="currentTime"></span></p>
    
    <div class="section">
        <h2>🔧 测试配置</h2>
        <p>
            <label>激活码: </label>
            <input type="text" id="activationCode" value="N3WK7-7YJW7-JHXB4-NWWEJ" placeholder="输入激活码">
        </p>
        <p>
            <label>会话令牌: </label>
            <input type="text" id="sessionToken" value="" placeholder="从浏览器获取session_token">
        </p>
        <p>
            <button onclick="testBinding()">🚀 测试绑定API</button>
            <button onclick="getSessionFromStorage()">📱 从localStorage获取session</button>
            <button onclick="clearResult()">🗑️ 清除结果</button>
        </p>
    </div>
    
    <div id="result"></div>
    
    <div class="section">
        <h2>📋 使用说明</h2>
        <ol>
            <li>确保已经登录系统（在另一个标签页打开 new-lock.html 并登录）</li>
            <li>点击"从localStorage获取session"按钮自动获取会话令牌</li>
            <li>确认激活码是 N3WK7-7YJW7-JHXB4-NWWEJ</li>
            <li>点击"测试绑定API"按钮</li>
            <li>查看返回结果</li>
        </ol>
    </div>

    <script>
        // 更新当前时间
        document.getElementById('currentTime').textContent = new Date().toLocaleString();
        
        // 从localStorage获取session token
        function getSessionFromStorage() {
            const sessionToken = localStorage.getItem('session_token');
            if (sessionToken) {
                document.getElementById('sessionToken').value = sessionToken;
                showResult('✅ 成功获取session token: ' + sessionToken.substring(0, 20) + '...', 'success');
            } else {
                showResult('❌ 未找到session token，请先登录系统', 'error');
            }
        }
        
        // 测试绑定API
        async function testBinding() {
            const activationCode = document.getElementById('activationCode').value.trim();
            const sessionToken = document.getElementById('sessionToken').value.trim();
            
            if (!activationCode) {
                showResult('❌ 请输入激活码', 'error');
                return;
            }
            
            if (!sessionToken) {
                showResult('❌ 请输入会话令牌', 'error');
                return;
            }
            
            showResult('🔄 正在测试绑定API...', 'info');
            
            try {
                const response = await fetch('../api/bind-activation.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        activation_code: activationCode,
                        session_token: sessionToken
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult('🎉 绑定成功！<br>' + 
                              '消息: ' + result.message + '<br>' +
                              '响应: ' + JSON.stringify(result, null, 2), 'success');
                } else {
                    showResult('❌ 绑定失败<br>' + 
                              '错误: ' + result.message + '<br>' +
                              '响应: ' + JSON.stringify(result, null, 2), 'error');
                }
            } catch (error) {
                showResult('❌ 请求失败: ' + error.message, 'error');
            }
        }
        
        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            let className = '';
            
            switch(type) {
                case 'success':
                    className = 'highlight';
                    break;
                case 'error':
                    className = 'error-box';
                    break;
                case 'info':
                    className = 'section';
                    break;
            }
            
            resultDiv.innerHTML = `<div class="${className}"><h3>测试结果</h3><p>${message}</p></div>`;
        }
        
        // 清除结果
        function clearResult() {
            document.getElementById('result').innerHTML = '';
        }
        
        // 页面加载时自动获取session
        window.onload = function() {
            getSessionFromStorage();
        };
    </script>
</body>
</html>
