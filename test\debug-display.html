<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>显示问题调试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html {
            width: 100vw !important;
            height: 100vh !important;
        }
        
        body {
            width: 100vw !important;
            height: 100vh !important;
            background: linear-gradient(135deg, #ff4757 0%, #ff3838 25%, #ff6b7a 50%, #ff4757 75%, #ff2f3a 100%) !important;
            font-family: Arial, sans-serif;
            color: white;
            overflow: hidden;
            position: relative;
        }
        
        .debug-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            font-size: 14px;
            line-height: 1.5;
            max-width: 400px;
            z-index: 1000;
        }
        
        .center-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 100;
        }
        
        .title {
            font-size: 4vw;
            margin-bottom: 2vh;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .instruction {
            font-size: 2vw;
            margin-bottom: 1vh;
        }
        
        .corner-markers {
            position: absolute;
            width: 50px;
            height: 50px;
            background: yellow;
            z-index: 200;
        }
        
        .corner-top-left {
            top: 0;
            left: 0;
        }
        
        .corner-top-right {
            top: 0;
            right: 0;
        }
        
        .corner-bottom-left {
            bottom: 0;
            left: 0;
        }
        
        .corner-bottom-right {
            bottom: 0;
            right: 0;
        }
        
        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        
        .refresh-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <!-- 四个角的标记 -->
    <div class="corner-markers corner-top-left"></div>
    <div class="corner-markers corner-top-right"></div>
    <div class="corner-markers corner-bottom-left"></div>
    <div class="corner-markers corner-bottom-right"></div>
    
    <div class="debug-panel" id="debugPanel">
        <h3>🔍 显示调试信息</h3>
        <div id="debugInfo">加载中...</div>
        <button class="refresh-btn" onclick="updateDebugInfo()">刷新信息</button>
    </div>
    
    <div class="center-content">
        <h1 class="title">显示问题调试</h1>
        <div class="instruction">如果您能看到四个角的黄色方块，说明页面是全屏的</div>
        <div class="instruction">如果只看到部分内容，说明存在显示问题</div>
    </div>

    <script>
        function updateDebugInfo() {
            const debugInfo = document.getElementById('debugInfo');
            const rect = document.body.getBoundingClientRect();
            
            debugInfo.innerHTML = `
                <strong>视口信息：</strong><br>
                内部尺寸: ${window.innerWidth} x ${window.innerHeight}<br>
                外部尺寸: ${window.outerWidth} x ${window.outerHeight}<br>
                <br>
                <strong>屏幕信息：</strong><br>
                屏幕尺寸: ${screen.width} x ${screen.height}<br>
                可用尺寸: ${screen.availWidth} x ${screen.availHeight}<br>
                <br>
                <strong>元素尺寸：</strong><br>
                Body偏移: ${document.body.offsetWidth} x ${document.body.offsetHeight}<br>
                Body客户端: ${document.body.clientWidth} x ${document.body.clientHeight}<br>
                Body滚动: ${document.body.scrollWidth} x ${document.body.scrollHeight}<br>
                Body边界: ${rect.width} x ${rect.height}<br>
                <br>
                <strong>其他信息：</strong><br>
                设备像素比: ${window.devicePixelRatio}<br>
                缩放级别: ${Math.round(window.devicePixelRatio * 100)}%<br>
                文档就绪: ${document.readyState}<br>
                <br>
                <strong>浏览器信息：</strong><br>
                ${navigator.userAgent.substring(0, 60)}...
            `;
        }
        
        // 页面加载完成后更新调试信息
        window.addEventListener('load', updateDebugInfo);
        window.addEventListener('resize', updateDebugInfo);
        
        // 立即更新一次
        updateDebugInfo();
        
        // 每秒更新一次
        setInterval(updateDebugInfo, 1000);
    </script>
</body>
</html>
