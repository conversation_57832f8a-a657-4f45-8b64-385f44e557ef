<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试登录无验证码功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"], input[type="email"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .captcha-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .captcha-input {
            width: 120px !important;
        }
        .captcha-image {
            cursor: pointer;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>登录验证码功能测试</h1>
    
    <!-- 登录测试 (无验证码) -->
    <div class="test-container">
        <h2 class="test-title">🔑 登录测试 (已移除验证码)</h2>
        <div class="form-group">
            <label for="loginUsername">用户名:</label>
            <input type="text" id="loginUsername" placeholder="请输入用户名">
        </div>
        <div class="form-group">
            <label for="loginPassword">密码:</label>
            <input type="password" id="loginPassword" placeholder="请输入密码">
        </div>
        <button onclick="testLogin()">测试登录</button>
        <div id="loginResult" class="result" style="display: none;"></div>
    </div>

    <!-- 注册测试 (保留验证码) -->
    <div class="test-container">
        <h2 class="test-title">📝 注册测试 (保留验证码)</h2>
        <div class="form-group">
            <label for="regUsername">用户名:</label>
            <input type="text" id="regUsername" placeholder="请输入用户名">
        </div>
        <div class="form-group">
            <label for="regPassword">密码:</label>
            <input type="password" id="regPassword" placeholder="请输入密码">
        </div>
        <div class="form-group">
            <label for="regEmail">邮箱:</label>
            <input type="email" id="regEmail" placeholder="请输入邮箱">
        </div>
        <div class="form-group">
            <label for="regCaptcha">验证码:</label>
            <div class="captcha-group">
                <input type="text" id="regCaptcha" class="captcha-input" placeholder="验证码">
                <img id="regCaptchaImg" class="captcha-image" src="../api/captcha.php" alt="验证码" onclick="refreshCaptcha('reg')">
            </div>
        </div>
        <button onclick="testRegister()">测试注册</button>
        <div id="registerResult" class="result" style="display: none;"></div>
    </div>

    <!-- 找回密码测试 (保留验证码) -->
    <div class="test-container">
        <h2 class="test-title">🔄 找回密码测试 (保留验证码)</h2>
        <div class="form-group">
            <label for="forgotUsername">用户名:</label>
            <input type="text" id="forgotUsername" placeholder="请输入用户名">
        </div>
        <div class="form-group">
            <label for="forgotEmail">邮箱:</label>
            <input type="email" id="forgotEmail" placeholder="请输入邮箱">
        </div>
        <div class="form-group">
            <label for="forgotCaptcha">验证码:</label>
            <div class="captcha-group">
                <input type="text" id="forgotCaptcha" class="captcha-input" placeholder="验证码">
                <img id="forgotCaptchaImg" class="captcha-image" src="../api/captcha.php" alt="验证码" onclick="refreshCaptcha('forgot')">
            </div>
        </div>
        <button onclick="testForgotPassword()">测试找回密码</button>
        <div id="forgotResult" class="result" style="display: none;"></div>
    </div>

    <script>
        // 刷新验证码
        function refreshCaptcha(type) {
            const img = document.getElementById(type + 'CaptchaImg');
            if (img) {
                img.src = '../api/captcha.php?' + Date.now();
            }
        }

        // 显示结果
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'result ' + type;
            element.style.display = 'block';
        }

        // 测试登录 (无验证码)
        async function testLogin() {
            const username = document.getElementById('loginUsername').value.trim();
            const password = document.getElementById('loginPassword').value.trim();

            if (!username || !password) {
                showResult('loginResult', '请填写用户名和密码', 'error');
                return;
            }

            try {
                const response = await fetch('../api/login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        device_info: 'Test Device'
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    showResult('loginResult', '✅ 登录成功！无需验证码验证', 'success');
                } else {
                    showResult('loginResult', '❌ 登录失败: ' + data.message, 'error');
                }
            } catch (error) {
                showResult('loginResult', '❌ 网络错误: ' + error.message, 'error');
            }
        }

        // 测试注册 (保留验证码)
        async function testRegister() {
            const username = document.getElementById('regUsername').value.trim();
            const password = document.getElementById('regPassword').value.trim();
            const email = document.getElementById('regEmail').value.trim();
            const captcha = document.getElementById('regCaptcha').value.trim();

            if (!username || !password || !email || !captcha) {
                showResult('registerResult', '请填写完整信息（包括验证码）', 'error');
                return;
            }

            try {
                const response = await fetch('../api/register.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        email: email,
                        captcha: captcha
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    showResult('registerResult', '✅ 注册成功！验证码验证正常', 'success');
                } else {
                    showResult('registerResult', '❌ 注册失败: ' + data.message, 'error');
                    refreshCaptcha('reg');
                }
            } catch (error) {
                showResult('registerResult', '❌ 网络错误: ' + error.message, 'error');
            }
        }

        // 测试找回密码 (保留验证码)
        async function testForgotPassword() {
            const username = document.getElementById('forgotUsername').value.trim();
            const email = document.getElementById('forgotEmail').value.trim();
            const captcha = document.getElementById('forgotCaptcha').value.trim();

            if (!username || !email || !captcha) {
                showResult('forgotResult', '请填写完整信息（包括验证码）', 'error');
                return;
            }

            try {
                const response = await fetch('../api/forgot-password.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        email: email,
                        captcha: captcha
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    showResult('forgotResult', '✅ 找回密码请求成功！验证码验证正常', 'success');
                } else {
                    showResult('forgotResult', '❌ 找回密码失败: ' + data.message, 'error');
                    refreshCaptcha('forgot');
                }
            } catch (error) {
                showResult('forgotResult', '❌ 网络错误: ' + error.message, 'error');
            }
        }

        // 页面加载时刷新验证码
        window.onload = function() {
            refreshCaptcha('reg');
            refreshCaptcha('forgot');
        };
    </script>
</body>
</html>
