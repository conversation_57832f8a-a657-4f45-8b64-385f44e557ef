<?php
// 创建测试激活码
$dbPath = '../server/user_system.db3';

try {
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 生成新的激活码
    $activationCode = 'TEST7-26ABC-DEFGH-IJKLM';
    
    // 检查激活码是否已存在
    $stmt = $pdo->prepare('SELECT id FROM activation_codes WHERE code = ?');
    $stmt->execute([$activationCode]);
    
    if ($stmt->fetch()) {
        echo "激活码 $activationCode 已存在<br>\n";
    } else {
        // 创建新的激活码
        $stmt = $pdo->prepare('
            INSERT INTO activation_codes (code, created_at, expires_at, is_used) 
            VALUES (?, datetime("now"), datetime("now", "+30 days"), 0)
        ');
        
        $stmt->execute([$activationCode]);
        
        echo "✅ 成功创建测试激活码<br>\n";
        echo "激活码: <strong>$activationCode</strong><br>\n";
        echo "有效期: 30天<br>\n";
        echo "状态: 未使用<br>\n";
    }
    
    echo "<br><a href='javascript:history.back()'>返回</a>";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage();
}
?>
