<?php
/**
 * 会话验证API
 * 验证用户会话是否有效，是否有激活码绑定
 */

session_start();
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 数据库配置
$dbPath = '../server/user_system.db3';

/**
 * 验证会话并检查激活状态
 */
function verifySessionAndActivation($sessionToken) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // 随机触发session自动清理（5%概率）
        require_once '../server/session-auto-cleaner.php';
        randomSessionCleanup($dbPath, 5);

        // 查询会话和激活码信息
        $stmt = $pdo->prepare("
            SELECT 
                us.user_id,
                us.session_token,
                us.expires_at as session_expires,
                us.last_access,
                u.username,
                u.status as user_status,
                ac.code as activation_code,
                ac.expires_at as activation_expires,
                ac.status as activation_status
            FROM user_sessions us
            JOIN users u ON us.user_id = u.id
            LEFT JOIN activation_codes ac ON ac.bound_user_id = u.id AND ac.status = 'used'
            WHERE us.session_token = ? 
            AND us.is_active = 1 
            AND us.expires_at > datetime('now')
            AND u.status = 'active'
        ");
        $stmt->execute([$sessionToken]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            // 更新最后访问时间
            $stmt = $pdo->prepare("
                UPDATE user_sessions 
                SET last_access = datetime('now') 
                WHERE session_token = ?
            ");
            $stmt->execute([$sessionToken]);
            
            // 检查激活码是否过期
            $hasValidActivation = false;
            if ($result['activation_code']) {
                $activationExpires = strtotime($result['activation_expires']);
                $hasValidActivation = $activationExpires > time();
            }
            
            return [
                'user_id' => $result['user_id'],
                'username' => $result['username'],
                'session_expires' => strtotime($result['session_expires']),
                'has_activation' => !empty($result['activation_code']),
                'activation_code' => $result['activation_code'],
                'activation_expires' => $result['activation_expires'] ? strtotime($result['activation_expires']) : null,
                'has_valid_activation' => $hasValidActivation,
                'last_access' => $result['last_access']
            ];
        }
        
        return false;
    } catch (Exception $e) {
        error_log("会话验证失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 获取会话令牌
 */
function getSessionToken() {
    // 优先从POST数据获取
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        if ($data && isset($data['session_token'])) {
            return trim($data['session_token']);
        }
    }
    
    // 从GET参数获取
    if (isset($_GET['session_token'])) {
        return trim($_GET['session_token']);
    }
    
    // 从Cookie获取
    if (isset($_COOKIE['session_token'])) {
        return trim($_COOKIE['session_token']);
    }
    
    // 从Header获取
    $headers = getallheaders();
    if (isset($headers['Authorization'])) {
        $auth = $headers['Authorization'];
        if (strpos($auth, 'Bearer ') === 0) {
            return trim(substr($auth, 7));
        }
    }
    
    return null;
}

try {
    // 获取会话令牌
    $sessionToken = getSessionToken();
    
    if (!$sessionToken) {
        throw new Exception('缺少会话令牌');
    }
    
    // 验证会话和激活状态
    $sessionInfo = verifySessionAndActivation($sessionToken);
    
    if (!$sessionInfo) {
        throw new Exception('会话无效或已过期');
    }
    
    // 检查是否需要激活码
    if (!$sessionInfo['has_valid_activation']) {
        echo json_encode([
            'success' => false,
            'message' => '需要绑定有效的激活码',
            'code' => 'NEED_ACTIVATION',
            'data' => [
                'user_id' => $sessionInfo['user_id'],
                'username' => $sessionInfo['username'],
                'has_activation' => $sessionInfo['has_activation'],
                'activation_expired' => $sessionInfo['has_activation'] && !$sessionInfo['has_valid_activation']
            ]
        ]);
        exit();
    }
    
    // 验证通过
    echo json_encode([
        'success' => true,
        'message' => '会话验证通过',
        'data' => [
            'user_id' => $sessionInfo['user_id'],
            'username' => $sessionInfo['username'],
            'session_expires' => $sessionInfo['session_expires'],
            'activation_code' => $sessionInfo['activation_code'],
            'activation_expires' => $sessionInfo['activation_expires'],
            'remaining_days' => ceil(($sessionInfo['activation_expires'] - time()) / 86400),
            'last_access' => $sessionInfo['last_access']
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'code' => 'SESSION_INVALID'
    ]);
}
?>
