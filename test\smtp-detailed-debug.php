<?php
/**
 * 详细的SMTP调试工具
 * 逐步执行SMTP命令并显示服务器响应
 */

header('Content-Type: text/html; charset=utf-8');

$testResult = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_smtp'])) {
    $testEmail = $_POST['test_email'] ?? '';
    $testResult = detailedSMTPTest($testEmail);
}

function detailedSMTPTest($toEmail) {
    $result = [
        'steps' => [],
        'success' => false,
        'error' => null
    ];
    
    try {
        // 加载配置
        require_once '../server/email-config.php';
        $config = getEmailConfig();
        
        $result['steps'][] = ['step' => '配置加载', 'status' => 'success', 'data' => [
            'smtp_host' => $config['smtp_host'],
            'smtp_port' => $config['smtp_port'],
            'from_email' => $config['from_email'],
            'username' => $config['username']
        ]];
        
        // 建立连接
        $context = stream_context_create([
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            ]
        ]);
        
        $host = "ssl://{$config['smtp_host']}";
        $socket = stream_socket_client("$host:{$config['smtp_port']}", $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context);
        
        if (!$socket) {
            throw new Exception("连接失败: $errstr ($errno)");
        }
        
        // 读取欢迎消息
        $response = fgets($socket);
        $result['steps'][] = ['step' => '服务器欢迎', 'status' => 'success', 'command' => '(连接)', 'response' => trim($response)];
        
        // EHLO
        $command = "EHLO localhost";
        fwrite($socket, "$command\r\n");
        $response = '';
        while ($line = fgets($socket)) {
            $response .= $line;
            if (substr($line, 3, 1) === ' ') break;
        }
        $result['steps'][] = ['step' => 'EHLO握手', 'status' => substr($response, 0, 3) === '250' ? 'success' : 'error', 'command' => $command, 'response' => trim($response)];
        
        // AUTH LOGIN
        $command = "AUTH LOGIN";
        fwrite($socket, "$command\r\n");
        $response = fgets($socket);
        $result['steps'][] = ['step' => '开始认证', 'status' => substr($response, 0, 3) === '334' ? 'success' : 'error', 'command' => $command, 'response' => trim($response)];
        
        // 发送用户名
        $username = base64_encode($config['username']);
        fwrite($socket, "$username\r\n");
        $response = fgets($socket);
        $result['steps'][] = ['step' => '发送用户名', 'status' => substr($response, 0, 3) === '334' ? 'success' : 'error', 'command' => "用户名: {$config['username']}", 'response' => trim($response)];
        
        // 发送密码
        $password = base64_encode($config['password']);
        fwrite($socket, "$password\r\n");
        $response = fgets($socket);
        $result['steps'][] = ['step' => '发送密码', 'status' => substr($response, 0, 3) === '235' ? 'success' : 'error', 'command' => '密码: ****', 'response' => trim($response)];
        
        if (substr($response, 0, 3) !== '235') {
            throw new Exception("认证失败: $response");
        }
        
        // MAIL FROM
        $command = "MAIL FROM: <{$config['from_email']}>";
        fwrite($socket, "$command\r\n");
        $response = fgets($socket);
        $result['steps'][] = ['step' => '设置发件人', 'status' => substr($response, 0, 3) === '250' ? 'success' : 'error', 'command' => $command, 'response' => trim($response)];
        
        if (substr($response, 0, 3) !== '250') {
            throw new Exception("发件人设置失败: $response");
        }
        
        // RCPT TO - 这里最可能出现550错误
        $command = "RCPT TO: <$toEmail>";
        fwrite($socket, "$command\r\n");
        $response = fgets($socket);
        $result['steps'][] = ['step' => '设置收件人', 'status' => substr($response, 0, 3) === '250' ? 'success' : 'error', 'command' => $command, 'response' => trim($response)];
        
        if (substr($response, 0, 3) !== '250') {
            $result['error'] = "收件人设置失败: $response";
            $result['steps'][] = ['step' => '错误分析', 'status' => 'info', 'command' => '分析', 'response' => '550错误通常表示收件人邮箱无效或被拒绝'];
        } else {
            // DATA
            $command = "DATA";
            fwrite($socket, "$command\r\n");
            $response = fgets($socket);
            $result['steps'][] = ['step' => '开始数据传输', 'status' => substr($response, 0, 3) === '354' ? 'success' : 'error', 'command' => $command, 'response' => trim($response)];
            
            if (substr($response, 0, 3) === '354') {
                $result['success'] = true;
                $result['steps'][] = ['step' => '测试完成', 'status' => 'success', 'command' => '总结', 'response' => 'SMTP通信正常，可以发送邮件'];
            }
        }
        
        // QUIT
        fwrite($socket, "QUIT\r\n");
        $response = fgets($socket);
        $result['steps'][] = ['step' => '断开连接', 'status' => 'info', 'command' => 'QUIT', 'response' => trim($response)];
        
        fclose($socket);
        
    } catch (Exception $e) {
        $result['error'] = $e->getMessage();
        $result['steps'][] = ['step' => '异常', 'status' => 'error', 'command' => '异常', 'response' => $e->getMessage()];
    }
    
    return $result;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMTP详细调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; font-weight: bold; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .step { margin: 10px 0; padding: 10px; border-left: 4px solid #ddd; }
        .step.success { border-left-color: #4CAF50; background: #f0f8f0; }
        .step.error { border-left-color: #f44336; background: #fdf0f0; }
        .step.info { border-left-color: #2196F3; background: #f0f7ff; }
        input, button { padding: 8px; margin: 5px; }
        button { background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
        .command { font-family: monospace; background: #f5f5f5; padding: 2px 4px; }
        .response { font-family: monospace; background: #e8e8e8; padding: 2px 4px; }
    </style>
</head>
<body>
    <h1>🔍 SMTP详细调试</h1>
    <p><strong>调试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="test-section">
        <h2>🧪 SMTP命令测试</h2>
        <p>此工具将逐步执行SMTP命令，帮助定位550错误的具体原因。</p>
        
        <form method="POST">
            <div>
                <label>测试收件人邮箱:</label><br>
                <input type="email" name="test_email" value="<?php echo $_POST['test_email'] ?? '<EMAIL>'; ?>" required style="width: 300px;">
                <small>建议先测试一个确定存在的邮箱地址</small>
            </div>
            <div>
                <button type="submit" name="test_smtp">开始SMTP测试</button>
            </div>
        </form>
    </div>
    
    <?php if ($testResult): ?>
    <div class="test-section">
        <h2>📋 SMTP命令执行结果</h2>
        
        <div class="step <?php echo $testResult['success'] ? 'success' : 'error'; ?>">
            <h3><?php echo $testResult['success'] ? '✅ SMTP测试成功' : '❌ SMTP测试失败'; ?></h3>
            <?php if ($testResult['error']): ?>
                <p><strong>错误信息:</strong> <?php echo htmlspecialchars($testResult['error']); ?></p>
            <?php endif; ?>
        </div>
        
        <h3>详细的SMTP命令执行过程:</h3>
        <?php foreach ($testResult['steps'] as $step): ?>
            <div class="step <?php echo $step['status']; ?>">
                <h4><?php echo $step['step']; ?> - <?php echo ucfirst($step['status']); ?></h4>
                <?php if (isset($step['command'])): ?>
                    <p><strong>命令:</strong> <span class="command"><?php echo htmlspecialchars($step['command']); ?></span></p>
                <?php endif; ?>
                <?php if (isset($step['response'])): ?>
                    <p><strong>服务器响应:</strong> <span class="response"><?php echo htmlspecialchars($step['response']); ?></span></p>
                <?php endif; ?>
                <?php if (isset($step['data'])): ?>
                    <p><strong>数据:</strong></p>
                    <pre><?php echo htmlspecialchars(json_encode($step['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
    </div>
    
    <div class="test-section">
        <h2>🔍 550错误解决方案</h2>
        
        <h3>如果在"设置收件人"步骤出现550错误:</h3>
        <ol>
            <li><strong>检查收件人邮箱</strong>: 确认邮箱地址正确且存在</li>
            <li><strong>尝试其他邮箱</strong>: 测试不同的收件人邮箱</li>
            <li><strong>检查发件人权限</strong>: 163邮箱可能限制发送到某些域名</li>
            <li><strong>验证邮箱格式</strong>: 确保邮箱地址格式正确</li>
        </ol>
        
        <h3>建议的测试邮箱:</h3>
        <ul>
            <li>同一个163邮箱 (<EMAIL>)</li>
            <li>其他163邮箱地址</li>
            <li>QQ邮箱地址</li>
            <li>Gmail地址</li>
        </ul>
    </div>
    <?php endif; ?>
    
    <div class="test-section">
        <h2>🔗 相关链接</h2>
        <ul>
            <li><a href="debug-email-sending.php">完整邮件发送调试</a></li>
            <li><a href="smtp-connection-test.php">SMTP连接测试</a></li>
            <li><a href="../new-lock.html">返回登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
