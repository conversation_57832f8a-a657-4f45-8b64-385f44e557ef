<?php
/**
 * 安全检查工具
 * 验证.htaccess配置是否正确保护敏感文件
 */

header('Content-Type: text/html; charset=utf-8');

$securityCheck = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_security_check'])) {
    $securityCheck = runSecurityCheck();
}

function runSecurityCheck() {
    $result = [
        'htaccess_files' => [],
        'protected_paths' => [],
        'vulnerable_files' => [],
        'security_score' => 0,
        'recommendations' => []
    ];
    
    // 检查.htaccess文件存在性
    $htaccessFiles = [
        '根目录' => '../.htaccess',
        'vendor目录' => '../vendor/.htaccess',
        'server目录' => '../server/.htaccess'
    ];
    
    foreach ($htaccessFiles as $name => $path) {
        $exists = file_exists($path);
        $result['htaccess_files'][$name] = [
            'path' => $path,
            'exists' => $exists,
            'size' => $exists ? filesize($path) : 0
        ];
        if ($exists) $result['security_score'] += 20;
    }
    
    // 检查需要保护的敏感路径
    $protectedPaths = [
        'vendor/autoload.php' => '../vendor/autoload.php',
        'vendor/composer/autoload_real.php' => '../vendor/composer/autoload_real.php',
        'vendor/phpmailer/phpmailer/src/PHPMailer.php' => '../vendor/phpmailer/phpmailer/src/PHPMailer.php',
        'server/email-config.php' => '../server/email-config.php',
        'server/working-email-sender.php' => '../server/working-email-sender.php',
        'docs/records.md' => '../docs/records.md'
    ];
    
    foreach ($protectedPaths as $name => $path) {
        $exists = file_exists($path);
        $result['protected_paths'][$name] = [
            'path' => $path,
            'exists' => $exists,
            'should_be_protected' => true
        ];
    }
    
    // 检查可能的安全漏洞文件
    $vulnerablePatterns = [
        '*.log' => '../*.log',
        '*.bak' => '../*.bak',
        '*.backup' => '../*.backup',
        '.env' => '../.env',
        'config.php' => '../config.php'
    ];
    
    foreach ($vulnerablePatterns as $pattern => $path) {
        $files = glob($path);
        if (!empty($files)) {
            $result['vulnerable_files'][$pattern] = $files;
            $result['recommendations'][] = "发现敏感文件: $pattern - 建议删除或保护";
        }
    }
    
    // 安全建议
    if (!$result['htaccess_files']['根目录']['exists']) {
        $result['recommendations'][] = "缺少根目录.htaccess文件 - 建议创建以保护敏感目录";
    }
    
    if (!$result['htaccess_files']['vendor目录']['exists']) {
        $result['recommendations'][] = "缺少vendor/.htaccess文件 - 建议创建以保护vendor目录";
    }
    
    // 计算安全评分
    $maxScore = 100;
    if (empty($result['vulnerable_files'])) $result['security_score'] += 30;
    if (count($result['recommendations']) <= 2) $result['security_score'] += 30;
    
    $result['security_score'] = min($result['security_score'], $maxScore);
    
    return $result;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全检查工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
        button { padding: 8px 16px; margin: 5px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .security-high { background: #d4edda; color: #155724; }
        .security-medium { background: #fff3cd; color: #856404; }
        .security-low { background: #f8d7da; color: #721c24; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
    </style>
</head>
<body>
    <h1>🔒 安全检查工具</h1>
    <p><strong>检查时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="highlight">
        <h3>🎯 安全检查目标</h3>
        <p><strong>检查范围:</strong> vendor目录、敏感文件、.htaccess配置</p>
        <p><strong>保护目标:</strong></p>
        <ul>
            <li>vendor/目录下的所有文件（PHPMailer源码、Composer文件）</li>
            <li>server/目录下的配置文件</li>
            <li>docs/目录下的文档文件</li>
            <li>数据库文件、日志文件、备份文件</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🧪 运行安全检查</h2>
        <form method="POST">
            <button type="submit" name="run_security_check">运行安全检查</button>
        </form>
    </div>
    
    <?php if ($securityCheck): ?>
    <div class="test-section">
        <h2>📊 安全评分</h2>
        <?php
        $score = $securityCheck['security_score'];
        $scoreClass = $score >= 80 ? 'security-high' : ($score >= 60 ? 'security-medium' : 'security-low');
        ?>
        <div class="<?php echo $scoreClass; ?>" style="padding: 15px; border-radius: 5px; text-align: center;">
            <h3>安全评分: <?php echo $score; ?>/100</h3>
            <p>
                <?php if ($score >= 80): ?>
                    ✅ 安全配置良好
                <?php elseif ($score >= 60): ?>
                    ⚠️ 安全配置一般，建议改进
                <?php else: ?>
                    ❌ 安全配置不足，需要立即改进
                <?php endif; ?>
            </p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📋 .htaccess文件检查</h2>
        <table>
            <tr>
                <th>位置</th>
                <th>文件路径</th>
                <th>状态</th>
                <th>大小</th>
            </tr>
            <?php foreach ($securityCheck['htaccess_files'] as $name => $info): ?>
                <tr>
                    <td><?php echo htmlspecialchars($name); ?></td>
                    <td><?php echo htmlspecialchars($info['path']); ?></td>
                    <td>
                        <?php if ($info['exists']): ?>
                            <span class="success">✅ 存在</span>
                        <?php else: ?>
                            <span class="error">❌ 缺失</span>
                        <?php endif; ?>
                    </td>
                    <td><?php echo $info['exists'] ? number_format($info['size']) . ' 字节' : '-'; ?></td>
                </tr>
            <?php endforeach; ?>
        </table>
    </div>
    
    <div class="test-section">
        <h2>🛡️ 受保护路径检查</h2>
        <table>
            <tr>
                <th>敏感文件</th>
                <th>文件路径</th>
                <th>存在状态</th>
                <th>保护状态</th>
            </tr>
            <?php foreach ($securityCheck['protected_paths'] as $name => $info): ?>
                <tr>
                    <td><?php echo htmlspecialchars($name); ?></td>
                    <td><?php echo htmlspecialchars($info['path']); ?></td>
                    <td>
                        <?php if ($info['exists']): ?>
                            <span class="info">📁 存在</span>
                        <?php else: ?>
                            <span class="warning">📂 不存在</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($info['should_be_protected']): ?>
                            <span class="success">🔒 应受保护</span>
                        <?php else: ?>
                            <span class="info">🔓 可公开</span>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endforeach; ?>
        </table>
    </div>
    
    <?php if (!empty($securityCheck['vulnerable_files'])): ?>
    <div class="test-section">
        <h2>⚠️ 发现的安全风险文件</h2>
        <table>
            <tr>
                <th>文件类型</th>
                <th>发现的文件</th>
                <th>风险级别</th>
            </tr>
            <?php foreach ($securityCheck['vulnerable_files'] as $pattern => $files): ?>
                <tr>
                    <td><?php echo htmlspecialchars($pattern); ?></td>
                    <td>
                        <?php foreach ($files as $file): ?>
                            <div><?php echo htmlspecialchars($file); ?></div>
                        <?php endforeach; ?>
                    </td>
                    <td><span class="error">🔴 高风险</span></td>
                </tr>
            <?php endforeach; ?>
        </table>
    </div>
    <?php endif; ?>
    
    <?php if (!empty($securityCheck['recommendations'])): ?>
    <div class="test-section">
        <h2>💡 安全建议</h2>
        <ul>
            <?php foreach ($securityCheck['recommendations'] as $recommendation): ?>
                <li class="warning"><?php echo htmlspecialchars($recommendation); ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>
    <?php endif; ?>
    
    <div class="test-section">
        <h2>🔒 vendor目录需要保护的文件类型</h2>
        
        <h3>🚫 完全禁止访问的文件：</h3>
        <ul>
            <li><strong>PHP源码文件</strong>: *.php (包含PHPMailer源码)</li>
            <li><strong>Composer文件</strong>: composer.json, composer.lock, autoload.php</li>
            <li><strong>配置文件</strong>: *.ini, *.conf, *.config</li>
            <li><strong>文档文件</strong>: *.md, *.txt, README, CHANGELOG</li>
            <li><strong>日志文件</strong>: *.log</li>
            <li><strong>备份文件</strong>: *.bak, *.backup</li>
        </ul>
        
        <h3>🔐 特别需要保护的vendor文件：</h3>
        <ul>
            <li><code>vendor/autoload.php</code> - Composer自动加载入口</li>
            <li><code>vendor/composer/autoload_real.php</code> - 自动加载核心</li>
            <li><code>vendor/composer/autoload_static.php</code> - 类映射文件</li>
            <li><code>vendor/composer/ClassLoader.php</code> - 类加载器</li>
            <li><code>vendor/phpmailer/phpmailer/src/*.php</code> - PHPMailer源码</li>
            <li><code>vendor/composer.json</code> - 依赖配置</li>
            <li><code>vendor/composer.lock</code> - 版本锁定文件</li>
        </ul>
        
        <h3>⚡ .htaccess保护策略：</h3>
        <ol>
            <li><strong>全目录拒绝</strong>: <code>Deny from all</code></li>
            <li><strong>文件类型过滤</strong>: 按扩展名拒绝访问</li>
            <li><strong>文件名模式</strong>: 按文件名模式拒绝</li>
            <li><strong>PHP执行禁用</strong>: 禁用PHP引擎</li>
            <li><strong>目录浏览禁用</strong>: <code>Options -Indexes</code></li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关工具</h2>
        <ul>
            <li><a href="test-access-protection.php">测试访问保护</a></li>
            <li><a href="final-verification.php">最终验证</a></li>
            <li><a href="../new-lock.html">返回登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
