/* Android TV优化版样式 - 减少动画和GPU负载 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
    height: 100vh;
    overflow: hidden;
    background: linear-gradient(135deg, #ff4757 0%, #ff3838 25%, #ff6b7a 50%, #ff4757 75%, #ff2f3a 100%);
    position: relative;
}

/* 装饰线条和图案 - 与标准版本保持一致 */
body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
        linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.05) 50%, transparent 60%);
    background-size: 50px 50px, 50px 50px, 100px 100px;
    animation: backgroundMove 20s linear infinite;
    z-index: 1;
}

@keyframes backgroundMove {
    0% { background-position: 0 0, 0 0, 0 0; }
    100% { background-position: 50px 50px, -50px -50px, 100px 100px; }
}

/* Logo容器 - 与原版本保持一致 */
.logo-container {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 1000;
}

.logo {
    max-width: 69px;  /* 60px * 1.15 = 69px */
    max-height: 46px; /* 40px * 1.15 = 46px */
    display: block;
    margin-left: 20px;
    margin-top: 20px;
    object-fit: contain;
    transition: all 0.3s ease;
}

/* 主要内容区域 */
.main-content {
    position: absolute;
    top: calc(40% - 20px); /* 向上移动20px */
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 5;
    width: 90%;
}

.main-title {
    color: white;
    font-size: 3.84vw; /* 3.2vw * 1.2 = 3.84vw */
    font-weight: bold;
    margin-bottom: 2.88vh; /* 2.4vh * 1.2 = 2.88vh */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    white-space: nowrap;

    /* 优化：移除脉冲动画，减少重绘 */
}

.warning-title {
    color: white;
    font-size: 2.28vw; /* 1.9vw * 1.2 = 2.28vw */
    font-weight: bold;
    margin-top: 2.88vh; /* 2.4vh * 1.2 = 2.88vh */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    white-space: nowrap;
    position: relative;
    overflow: hidden;

    /* 优化：简化高光动画，降低频率 */
}

.warning-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 100%);
    
    /* 优化：降低动画频率，使用transform代替left */
    animation: shine 6s ease-in-out infinite;
}

@keyframes shine {
    0% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(200%);
    }
    100% {
        transform: translateX(200%);
    }
}

.highlight-number {
    color: #ffff00;
    font-weight: bold;
    font-size: 3.5rem; /* TV版使用更大的字体 */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7), 0 0 15px #ffff00;
    display: inline-block;
    transform: scale(1.2) translateY(12px); /* TV版额外放大20%，并向下移动3px */
    margin: 0 22px; /* TV版左右增加20px间距，避免覆盖其他文字 */
}

/* 倒计时容器 */
.countdown-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1.8vw;
    margin: 2vh 0;
    flex-wrap: nowrap;
}

.countdown-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 7.2vw;
    padding: 1.8vh 1.2vw;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    
    /* 优化：使用will-change提示浏览器优化 */
    will-change: contents;
}

.countdown-number {
    font-size: 4.56vw; /* 3.8vw * 1.2 = 4.56vw */
    font-weight: bold;
    color: #ffff00;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    line-height: 1;

    /* 优化：移除发光动画，减少GPU负载 */
    /* 使用静态阴影效果 */
    text-shadow:
        2px 2px 4px rgba(0, 0, 0, 0.7),
        0 0 15px #ffff00;
}

.countdown-label {
    font-size: 1.44vw;
    color: #ffff00;
    margin-top: 0.5vh;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* 汽车图片区域 */
.car-container {
    position: fixed;
    bottom: 8vh;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
}

.car-image {
    max-width: 57.6vw; /* 48vw * 1.2 = 57.6vw */
    max-height: 27.6vh; /* 23vh * 1.2 = 27.6vh */
    object-fit: contain;
    transform: translateY(-10px); /* 向上移动10px */

    /* 优化：移除transform动画 */
}

/* 响应式设计优化 */
@media (max-width: 768px) {
    .main-title {
        font-size: 6vw;
    }
    
    .warning-title {
        font-size: 4vw;
    }
    
    .countdown-number {
        font-size: 8vw;
    }
    
    .countdown-label {
        font-size: 3vw;
    }
    
    .countdown-item {
        min-width: 15vw;
        padding: 2vh 2vw;
    }
    
    .car-image {
        max-width: 96vw; /* 80vw * 1.2 = 96vw */
        max-height: 24vh; /* 20vh * 1.2 = 24vh */
        transform: translateY(-10px); /* 向上移动10px */
    }
    
    .logo {
        max-width: 92px; /* 80px * 1.15 = 92px */
        max-height: 58px; /* 50px * 1.15 = 58px */
        margin-left: 3px;
        margin-top: 3px;
    }
}

/* 超小屏幕优化 */
@media (max-width: 320px) {
    .logo {
        max-width: 69px; /* 60px * 1.15 = 69px */
        max-height: 46px; /* 40px * 1.15 = 46px */
        margin-left: 2px;
        margin-top: 2px;
    }
}

/* 平板设备优化 */
@media (min-width: 768px) and (max-width: 1024px) {
    .logo {
        max-width: 115px; /* 100px * 1.15 = 115px */
        max-height: 69px; /* 60px * 1.15 = 69px */
        margin-left: 5px;
        margin-top: 5px;
    }
}

/* 中等屏幕优化 */
@media (min-width: 1025px) and (max-width: 1439px) {
    .logo {
        max-width: 92px; /* 80px * 1.15 = 92px */
        max-height: 58px; /* 50px * 1.15 = 58px */
        margin-left: 3px;
        margin-top: 3px;
    }
}

/* 大屏幕优化 */
@media (min-width: 1440px) and (max-width: 1919px) {
    .logo {
        max-width: 92px; /* 80px * 1.15 = 92px */
        max-height: 58px; /* 50px * 1.15 = 58px */
        margin-left: 20px;
        margin-top: 20px;
    }
}

/* 超大屏幕优化 */
@media (min-width: 1920px) and (max-width: 2559px) {
    .logo {
        max-width: 115px; /* 100px * 1.15 = 115px */
        max-height: 69px; /* 60px * 1.15 = 69px */
        margin-left: 20px;
        margin-top: 20px;
    }
}

/* Android TV特殊优化 */
/* 用户图标样式 */
.user-icon-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
}

.user-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #333;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    animation: fadeToTransparent 3s ease-in-out forwards;
}

.user-icon:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
    opacity: 1 !important;
}

.user-menu {
    position: absolute;
    top: 50px;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    border-radius: 8px;
    padding: 15px;
    min-width: 250px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.user-icon-container:hover .user-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-info {
    margin-bottom: 10px;
}

.username {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
}

.version-info {
    font-size: 12px;
    color: #4CAF50;
    margin-bottom: 5px;
}

.activation-code {
    font-size: 12px;
    font-family: 'Courier New', monospace;
    background: rgba(255, 255, 255, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    margin-bottom: 5px;
}

.expiry-date {
    font-size: 11px;
    color: #ccc;
}

.logout-btn {
    background: #f44336;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    width: 100%;
    transition: background 0.3s ease;
}

.logout-btn:hover {
    background: #d32f2f;
}

/* 版本切换图标样式 */
.version-toggle-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.version-toggle {
    min-width: 120px;
    height: 40px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
    opacity: 0.3;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    padding: 0 8px;
    white-space: nowrap;
    color: white;
}

.version-toggle:hover {
    opacity: 0.9;
    transform: scale(1.05);
    background: rgba(0, 0, 0, 0.9);
}

/* 3秒后自动降低透明度的动画 */
@keyframes fadeToTransparent {
    0% { opacity: 1; }
    90% { opacity: 1; }
    100% { opacity: 0.6; }
}

/* 小屏幕响应式优化 - 保持比例缩放 */
@media (max-width: 1440px) {
    /* 保持原有的视口单位比例，确保元素按比例缩放 */
    .user-icon-container {
        top: 2vh;
        right: 3vw;
    }

    .version-toggle-container {
        bottom: 5vh;
        right: 3vw;
    }

    /* 汽车图片在小屏幕下适当限制最大宽度 */
    .car-image {
        max-width: min(57.6vw, 90vw);
        height: auto;
    }
}

/* 移动设备优化 - 保持比例但适配触摸操作 */
@media (max-width: 768px) {
    .user-icon {
        width: 35px;
        height: 35px;
    }

    .version-toggle {
        min-width: 100px;
        height: 35px;
        font-size: 10px;
        padding: 0 6px;
    }

    .car-image {
        max-width: min(57.6vw, 95vw);
        height: auto;
    }
}

@media (min-width: 1920px) {
    /* 4K电视优化 */
    .main-content {
        top: 35%;
    }

    .countdown-container {
        gap: 2vw;
    }

    .countdown-item {
        border-radius: 15px;
        border-width: 3px;
    }

    /* 主标题4K优化 - 与标准版本保持一致 */
    .main-title {
        font-size: 3.84vw; /* 3.2vw * 1.2 = 3.84vw */
        margin-bottom: 2.88vh; /* 2.4vh * 1.2 = 2.88vh */
        white-space: nowrap;
    }

    /* 警示文字4K优化 - 与标准版本保持一致 */
    .warning-title {
        font-size: 2.28vw; /* 1.9vw * 1.2 = 2.28vw */
        white-space: nowrap;
    }

    /* 倒计时数字4K优化 - 与标准版本保持一致 */
    .countdown-number {
        font-size: 4.56vw; /* 3.8vw * 1.2 = 4.56vw */
        text-shadow:
            3px 3px 6px rgba(0, 0, 0, 0.8),
            0 0 20px #ffff00;
    }

    /* 倒计时标签4K优化 - 与标准版本保持一致 */
    .countdown-label {
        font-size: 1.44vw;
    }

    /* Logo大小优化 - 与标准版保持一致 */
    .logo {
        max-width: 115px;  /* 与标准版4K分辨率一致 */
        max-height: 69px;  /* 与标准版4K分辨率一致 */
        margin-left: 1vw;
        margin-top: 1vh;
    }

    /* 汽车图片4K优化 - 与标准版本保持一致 */
    .car-image {
        max-width: 57.6vw; /* 48vw * 1.2 = 57.6vw */
        max-height: 27.6vh; /* 23vh * 1.2 = 27.6vh */
        transform: translateY(-25px); /* -15px - 10px = -25px */
    }

    /* 倒计时容器4K优化 - 与标准版本保持一致 */
    .countdown-container {
        gap: 1.8vw;
    }

    .countdown-item {
        min-width: 7.2vw;
        padding: 1.8vh 1.2vw;
        border-radius: 15px;
        border-width: 3px;
    }
}

/* 4K电视和超大屏幕优化 - 保持与1920x1080相同的视觉比例 */
@media (min-width: 2560px) {
    .main-title {
        font-size: 3.84vw; /* 3.2vw * 1.2 = 3.84vw */
        margin-bottom: 2.88vh; /* 2.4vh * 1.2 = 2.88vh */
        white-space: nowrap;
    }

    .warning-title {
        font-size: 2.28vw; /* 1.9vw * 1.2 = 2.28vw */
        white-space: nowrap;
    }

    .countdown-number {
        font-size: 4.56vw; /* 3.8vw * 1.2 = 4.56vw */
    }

    .countdown-label {
        font-size: 1.44vw;
    }

    .car-image {
        max-width: 57.6vw; /* 48vw * 1.2 = 57.6vw */
        max-height: 27.6vh; /* 23vh * 1.2 = 27.6vh */
        transform: translateY(-25px); /* -15px - 10px = -25px */
    }

    .logo {
        max-width: 115px;  /* 与标准版4K分辨率一致 */
        max-height: 69px;  /* 与标准版4K分辨率一致 */
        margin-left: 1vw;
        margin-top: 1vh;
    }

    .countdown-container {
        gap: 1.8vw;
    }

    .countdown-item {
        min-width: 7.2vw;
        padding: 1.8vh 1.2vw;
    }
}

/* 性能优化：减少重绘和回流 */
.countdown-number,
.countdown-label {
    /* 使用GPU加速，但避免过度使用 */
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* 优化：移除不必要的动画 */
/* 保持页面静态，专注于倒计时功能 */

/* 内存优化：简化选择器 */
.main-title,
.warning-title,
.countdown-number,
.countdown-label {
    /* 统一字体渲染优化 */
    text-rendering: optimizeSpeed;
    font-display: swap;
}

/* 减少CSS计算复杂度 */
* {
    /* 避免复杂的盒模型计算 */
    contain: layout style;
}
