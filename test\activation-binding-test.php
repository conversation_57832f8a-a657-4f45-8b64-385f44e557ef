<?php
/**
 * 激活码绑定测试工具
 * 专门用于测试"激活码已被其他用户使用"的问题
 */

header('Content-Type: text/html; charset=utf-8');

$dbPath = '../server/user_system.db3';

function testActivationBinding($code, $username) {
    global $dbPath;
    
    $result = [
        'success' => false,
        'message' => '',
        'debug_info' => [],
        'steps' => []
    ];
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 步骤1: 查找用户
        $result['steps'][] = "步骤1: 查找用户 '$username'";
        $stmt = $pdo->prepare("SELECT id, username, status FROM users WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            $result['message'] = "用户 '$username' 不存在";
            return $result;
        }
        
        $result['debug_info']['user'] = $user;
        $result['steps'][] = "✅ 找到用户: ID={$user['id']}, 状态={$user['status']}";
        
        // 步骤2: 检查激活码
        $result['steps'][] = "步骤2: 检查激活码 '$code'";
        $stmt = $pdo->prepare("
            SELECT id, code, is_used, bound_user_id, expires_at, status, created_at
            FROM activation_codes 
            WHERE code = ?
        ");
        $stmt->execute([$code]);
        $codeInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$codeInfo) {
            $result['message'] = "激活码 '$code' 不存在";
            return $result;
        }
        
        $result['debug_info']['code_before'] = $codeInfo;
        $result['steps'][] = "✅ 找到激活码: ID={$codeInfo['id']}, is_used={$codeInfo['is_used']}, status={$codeInfo['status']}";
        
        // 步骤3: 检查激活码状态
        $result['steps'][] = "步骤3: 检查激活码状态";
        
        if ($codeInfo['status'] !== 'active') {
            $result['message'] = "激活码状态无效: {$codeInfo['status']}";
            return $result;
        }
        $result['steps'][] = "✅ 激活码状态正常: active";
        
        if ($codeInfo['is_used']) {
            if ($codeInfo['bound_user_id'] == $user['id']) {
                $result['message'] = "您已经绑定过这个激活码了";
            } else {
                $result['message'] = "激活码已被其他用户使用 (bound_user_id: {$codeInfo['bound_user_id']})";
            }
            return $result;
        }
        $result['steps'][] = "✅ 激活码未被使用";
        
        // 检查是否过期
        if (strtotime($codeInfo['expires_at']) <= time()) {
            $result['message'] = "激活码已过期";
            return $result;
        }
        $result['steps'][] = "✅ 激活码未过期";
        
        // 步骤4: 检查用户是否已有激活码
        $result['steps'][] = "步骤4: 检查用户是否已有激活码";
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM activation_codes 
            WHERE bound_user_id = ? AND status = 'used'
        ");
        $stmt->execute([$user['id']]);
        
        if ($stmt->fetchColumn() > 0) {
            $result['message'] = '您已经绑定过激活码了';
            return $result;
        }
        $result['steps'][] = "✅ 用户未绑定其他激活码";
        
        // 步骤5: 开始事务绑定
        $result['steps'][] = "步骤5: 开始事务绑定";
        $pdo->beginTransaction();
        
        try {
            // 更新激活码
            $stmt = $pdo->prepare("
                UPDATE activation_codes 
                SET is_used = 1, bound_user_id = ?, used_at = datetime('now'), status = 'used'
                WHERE id = ?
            ");
            $stmt->execute([$user['id'], $codeInfo['id']]);
            $result['steps'][] = "✅ 激活码更新成功";
            
            // 验证更新结果
            $stmt = $pdo->prepare("
                SELECT id, code, is_used, bound_user_id, expires_at, status, used_at
                FROM activation_codes 
                WHERE id = ?
            ");
            $stmt->execute([$codeInfo['id']]);
            $updatedCode = $stmt->fetch(PDO::FETCH_ASSOC);
            $result['debug_info']['code_after'] = $updatedCode;
            $result['steps'][] = "✅ 验证更新: is_used={$updatedCode['is_used']}, bound_user_id={$updatedCode['bound_user_id']}, status={$updatedCode['status']}";
            
            // 提交事务
            $pdo->commit();
            $result['steps'][] = "✅ 事务提交成功";
            
            $result['success'] = true;
            $result['message'] = '激活码绑定成功！';
            
        } catch (Exception $e) {
            $pdo->rollBack();
            $result['steps'][] = "❌ 事务回滚: " . $e->getMessage();
            throw $e;
        }
        
    } catch (Exception $e) {
        $result['message'] = "绑定失败: " . $e->getMessage();
        $result['debug_info']['error'] = $e->getMessage();
    }
    
    return $result;
}

function getCurrentActivationCodes() {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->query("
            SELECT ac.code, ac.is_used, ac.status, ac.created_at, ac.expires_at, 
                   ac.bound_user_id, u.username
            FROM activation_codes ac
            LEFT JOIN users u ON ac.bound_user_id = u.id
            ORDER BY ac.created_at DESC
            LIMIT 5
        ");
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return [];
    }
}

function getUsers() {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->query("
            SELECT id, username, email, status, created_at
            FROM users
            ORDER BY created_at DESC
            LIMIT 10
        ");
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return [];
    }
}

// 处理测试请求
$testResult = null;
if (isset($_POST['action']) && $_POST['action'] === 'test' && !empty($_POST['code']) && !empty($_POST['username'])) {
    $testResult = testActivationBinding($_POST['code'], $_POST['username']);
}

$codes = getCurrentActivationCodes();
$users = getUsers();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>激活码绑定测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
        .warning-box { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; }
        .error-box { background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .test-form { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .test-form input, .test-form select { padding: 8px; margin: 5px; width: 200px; }
        .test-form button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .test-form button:hover { background: #0056b3; }
        .steps { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .steps ol { margin: 0; padding-left: 20px; }
        .steps li { margin: 5px 0; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <h1>🧪 激活码绑定测试工具</h1>
    <p><strong>测试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="test-form">
        <h2>🔬 绑定测试</h2>
        <form method="POST">
            <input type="hidden" name="action" value="test">
            <div>
                <label>激活码:</label><br>
                <input type="text" name="code" value="<?php echo htmlspecialchars($_POST['code'] ?? ''); ?>" 
                       placeholder="XXXXX-XXXXX-XXXXX-XXXXX" required>
            </div>
            <div>
                <label>用户名:</label><br>
                <select name="username" required>
                    <option value="">选择用户</option>
                    <?php foreach ($users as $user): ?>
                        <option value="<?php echo htmlspecialchars($user['username']); ?>" 
                                <?php echo ($_POST['username'] ?? '') === $user['username'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($user['username']); ?> (ID: <?php echo $user['id']; ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div>
                <button type="submit">开始测试绑定</button>
            </div>
        </form>
    </div>
    
    <?php if ($testResult): ?>
        <div class="section">
            <h2>🔍 测试结果</h2>
            
            <div class="<?php echo $testResult['success'] ? 'highlight' : 'error-box'; ?>">
                <h3><?php echo $testResult['success'] ? '✅ 绑定成功' : '❌ 绑定失败'; ?></h3>
                <p><strong>结果:</strong> <?php echo htmlspecialchars($testResult['message']); ?></p>
            </div>
            
            <div class="steps">
                <h3>📋 执行步骤:</h3>
                <ol>
                    <?php foreach ($testResult['steps'] as $step): ?>
                        <li><?php echo htmlspecialchars($step); ?></li>
                    <?php endforeach; ?>
                </ol>
            </div>
            
            <?php if (!empty($testResult['debug_info'])): ?>
                <h3>🔧 调试信息:</h3>
                <pre><?php echo htmlspecialchars(json_encode($testResult['debug_info'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
            <?php endif; ?>
        </div>
    <?php endif; ?>
    
    <div class="section">
        <h2>📋 当前激活码</h2>
        <table>
            <tr>
                <th>激活码</th>
                <th>is_used</th>
                <th>status</th>
                <th>创建时间</th>
                <th>过期时间</th>
                <th>绑定用户</th>
                <th>操作</th>
            </tr>
            <?php foreach ($codes as $code): ?>
                <tr>
                    <td><code><?php echo htmlspecialchars($code['code']); ?></code></td>
                    <td><?php echo $code['is_used'] ? '✅' : '❌'; ?></td>
                    <td><?php echo htmlspecialchars($code['status']); ?></td>
                    <td><?php echo htmlspecialchars($code['created_at']); ?></td>
                    <td><?php echo htmlspecialchars($code['expires_at']); ?></td>
                    <td><?php echo htmlspecialchars($code['username'] ?? '未绑定'); ?></td>
                    <td>
                        <button onclick="fillCode('<?php echo htmlspecialchars($code['code']); ?>')">测试此码</button>
                    </td>
                </tr>
            <?php endforeach; ?>
        </table>
    </div>
    
    <div class="section">
        <h2>👥 用户列表</h2>
        <table>
            <tr>
                <th>ID</th>
                <th>用户名</th>
                <th>邮箱</th>
                <th>状态</th>
                <th>注册时间</th>
            </tr>
            <?php foreach ($users as $user): ?>
                <tr>
                    <td><?php echo $user['id']; ?></td>
                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                    <td><?php echo htmlspecialchars($user['status']); ?></td>
                    <td><?php echo htmlspecialchars($user['created_at']); ?></td>
                </tr>
            <?php endforeach; ?>
        </table>
    </div>
    
    <script>
        function fillCode(code) {
            document.querySelector('input[name="code"]').value = code;
        }
    </script>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
