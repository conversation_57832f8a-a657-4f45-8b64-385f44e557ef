<?php
/**
 * 激活码状态诊断工具
 * 专门用于诊断"激活码已被其他用户使用"的问题
 */

header('Content-Type: text/html; charset=utf-8');

$dbPath = '../server/user_system.db3';

function diagnoseActivationCode($code = null) {
    global $dbPath;
    
    $diagnosis = [
        'database_info' => [],
        'code_analysis' => [],
        'environment_check' => [],
        'recommendations' => []
    ];
    
    // 数据库基本信息
    if (file_exists($dbPath)) {
        $stat = stat($dbPath);
        $diagnosis['database_info'] = [
            'path' => realpath($dbPath),
            'size_mb' => round($stat['size'] / 1024 / 1024, 2),
            'last_modified' => date('Y-m-d H:i:s', $stat['mtime']),
            'permissions' => substr(sprintf('%o', fileperms($dbPath)), -4),
            'writable' => is_writable($dbPath)
        ];
    } else {
        $diagnosis['database_info']['error'] = '数据库文件不存在';
        return $diagnosis;
    }
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 环境检查
        $diagnosis['environment_check'] = [
            'server_time' => date('Y-m-d H:i:s'),
            'timezone' => date_default_timezone_get(),
            'sqlite_version' => $pdo->query('SELECT sqlite_version()')->fetchColumn(),
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'
        ];
        
        // 如果提供了激活码，进行详细分析
        if ($code) {
            $diagnosis['code_analysis'] = analyzeSpecificCode($pdo, $code);
        }
        
        // 获取最近的激活码状态
        $diagnosis['recent_codes'] = getRecentCodes($pdo);
        
        // 检查数据库一致性
        $diagnosis['consistency_check'] = checkDatabaseConsistency($pdo);
        
    } catch (Exception $e) {
        $diagnosis['database_info']['error'] = $e->getMessage();
    }
    
    return $diagnosis;
}

function analyzeSpecificCode($pdo, $code) {
    $analysis = [];

    try {
        // 查询激活码详细信息
        $stmt = $pdo->prepare("
            SELECT ac.*, u.username, u.email, u.status as user_status
            FROM activation_codes ac
            LEFT JOIN users u ON ac.bound_user_id = u.id
            WHERE ac.code = ?
        ");
        $stmt->execute([$code]);
        $codeInfo = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$codeInfo) {
            $analysis['status'] = 'not_found';
            $analysis['message'] = '激活码不存在';
            return $analysis;
        }

        $analysis['basic_info'] = $codeInfo;

        // 分析状态
        $analysis['status_analysis'] = [
            'is_used' => (bool)$codeInfo['is_used'],
            'status' => $codeInfo['status'],
            'is_expired' => strtotime($codeInfo['expires_at']) <= time(),
            'bound_user' => $codeInfo['username'] ?? null,
            'bound_user_status' => $codeInfo['user_status'] ?? null
        ];

        // 检查是否真的被使用
        $analysis['usage_verification'] = [];

        // 检查user_records表（如果存在）
        try {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM user_records WHERE activation_code = ?");
            $stmt->execute([$code]);
            $analysis['usage_verification']['user_records_count'] = $stmt->fetchColumn();
        } catch (Exception $e) {
            $analysis['usage_verification']['user_records_count'] = 'N/A (表不存在)';
        }

        // 检查绑定历史
        $analysis['usage_verification']['binding_logs_count'] = 0;
        if ($codeInfo['bound_user_id']) {
            try {
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) FROM login_logs
                    WHERE username LIKE ? AND status LIKE '%activation%'
                ");
                $stmt->execute(["activation_bind:$code"]);
                $analysis['usage_verification']['binding_logs_count'] = $stmt->fetchColumn();
            } catch (Exception $e) {
                $analysis['usage_verification']['binding_logs_count'] = 'N/A';
            }
        }

        // 时间分析
        $analysis['time_analysis'] = [
            'created_at' => $codeInfo['created_at'],
            'expires_at' => $codeInfo['expires_at'],
            'used_at' => $codeInfo['used_at'],
            'current_time' => date('Y-m-d H:i:s'),
            'is_expired' => strtotime($codeInfo['expires_at']) <= time(),
            'days_until_expiry' => round((strtotime($codeInfo['expires_at']) - time()) / 86400, 1)
        ];

        // 问题诊断
        $analysis['diagnosis'] = [];

        if ((int)$codeInfo['is_used'] === 1 && $codeInfo['bound_user_id'] === null) {
            $analysis['diagnosis'][] = [
                'type' => 'error',
                'message' => '数据不一致：is_used=1但bound_user_id为空'
            ];
        }

        if ($codeInfo['status'] === 'used' && (int)$codeInfo['is_used'] === 0) {
            $analysis['diagnosis'][] = [
                'type' => 'error',
                'message' => '数据不一致：status=used但is_used=0'
            ];
        }

        if (is_numeric($analysis['usage_verification']['user_records_count']) &&
            $analysis['usage_verification']['user_records_count'] > 0 && !$codeInfo['is_used']) {
            $analysis['diagnosis'][] = [
                'type' => 'warning',
                'message' => 'user_records中有记录但激活码标记为未使用'
            ];
        }

    } catch (Exception $e) {
        $analysis['error'] = $e->getMessage();
    }

    return $analysis;
}

function getRecentCodes($pdo) {
    $stmt = $pdo->query("
        SELECT ac.code, ac.is_used, ac.status, ac.created_at, ac.expires_at, 
               ac.bound_user_id, u.username
        FROM activation_codes ac
        LEFT JOIN users u ON ac.bound_user_id = u.id
        ORDER BY ac.created_at DESC
        LIMIT 10
    ");
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function checkDatabaseConsistency($pdo) {
    $checks = [];

    try {
        // 检查is_used和status的一致性
        $stmt = $pdo->query("
            SELECT COUNT(*) FROM activation_codes
            WHERE (is_used = 1 AND status != 'used') OR (is_used = 0 AND status = 'used')
        ");
        $checks['status_inconsistency'] = $stmt->fetchColumn();

        // 检查bound_user_id和is_used的一致性
        $stmt = $pdo->query("
            SELECT COUNT(*) FROM activation_codes
            WHERE (bound_user_id IS NOT NULL AND is_used = 0) OR (bound_user_id IS NULL AND is_used = 1)
        ");
        $checks['binding_inconsistency'] = $stmt->fetchColumn();

        // 检查过期但仍标记为active的激活码
        $stmt = $pdo->query("
            SELECT COUNT(*) FROM activation_codes
            WHERE expires_at <= datetime('now') AND status = 'active'
        ");
        $checks['expired_active_codes'] = $stmt->fetchColumn();

    } catch (Exception $e) {
        $checks['error'] = $e->getMessage();
    }

    return $checks;
}

// 处理请求
$code = $_GET['code'] ?? $_POST['code'] ?? null;
$diagnosis = diagnoseActivationCode($code);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>激活码状态诊断</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
        .warning-box { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; }
        .error-box { background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .code-input { padding: 10px; margin: 10px 0; }
        .code-input input { padding: 8px; width: 300px; font-family: monospace; }
        .code-input button { padding: 8px 15px; margin-left: 10px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 激活码状态诊断工具</h1>
    <p><strong>诊断时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="code-input">
        <form method="GET">
            <label for="code">输入要诊断的激活码:</label><br>
            <input type="text" id="code" name="code" value="<?php echo htmlspecialchars($code ?? ''); ?>" 
                   placeholder="XXXXX-XXXXX-XXXXX-XXXXX" pattern="[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}">
            <button type="submit">诊断</button>
        </form>
    </div>
    
    <div class="section">
        <h2>📊 数据库信息</h2>
        <?php if (isset($diagnosis['database_info']['error'])): ?>
            <div class="error-box">
                <p class="error">❌ 数据库错误: <?php echo htmlspecialchars($diagnosis['database_info']['error']); ?></p>
            </div>
        <?php else: ?>
            <table>
                <tr><th>属性</th><th>值</th></tr>
                <tr><td>数据库路径</td><td><?php echo htmlspecialchars($diagnosis['database_info']['path']); ?></td></tr>
                <tr><td>文件大小</td><td><?php echo $diagnosis['database_info']['size_mb']; ?> MB</td></tr>
                <tr><td>最后修改</td><td><?php echo $diagnosis['database_info']['last_modified']; ?></td></tr>
                <tr><td>文件权限</td><td><?php echo $diagnosis['database_info']['permissions']; ?></td></tr>
                <tr><td>可写性</td><td><?php echo $diagnosis['database_info']['writable'] ? '✅ 可写' : '❌ 不可写'; ?></td></tr>
            </table>
        <?php endif; ?>
    </div>
    
    <div class="section">
        <h2>🌍 环境信息</h2>
        <table>
            <tr><th>属性</th><th>值</th></tr>
            <?php foreach ($diagnosis['environment_check'] as $key => $value): ?>
                <tr><td><?php echo htmlspecialchars($key); ?></td><td><?php echo htmlspecialchars($value); ?></td></tr>
            <?php endforeach; ?>
        </table>
    </div>
    
    <?php if ($code && isset($diagnosis['code_analysis'])): ?>
        <div class="section">
            <h2>🔍 激活码详细分析: <?php echo htmlspecialchars($code); ?></h2>

            <?php $analysis = $diagnosis['code_analysis']; ?>

            <?php if (isset($analysis['error'])): ?>
                <div class="error-box">
                    <p class="error">❌ 分析错误: <?php echo htmlspecialchars($analysis['error']); ?></p>
                </div>
            <?php elseif (isset($analysis['status']) && $analysis['status'] === 'not_found'): ?>
                <div class="error-box">
                    <p class="error">❌ <?php echo htmlspecialchars($analysis['message']); ?></p>
                </div>
            <?php elseif (isset($analysis['basic_info'])): ?>
                <h3>基本信息:</h3>
                <table>
                    <tr><th>属性</th><th>值</th></tr>
                    <tr><td>激活码</td><td><code><?php echo htmlspecialchars($analysis['basic_info']['code'] ?? ''); ?></code></td></tr>
                    <tr><td>is_used</td><td><?php echo ($analysis['basic_info']['is_used'] ?? false) ? '✅ 是' : '❌ 否'; ?></td></tr>
                    <tr><td>status</td><td><strong><?php echo htmlspecialchars($analysis['basic_info']['status'] ?? ''); ?></strong></td></tr>
                    <tr><td>创建时间</td><td><?php echo htmlspecialchars($analysis['basic_info']['created_at'] ?? ''); ?></td></tr>
                    <tr><td>过期时间</td><td><?php echo htmlspecialchars($analysis['basic_info']['expires_at'] ?? ''); ?></td></tr>
                    <tr><td>使用时间</td><td><?php echo htmlspecialchars($analysis['basic_info']['used_at'] ?? '未使用'); ?></td></tr>
                    <tr><td>绑定用户ID</td><td><?php echo htmlspecialchars($analysis['basic_info']['bound_user_id'] ?? '未绑定'); ?></td></tr>
                    <tr><td>绑定用户名</td><td><?php echo htmlspecialchars($analysis['basic_info']['username'] ?? '未绑定'); ?></td></tr>
                </table>

                <?php if (isset($analysis['status_analysis'])): ?>
                <h3>状态分析:</h3>
                <div class="<?php echo ($analysis['status_analysis']['is_expired'] ?? false) ? 'error-box' : 'highlight'; ?>">
                    <p><strong>过期状态:</strong> <?php echo ($analysis['status_analysis']['is_expired'] ?? false) ? '❌ 已过期' : '✅ 有效'; ?></p>
                    <p><strong>使用状态:</strong> <?php echo ($analysis['status_analysis']['is_used'] ?? false) ? '❌ 已使用' : '✅ 未使用'; ?></p>
                    <p><strong>绑定状态:</strong> <?php echo ($analysis['status_analysis']['bound_user'] ?? null) ? "✅ 已绑定到: {$analysis['status_analysis']['bound_user']}" : '❌ 未绑定'; ?></p>
                </div>
                <?php endif; ?>

                <?php if (isset($analysis['usage_verification'])): ?>
                <h3>使用验证:</h3>
                <table>
                    <tr><th>检查项</th><th>结果</th></tr>
                    <tr><td>user_records表记录数</td><td><?php echo $analysis['usage_verification']['user_records_count'] ?? 'N/A'; ?></td></tr>
                    <tr><td>绑定日志记录数</td><td><?php echo $analysis['usage_verification']['binding_logs_count'] ?? 0; ?></td></tr>
                </table>
                <?php endif; ?>

                <?php if (!empty($analysis['diagnosis'])): ?>
                    <h3>问题诊断:</h3>
                    <?php foreach ($analysis['diagnosis'] as $issue): ?>
                        <div class="<?php echo $issue['type'] === 'error' ? 'error-box' : 'warning-box'; ?>">
                            <p><strong><?php echo ucfirst($issue['type']); ?>:</strong> <?php echo htmlspecialchars($issue['message']); ?></p>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    <?php endif; ?>
    
    <div class="section">
        <h2>📋 最近的激活码</h2>
        <table>
            <tr>
                <th>激活码</th>
                <th>is_used</th>
                <th>status</th>
                <th>创建时间</th>
                <th>过期时间</th>
                <th>绑定用户</th>
                <th>操作</th>
            </tr>
            <?php if (isset($diagnosis['recent_codes']) && is_array($diagnosis['recent_codes'])): ?>
                <?php foreach ($diagnosis['recent_codes'] as $recentCode): ?>
                    <tr>
                        <td><code><?php echo htmlspecialchars($recentCode['code'] ?? ''); ?></code></td>
                        <td><?php echo ($recentCode['is_used'] ?? false) ? '✅' : '❌'; ?></td>
                        <td><?php echo htmlspecialchars($recentCode['status'] ?? ''); ?></td>
                        <td><?php echo htmlspecialchars($recentCode['created_at'] ?? ''); ?></td>
                        <td><?php echo htmlspecialchars($recentCode['expires_at'] ?? ''); ?></td>
                        <td><?php echo htmlspecialchars($recentCode['username'] ?? '未绑定'); ?></td>
                        <td><a href="?code=<?php echo urlencode($recentCode['code'] ?? ''); ?>">诊断</a></td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="7">无法获取激活码列表</td>
                </tr>
            <?php endif; ?>
        </table>
    </div>
    
    <div class="section">
        <h2>🔧 数据库一致性检查</h2>
        <?php if (isset($diagnosis['consistency_check']) && is_array($diagnosis['consistency_check'])): ?>
            <?php $consistency = $diagnosis['consistency_check']; ?>

            <?php if (isset($consistency['error'])): ?>
                <div class="error-box">
                    <p class="error">❌ 一致性检查失败: <?php echo htmlspecialchars($consistency['error']); ?></p>
                </div>
            <?php else: ?>
                <?php
                $totalIssues = ($consistency['status_inconsistency'] ?? 0) +
                              ($consistency['binding_inconsistency'] ?? 0) +
                              ($consistency['expired_active_codes'] ?? 0);
                ?>
                <div class="<?php echo $totalIssues > 0 ? 'warning-box' : 'highlight'; ?>">
                    <h3><?php echo $totalIssues > 0 ? '⚠️ 发现数据不一致' : '✅ 数据一致性良好'; ?></h3>
                    <ul>
                        <li><strong>状态不一致:</strong> <?php echo $consistency['status_inconsistency'] ?? 0; ?> 条记录</li>
                        <li><strong>绑定不一致:</strong> <?php echo $consistency['binding_inconsistency'] ?? 0; ?> 条记录</li>
                        <li><strong>过期但仍活跃:</strong> <?php echo $consistency['expired_active_codes'] ?? 0; ?> 条记录</li>
                    </ul>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="error-box">
                <p class="error">❌ 无法执行一致性检查</p>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="section">
        <h2>💡 问题解决建议</h2>
        
        <h3>常见问题及解决方案:</h3>
        
        <div class="warning-box">
            <h4>🔴 问题1: 激活码显示已被使用但数据库中未绑定</h4>
            <p><strong>可能原因:</strong></p>
            <ul>
                <li>数据库事务未正确提交</li>
                <li>多个进程同时访问数据库</li>
                <li>服务器时区设置问题</li>
                <li>数据库文件权限问题</li>
            </ul>
            <p><strong>解决方案:</strong></p>
            <ul>
                <li>检查数据库文件权限（应为664或666）</li>
                <li>确保服务器和本地时区一致</li>
                <li>重新生成激活码并测试</li>
                <li>检查SQLite版本兼容性</li>
            </ul>
        </div>
        
        <div class="highlight">
            <h4>🔧 修复数据不一致的SQL命令:</h4>
            <pre>-- 修复状态不一致
UPDATE activation_codes SET status = 'used' WHERE is_used = 1 AND status != 'used';
UPDATE activation_codes SET status = 'active' WHERE is_used = 0 AND status = 'used';

-- 修复过期激活码状态
UPDATE activation_codes SET status = 'expired' WHERE expires_at <= datetime('now') AND status = 'active';</pre>
        </div>
    </div>
    
    <div class="section">
        <h2>🔗 相关工具</h2>
        <ul>
            <li><a href="activation-code-manager.php">激活码管理界面</a></li>
            <li><a href="database-repair.php">数据库修复工具</a></li>
            <li><a href="../server/generate_activation_code.py">生成新激活码</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
