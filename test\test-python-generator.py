#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的Python激活码生成器
"""

import sys
import os

# 添加server目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'server'))

try:
    from generate_activation_code import ActivationCodeGenerator
    
    print("🧪 测试Python激活码生成器")
    print("=" * 50)
    
    # 创建生成器实例
    generator = ActivationCodeGenerator()
    
    print("✅ 成功导入激活码生成器")
    
    # 测试生成单个激活码
    print("\n📝 测试生成单个激活码:")
    result = generator.create_activation_code(30)
    
    if result:
        print("✅ 单个激活码生成测试成功")
    else:
        print("❌ 单个激活码生成测试失败")
    
    # 测试查看激活码
    print("\n📋 测试查看有效激活码:")
    codes = generator.list_active_codes()
    
    # 测试数据库统计
    print("\n📊 测试数据库统计:")
    stats = generator.get_database_stats()
    
    print("\n🎉 所有测试完成!")
    
except ImportError as e:
    print(f"❌ 导入失败: {e}")
except Exception as e:
    print(f"❌ 测试失败: {e}")
