# 验证码显示问题解决指南

## 问题描述
验证码在本地环境（localhost:8080）能正常显示，但在服务器上无法显示。

## 可能原因
1. **GD扩展未安装或未启用** - 最常见原因（99%的情况）
2. **PHP配置问题** - php.ini配置不正确
3. **文件权限问题** - 临时文件无法创建
4. **内存限制** - PHP内存不足
5. **服务器环境差异** - 本地和服务器环境不一致

## 快速诊断步骤

### 第一步：运行诊断工具
访问以下页面进行快速诊断：
```
http://你的域名/test/quick-server-test.html
```

### 第二步：详细环境检查
1. **服务器环境全面检查**
   ```
   http://你的域名/test/server-check.php
   ```
   
2. **GD扩展专项检查**
   ```
   http://你的域名/test/check-gd-extension.php
   ```

### 第三步：测试验证码格式
测试以下不同格式的验证码：
- 原始验证码：`/api/captcha.php`
- SVG验证码：`/api/captcha-fallback.php?format=svg`
- 自动检测：`/api/captcha-fallback.php`
- 文本验证码：`/api/captcha-fallback.php?format=text`

## 解决方案

### 方案一：立即可用（推荐）
如果GD扩展确实有问题，可以立即使用SVG验证码：

**前端代码修改：**
```javascript
// 原来的验证码URL
// src="../api/captcha.php"

// 改为SVG验证码
src="../api/captcha-fallback.php?format=svg"
```

**优点：**
- 无需服务器配置
- 立即可用
- 兼容性好

**缺点：**
- 安全性略低于图像验证码

### 方案二：修复GD扩展（根本解决）

#### Linux服务器
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install php-gd

# CentOS/RHEL
sudo yum install php-gd
# 或者
sudo dnf install php-gd

# 重启Web服务器
sudo systemctl restart apache2
# 或者
sudo systemctl restart nginx
```

#### Windows服务器
1. 编辑 `php.ini` 文件
2. 找到并取消注释：
   ```ini
   extension=gd
   ```
3. 重启Web服务器

#### 共享主机
联系主机商启用GD扩展

### 方案三：自动切换（最佳）
使用智能检测API，自动选择最佳方案：

```javascript
// 使用自动检测验证码
src="../api/captcha-fallback.php"
```

这个API会：
1. 检测GD扩展是否可用
2. 如果可用，使用原始图像验证码
3. 如果不可用，自动切换到SVG验证码

## 验证修复结果

### 检查GD扩展状态
```php
<?php
if (extension_loaded('gd')) {
    echo "GD扩展已加载";
    print_r(gd_info());
} else {
    echo "GD扩展未加载";
}
?>
```

### 测试图像生成
```php
<?php
if (function_exists('imagecreate')) {
    $img = imagecreate(100, 30);
    $bg = imagecolorallocate($img, 255, 255, 255);
    $text = imagecolorallocate($img, 0, 0, 0);
    imagestring($img, 5, 10, 5, 'TEST', $text);
    
    header('Content-Type: image/png');
    imagepng($img);
    imagedestroy($img);
    echo "图像生成成功";
} else {
    echo "图像函数不可用";
}
?>
```

## 常见错误和解决方法

### 错误1：Call to undefined function imagecreate()
**原因：** GD扩展未安装
**解决：** 安装GD扩展并重启服务器

### 错误2：验证码显示为叉号或空白
**原因：** 图像生成失败
**解决：** 检查文件权限和内存限制

### 错误3：Fatal error: Allowed memory size exhausted
**原因：** PHP内存不足
**解决：** 增加PHP内存限制
```ini
memory_limit = 256M
```

### 错误4：Warning: Cannot modify header information
**原因：** 输出缓冲问题
**解决：** 检查验证码文件前是否有多余输出

## 预防措施

### 部署前检查清单
- [ ] 确认服务器支持GD扩展
- [ ] 测试图像生成功能
- [ ] 检查文件权限设置
- [ ] 验证PHP配置参数
- [ ] 准备备用验证码方案

### 监控建议
- 定期检查验证码功能
- 监控服务器错误日志
- 设置验证码失败率告警
- 准备多种验证码方案

## 技术支持

如果按照以上步骤仍无法解决问题，请：

1. 运行所有诊断工具并截图保存结果
2. 记录服务器环境信息（PHP版本、操作系统等）
3. 检查服务器错误日志
4. 联系服务器管理员或主机商技术支持

## 相关文件

- `test/quick-server-test.html` - 快速测试页面
- `test/server-check.php` - 服务器环境检查
- `test/check-gd-extension.php` - GD扩展详细检查
- `api/captcha-fallback.php` - 备用验证码方案
- `api/captcha.php` - 原始验证码

---

**最后更新：** 2025-07-24  
**版本：** 1.0
