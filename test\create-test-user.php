<?php
/**
 * 创建测试用户工具
 */

header('Content-Type: text/html; charset=utf-8');

$dbPath = '../server/user_system.db3';

function createTestUser($username, $email, $password) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 检查用户是否已存在
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ? OR email = ?");
        $stmt->execute([$username, $email]);
        
        if ($stmt->fetchColumn() > 0) {
            return ['success' => false, 'message' => '用户名或邮箱已存在'];
        }
        
        // 创建用户
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password_hash, status, created_at)
            VALUES (?, ?, ?, 'active', datetime('now'))
        ");
        
        $stmt->execute([$username, $email, $passwordHash]);
        $userId = $pdo->lastInsertId();
        
        return [
            'success' => true,
            'message' => '用户创建成功',
            'user_id' => $userId,
            'username' => $username,
            'email' => $email
        ];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => '创建失败: ' . $e->getMessage()];
    }
}

$result = null;
if (isset($_POST['action']) && $_POST['action'] === 'create' && !empty($_POST['username']) && !empty($_POST['email']) && !empty($_POST['password'])) {
    $result = createTestUser($_POST['username'], $_POST['email'], $_POST['password']);
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建测试用户</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .form { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .form input { padding: 8px; margin: 5px; width: 200px; }
        .form button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .form button:hover { background: #0056b3; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
        .error-box { background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0; }
    </style>
</head>
<body>
    <h1>🧪 创建测试用户</h1>
    
    <div class="form">
        <h2>创建新用户</h2>
        <form method="POST">
            <input type="hidden" name="action" value="create">
            <div>
                <label>用户名:</label><br>
                <input type="text" name="username" value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
            </div>
            <div>
                <label>邮箱:</label><br>
                <input type="email" name="email" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
            </div>
            <div>
                <label>密码:</label><br>
                <input type="password" name="password" value="<?php echo htmlspecialchars($_POST['password'] ?? ''); ?>" required>
            </div>
            <div>
                <button type="submit">创建用户</button>
            </div>
        </form>
    </div>
    
    <?php if ($result): ?>
        <div class="<?php echo $result['success'] ? 'highlight' : 'error-box'; ?>">
            <h3><?php echo $result['success'] ? '✅ 创建成功' : '❌ 创建失败'; ?></h3>
            <p><?php echo htmlspecialchars($result['message']); ?></p>
            <?php if ($result['success']): ?>
                <p><strong>用户ID:</strong> <?php echo $result['user_id']; ?></p>
                <p><strong>用户名:</strong> <?php echo htmlspecialchars($result['username']); ?></p>
                <p><strong>邮箱:</strong> <?php echo htmlspecialchars($result['email']); ?></p>
                <p><a href="activation-binding-test.php">返回绑定测试</a></p>
            <?php endif; ?>
        </div>
    <?php endif; ?>
    
    <div>
        <h3>建议的测试用户信息:</h3>
        <ul>
            <li><strong>用户名:</strong> testuser<?php echo date('His'); ?></li>
            <li><strong>邮箱:</strong> test<?php echo date('His'); ?>@example.com</li>
            <li><strong>密码:</strong> 123456</li>
        </ul>
    </div>
    
    <p><a href="activation-binding-test.php">返回绑定测试</a></p>
</body>
</html>
