<?php
/**
 * 邮件发送类
 * 使用原生PHP实现SMTP邮件发送，无需外部依赖
 */

require_once 'email-config.php';

class EmailSender {
    private $config;
    private $socket;
    private $lastResponse;
    
    public function __construct() {
        $this->config = getEmailConfig();
    }
    
    /**
     * 发送邮件
     */
    public function sendEmail($to, $subject, $body, $isHtml = true) {
        try {
            // 连接SMTP服务器
            $this->connect();
            
            // SMTP认证
            $this->authenticate();
            
            // 发送邮件
            $this->sendMessage($to, $subject, $body, $isHtml);
            
            // 关闭连接
            $this->disconnect();
            
            return [
                'success' => true,
                'message' => '邮件发送成功'
            ];

        } catch (Exception $e) {
            $this->disconnect();
            error_log("邮件发送失败: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'debug' => $e->getFile() . ':' . $e->getLine()
            ];
        }
    }
    
    /**
     * 连接SMTP服务器
     */
    private function connect() {
        $host = $this->config['smtp_host'];
        $port = $this->config['smtp_port'];
        $secure = $this->config['smtp_secure'];
        
        // 创建socket连接
        if ($secure === 'ssl') {
            $host = "ssl://$host";
        }
        
        $this->socket = fsockopen($host, $port, $errno, $errstr, 30);
        
        if (!$this->socket) {
            throw new Exception("无法连接SMTP服务器: $errstr ($errno)");
        }
        
        // 读取欢迎消息
        $this->readResponse();
        
        // 发送EHLO命令
        $hostname = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $this->sendCommand("EHLO " . $hostname);
        
        // 如果使用TLS，启动加密
        if ($secure === 'tls') {
            $this->sendCommand("STARTTLS");
            if (!stream_socket_enable_crypto($this->socket, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
                throw new Exception("启动TLS加密失败");
            }
            // 重新发送EHLO
            $hostname = $_SERVER['HTTP_HOST'] ?? 'localhost';
            $this->sendCommand("EHLO " . $hostname);
        }
    }
    
    /**
     * SMTP认证
     */
    private function authenticate() {
        $this->sendCommand("AUTH LOGIN");
        $this->sendCommand(base64_encode($this->config['username']));
        $this->sendCommand(base64_encode($this->config['password']));
    }
    
    /**
     * 发送邮件消息
     */
    private function sendMessage($to, $subject, $body, $isHtml) {
        // 设置发件人
        $this->sendCommand("MAIL FROM: <{$this->config['from_email']}>");
        
        // 设置收件人
        $this->sendCommand("RCPT TO: <$to>");
        
        // 开始数据传输
        $this->sendCommand("DATA");
        
        // 构建邮件头
        $headers = $this->buildHeaders($to, $subject, $isHtml);
        
        // 发送邮件头和正文
        $message = $headers . "\r\n" . $body . "\r\n.";
        $this->sendRaw($message);
        $this->readResponse();
    }
    
    /**
     * 构建邮件头
     */
    private function buildHeaders($to, $subject, $isHtml) {
        $fromName = $this->config['from_name'];
        $fromEmail = $this->config['from_email'];
        
        $headers = [];
        $headers[] = "From: $fromName <$fromEmail>";
        $headers[] = "To: $to";
        $headers[] = "Subject: =?UTF-8?B?" . base64_encode($subject) . "?=";
        $headers[] = "Date: " . date('r');
        $hostname = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $headers[] = "Message-ID: <" . uniqid() . "@" . $hostname . ">";
        $headers[] = "MIME-Version: 1.0";
        
        if ($isHtml) {
            $headers[] = "Content-Type: text/html; charset=UTF-8";
        } else {
            $headers[] = "Content-Type: text/plain; charset=UTF-8";
        }
        
        $headers[] = "Content-Transfer-Encoding: 8bit";
        
        return implode("\r\n", $headers);
    }
    
    /**
     * 发送SMTP命令
     */
    private function sendCommand($command) {
        $this->sendRaw($command . "\r\n");
        $response = $this->readResponse();
        
        // 检查响应码
        $code = substr($response, 0, 3);
        if ($code >= 400) {
            throw new Exception("SMTP错误: $response");
        }
        
        return $response;
    }
    
    /**
     * 发送原始数据
     */
    private function sendRaw($data) {
        if (!fwrite($this->socket, $data)) {
            throw new Exception("发送数据失败");
        }
    }
    
    /**
     * 读取服务器响应
     */
    private function readResponse() {
        $response = '';
        while (($line = fgets($this->socket, 515)) !== false) {
            $response .= $line;
            if (substr($line, 3, 1) === ' ') {
                break;
            }
        }
        
        $this->lastResponse = trim($response);
        return $this->lastResponse;
    }
    
    /**
     * 断开连接
     */
    private function disconnect() {
        if ($this->socket) {
            $this->sendRaw("QUIT\r\n");
            fclose($this->socket);
            $this->socket = null;
        }
    }
    
    /**
     * 获取最后的响应
     */
    public function getLastResponse() {
        return $this->lastResponse;
    }
}

/**
 * 快速发送邮件函数
 */
function sendQuickEmail($to, $subject, $body, $isHtml = true) {
    $sender = new EmailSender();
    return $sender->sendEmail($to, $subject, $body, $isHtml);
}

/**
 * 测试邮件配置
 */
function testEmailConfig($testEmail = null) {
    try {
        $config = validateEmailConfig();
        if (!$config['valid']) {
            return [
                'success' => false,
                'message' => '邮件配置无效: ' . $config['error']
            ];
        }
        
        if ($testEmail) {
            $subject = '邮件配置测试';
            $body = '这是一封测试邮件，用于验证邮件配置是否正确。<br><br>发送时间: ' . date('Y-m-d H:i:s');
            
            $result = sendQuickEmail($testEmail, $subject, $body);
            
            return [
                'success' => $result,
                'message' => $result ? '测试邮件发送成功' : '测试邮件发送失败'
            ];
        }
        
        return [
            'success' => true,
            'message' => '邮件配置验证通过',
            'config' => $config
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '测试失败: ' . $e->getMessage()
        ];
    }
}
?>
