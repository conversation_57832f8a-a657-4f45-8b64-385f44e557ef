<?php
/**
 * Session自动清理机制
 * 在各种API调用时自动清理过期的session
 */

/**
 * 自动清理过期的session
 */
function autoCleanupExpiredSessions($dbPath) {
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 删除过期的session
        $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE expires_at < datetime('now')");
        $stmt->execute();
        $deletedCount = $stmt->rowCount();
        
        if ($deletedCount > 0) {
            error_log("自动清理了 {$deletedCount} 个过期session");
        }
        
        return $deletedCount;
        
    } catch (Exception $e) {
        error_log("自动清理过期session失败: " . $e->getMessage());
        return 0;
    }
}

/**
 * 智能session清理 - 在登录时触发
 */
function smartSessionCleanup($dbPath, $userId = null) {
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $pdo->beginTransaction();
        
        $totalDeleted = 0;
        
        // 1. 清理所有过期session
        $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE expires_at < datetime('now')");
        $stmt->execute();
        $totalDeleted += $stmt->rowCount();
        
        // 2. 如果指定了用户ID，清理该用户的旧session（保留最新3个）
        if ($userId) {
            $stmt = $pdo->prepare("
                DELETE FROM user_sessions 
                WHERE user_id = ? AND id NOT IN (
                    SELECT id FROM (
                        SELECT id FROM user_sessions 
                        WHERE user_id = ? 
                        ORDER BY created_at DESC 
                        LIMIT 3
                    )
                )
            ");
            $stmt->execute([$userId, $userId]);
            $totalDeleted += $stmt->rowCount();
        }
        
        // 3. 清理7天前的非活跃session
        $stmt = $pdo->prepare("
            DELETE FROM user_sessions 
            WHERE is_active = 0 AND created_at < datetime('now', '-7 days')
        ");
        $stmt->execute();
        $totalDeleted += $stmt->rowCount();
        
        $pdo->commit();
        
        if ($totalDeleted > 0) {
            error_log("智能session清理完成: 删除了 {$totalDeleted} 个session");
        }
        
        return $totalDeleted;
        
    } catch (Exception $e) {
        if (isset($pdo)) {
            $pdo->rollBack();
        }
        error_log("智能session清理失败: " . $e->getMessage());
        return 0;
    }
}

/**
 * 检查是否需要执行session清理
 */
function shouldCleanupSessions($dbPath) {
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 获取session统计
        $stmt = $pdo->query("SELECT COUNT(*) FROM user_sessions");
        $totalSessions = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM user_sessions WHERE expires_at < datetime('now')");
        $expiredSessions = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM user_sessions WHERE is_active = 0");
        $inactiveSessions = $stmt->fetchColumn();
        
        // 检查最后清理时间
        $stmt = $pdo->prepare("SELECT config_value FROM system_config WHERE config_key = 'last_session_cleanup'");
        $stmt->execute();
        $lastCleanup = $stmt->fetchColumn();
        
        $needsCleanup = false;
        $reason = '';
        
        // 条件1: 有过期session
        if ($expiredSessions > 0) {
            $needsCleanup = true;
            $reason = "发现 {$expiredSessions} 个过期session";
        }
        
        // 条件2: 总session数过多
        if ($totalSessions > 50) {
            $needsCleanup = true;
            $reason = $reason ? $reason . ", 总session数过多 ({$totalSessions})" : "总session数过多 ({$totalSessions})";
        }
        
        // 条件3: 距离上次清理超过24小时
        if ($lastCleanup) {
            $hoursSinceCleanup = (time() - strtotime($lastCleanup)) / 3600;
            if ($hoursSinceCleanup > 24) {
                $needsCleanup = true;
                $reason = $reason ? $reason . ", 距离上次清理超过24小时" : "距离上次清理超过24小时";
            }
        } else {
            $needsCleanup = true;
            $reason = $reason ? $reason . ", 从未执行过session清理" : "从未执行过session清理";
        }
        
        return [
            'needs_cleanup' => $needsCleanup,
            'reason' => $reason,
            'total_sessions' => $totalSessions,
            'expired_sessions' => $expiredSessions,
            'inactive_sessions' => $inactiveSessions,
            'last_cleanup' => $lastCleanup
        ];
        
    } catch (Exception $e) {
        error_log("检查session清理需求失败: " . $e->getMessage());
        return ['needs_cleanup' => false, 'error' => $e->getMessage()];
    }
}

/**
 * 在API调用时随机触发session清理
 */
function randomSessionCleanup($dbPath, $probability = 5) {
    // 随机触发清理（默认5%概率）
    if (rand(1, 100) > $probability) {
        return null;
    }
    
    $checkResult = shouldCleanupSessions($dbPath);
    
    if ($checkResult['needs_cleanup']) {
        $deletedCount = autoCleanupExpiredSessions($dbPath);
        
        // 更新最后清理时间
        try {
            $pdo = new PDO("sqlite:$dbPath");
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $stmt = $pdo->prepare("
                INSERT OR REPLACE INTO system_config (config_key, config_value, description) 
                VALUES ('last_session_cleanup', ?, '最后一次session清理时间')
            ");
            $stmt->execute([date('Y-m-d H:i:s')]);
            
        } catch (Exception $e) {
            error_log("更新session清理时间失败: " . $e->getMessage());
        }
        
        return [
            'cleaned' => true,
            'deleted_count' => $deletedCount,
            'reason' => $checkResult['reason']
        ];
    }
    
    return null;
}

/**
 * 在用户登录时执行session清理
 */
function loginSessionCleanup($dbPath, $userId) {
    // 登录时总是执行智能清理
    return smartSessionCleanup($dbPath, $userId);
}

/**
 * 获取session清理统计
 */
function getSessionCleanupStats($dbPath) {
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stats = [];
        
        // 基本统计
        $stmt = $pdo->query("SELECT COUNT(*) FROM user_sessions");
        $stats['total_sessions'] = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM user_sessions WHERE is_active = 1");
        $stats['active_sessions'] = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM user_sessions WHERE expires_at < datetime('now')");
        $stats['expired_sessions'] = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM user_sessions WHERE is_active = 0");
        $stats['inactive_sessions'] = $stmt->fetchColumn();
        
        // 最后清理时间
        $stmt = $pdo->prepare("SELECT config_value FROM system_config WHERE config_key = 'last_session_cleanup'");
        $stmt->execute();
        $stats['last_cleanup'] = $stmt->fetchColumn();
        
        // 计算无效session占比
        $stats['waste_ratio'] = $stats['total_sessions'] > 0 ? 
            round((($stats['expired_sessions'] + $stats['inactive_sessions']) / $stats['total_sessions']) * 100, 1) : 0;
        
        return $stats;
        
    } catch (Exception $e) {
        return ['error' => $e->getMessage()];
    }
}
?>
