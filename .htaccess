# =============================================================================
# 倒计时系统 - 主安全配置
# 保护敏感目录和文件
# =============================================================================

# 启用重写引擎
RewriteEngine On

# =============================================================================
# 多品牌路由规则
# =============================================================================

# 品牌路由规则 - 匹配 /brand/index.php 格式
RewriteRule ^(wuling|byd|geely|chery|changan|haval|greatwall|mg)/index\.php$ index.php [L,QSA]

# 品牌路由规则 - 匹配 /brand/index-tv-optimized.php 格式
RewriteRule ^(wuling|byd|geely|chery|changan|haval|greatwall|mg)/index-tv-optimized\.php$ index-tv-optimized.php [L,QSA]

# 品牌路由规则 - 匹配 /brand/ 格式，默认跳转到 index.php
RewriteRule ^(wuling|byd|geely|chery|changan|haval|greatwall|mg)/?$ index.php [L,QSA]

# 根目录访问重定向到品牌选择页面
RewriteRule ^$ brand-selector.html [L]

# =============================================================================
# 目录访问控制
# =============================================================================

# 完全禁止访问vendor目录
RewriteRule ^vendor/.*$ - [F,L]

# 保护敏感目录
RewriteRule ^(docs|test|server|config|logs|backup)/.*$ - [F,L]

# 保护数据库文件
RewriteRule ^.*\.(db|sqlite|sql)$ - [F,L]

# =============================================================================
# 文件类型保护
# =============================================================================

# 保护配置文件
<FilesMatch "\.(ini|conf|config|env|log|bak|backup)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# 保护PHP配置和敏感文件
<FilesMatch "^(\.htaccess|\.htpasswd|composer\.|package\.|gulpfile|webpack|tsconfig)">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# 保护源码管理文件
<FilesMatch "^(\.git|\.svn|\.hg)">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# 保护文档和说明文件（可选）
<FilesMatch "\.(md|txt|readme|changelog|license)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# =============================================================================
# 安全头部
# =============================================================================

<IfModule mod_headers.c>
    # 防止XSS攻击
    Header always set X-XSS-Protection "1; mode=block"
    
    # 防止MIME类型嗅探
    Header always set X-Content-Type-Options nosniff
    
    # 防止点击劫持
    Header always set X-Frame-Options SAMEORIGIN
    
    # 内容安全策略（根据需要调整）
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self';"
    
    # 引用策略
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # 移除服务器信息
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# =============================================================================
# PHP安全配置
# =============================================================================

<IfModule mod_php7.c>
    # 隐藏PHP版本
    php_flag expose_php off
    
    # 禁用危险函数（如果需要）
    # php_admin_value disable_functions "exec,passthru,shell_exec,system,proc_open,popen"
</IfModule>

<IfModule mod_php8.c>
    # 隐藏PHP版本
    php_flag expose_php off
</IfModule>

# =============================================================================
# 缓存控制
# =============================================================================

# 静态资源缓存
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# =============================================================================
# 压缩优化
# =============================================================================

<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# =============================================================================
# 错误页面
# =============================================================================

# 自定义错误页面
ErrorDocument 403 /403.html
ErrorDocument 404 /brand-selector.html
ErrorDocument 500 /500.html

# =============================================================================
# 防止目录浏览
# =============================================================================

Options -Indexes

# =============================================================================
# 允许的公共文件
# =============================================================================

# 允许访问主要页面
<Files "index.html">
    Allow from all
</Files>

<Files "index.php">
    Allow from all
</Files>

<Files "index-tv-optimized.php">
    Allow from all
</Files>

<Files "brand-selector.html">
    Allow from all
</Files>

<Files "brand-selector-after-login.html">
    Allow from all
</Files>

<Files "new-lock.html">
    Allow from all
</Files>

<Files "reset-password.html">
    Allow from all
</Files>

# 允许API访问
<FilesMatch "^api/.*\.php$">
    Allow from all
</FilesMatch>

# 允许静态资源
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$">
    Allow from all
</FilesMatch>
