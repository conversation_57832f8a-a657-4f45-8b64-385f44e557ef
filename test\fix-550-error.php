<?php
/**
 * 550 Invalid User 错误修复工具
 * 专门解决163邮箱的550错误问题
 */

header('Content-Type: text/html; charset=utf-8');

// 处理测试请求
$testResults = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_multiple'])) {
    $testEmails = [
        '<EMAIL>', // 发件人自己
        '<EMAIL>',       // 用户的QQ邮箱
        '<EMAIL>',           // 测试163邮箱
        '<EMAIL>',            // 测试QQ邮箱
        '<EMAIL>'          // 测试Gmail
    ];
    
    foreach ($testEmails as $email) {
        $testResults[$email] = testSingleEmail($email);
    }
}

function testSingleEmail($toEmail) {
    try {
        require_once '../server/email-config.php';
        $config = getEmailConfig();
        
        // 建立SSL连接
        $context = stream_context_create([
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            ]
        ]);
        
        $socket = stream_socket_client("ssl://{$config['smtp_host']}:{$config['smtp_port']}", $errno, $errstr, 10, STREAM_CLIENT_CONNECT, $context);
        
        if (!$socket) {
            return ['success' => false, 'error' => "连接失败: $errstr"];
        }
        
        // 读取欢迎消息
        $welcome = fgets($socket);
        
        // EHLO
        fwrite($socket, "EHLO localhost\r\n");
        $response = '';
        while ($line = fgets($socket)) {
            $response .= $line;
            if (substr($line, 3, 1) === ' ') break;
        }
        
        // AUTH LOGIN
        fwrite($socket, "AUTH LOGIN\r\n");
        fgets($socket);
        
        // 用户名
        fwrite($socket, base64_encode($config['username']) . "\r\n");
        fgets($socket);
        
        // 密码
        fwrite($socket, base64_encode($config['password']) . "\r\n");
        $authResponse = fgets($socket);
        
        if (substr($authResponse, 0, 3) !== '235') {
            fclose($socket);
            return ['success' => false, 'error' => "认证失败: $authResponse"];
        }
        
        // MAIL FROM
        fwrite($socket, "MAIL FROM: <{$config['from_email']}>\r\n");
        $mailFromResponse = fgets($socket);
        
        if (substr($mailFromResponse, 0, 3) !== '250') {
            fclose($socket);
            return ['success' => false, 'error' => "MAIL FROM失败: $mailFromResponse"];
        }
        
        // RCPT TO - 关键测试点
        fwrite($socket, "RCPT TO: <$toEmail>\r\n");
        $rcptResponse = fgets($socket);
        
        // QUIT
        fwrite($socket, "QUIT\r\n");
        fgets($socket);
        fclose($socket);
        
        if (substr($rcptResponse, 0, 3) === '250') {
            return ['success' => true, 'message' => '收件人验证成功', 'response' => trim($rcptResponse)];
        } else {
            return ['success' => false, 'error' => trim($rcptResponse), 'is_550' => substr($rcptResponse, 0, 3) === '550'];
        }
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// 检查163邮箱配置
function check163Config() {
    try {
        require_once '../server/email-config.php';
        $config = getEmailConfig();
        
        return [
            'valid' => true,
            'username' => $config['username'],
            'from_email' => $config['from_email'],
            'smtp_host' => $config['smtp_host'],
            'smtp_port' => $config['smtp_port']
        ];
    } catch (Exception $e) {
        return ['valid' => false, 'error' => $e->getMessage()];
    }
}

$configStatus = check163Config();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>550错误修复工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
        button { padding: 8px 16px; margin: 5px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .fix-box { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 15px 0; }
    </style>
</head>
<body>
    <h1>🔧 550 Invalid User 错误修复工具</h1>
    <p><strong>修复时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="fix-box">
        <h3>⚠️ 问题分析</h3>
        <p><strong>错误信息:</strong> <code>550 Invalid User</code></p>
        <p><strong>问题原因:</strong> 163邮箱服务器拒绝了收件人邮箱地址</p>
        <p><strong>可能原因:</strong></p>
        <ul>
            <li>收件人邮箱地址不存在或格式错误</li>
            <li>163邮箱的反垃圾邮件策略限制</li>
            <li>发件人邮箱信誉度问题</li>
            <li>收件人域名被163邮箱限制</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>📊 当前163邮箱配置</h2>
        <?php if ($configStatus['valid']): ?>
            <table>
                <tr><th>配置项</th><th>值</th></tr>
                <tr><td>用户名</td><td><?php echo $configStatus['username']; ?></td></tr>
                <tr><td>发件人</td><td><?php echo $configStatus['from_email']; ?></td></tr>
                <tr><td>SMTP服务器</td><td><?php echo $configStatus['smtp_host']; ?>:<?php echo $configStatus['smtp_port']; ?></td></tr>
            </table>
        <?php else: ?>
            <p class="error">❌ 配置无效: <?php echo $configStatus['error']; ?></p>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>🧪 批量收件人测试</h2>
        <p>测试不同类型的收件人邮箱，找出可以成功发送的邮箱类型：</p>
        
        <form method="POST">
            <button type="submit" name="test_multiple">测试多个收件人邮箱</button>
        </form>
        
        <?php if (!empty($testResults)): ?>
            <h3>测试结果:</h3>
            <table>
                <tr>
                    <th>收件人邮箱</th>
                    <th>邮箱类型</th>
                    <th>测试结果</th>
                    <th>详细信息</th>
                </tr>
                <?php foreach ($testResults as $email => $result): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($email); ?></td>
                        <td>
                            <?php 
                            if (strpos($email, '@163.com') !== false) echo '163邮箱';
                            elseif (strpos($email, '@qq.com') !== false) echo 'QQ邮箱';
                            elseif (strpos($email, '@gmail.com') !== false) echo 'Gmail';
                            else echo '其他';
                            ?>
                        </td>
                        <td class="<?php echo $result['success'] ? 'success' : 'error'; ?>">
                            <?php echo $result['success'] ? '✅ 成功' : '❌ 失败'; ?>
                        </td>
                        <td>
                            <?php 
                            if ($result['success']) {
                                echo htmlspecialchars($result['message']);
                            } else {
                                echo htmlspecialchars($result['error']);
                                if (isset($result['is_550']) && $result['is_550']) {
                                    echo ' <span class="warning">(550错误)</span>';
                                }
                            }
                            ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </table>
            
            <?php
            $successCount = count(array_filter($testResults, function($r) { return $r['success']; }));
            $totalCount = count($testResults);
            ?>
            
            <div class="fix-box">
                <h3>📊 测试总结</h3>
                <p><strong>成功率:</strong> <?php echo $successCount; ?>/<?php echo $totalCount; ?> (<?php echo round($successCount/$totalCount*100, 1); ?>%)</p>
                
                <?php if ($successCount > 0): ?>
                    <p class="success">✅ 找到可用的收件人类型，SMTP配置正常</p>
                    <p><strong>建议:</strong> 使用测试成功的邮箱类型作为收件人</p>
                <?php else: ?>
                    <p class="error">❌ 所有收件人都失败，可能是163邮箱配置问题</p>
                    <p><strong>建议:</strong> 检查163邮箱授权码或更换邮件服务商</p>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>🔧 解决方案</h2>
        
        <h3>方案1: 更换邮件服务商 (推荐)</h3>
        <p>如果163邮箱限制较多，建议切换到QQ邮箱或Gmail：</p>
        <pre>
// 在 server/email-config.php 中修改
'provider' => 'qq',  // 或 'outlook'
        </pre>
        
        <h3>方案2: 使用163邮箱发送到163邮箱</h3>
        <p>163邮箱通常允许发送到同域名邮箱，建议：</p>
        <ul>
            <li>测试收件人使用163邮箱地址</li>
            <li>确保收件人邮箱真实存在</li>
        </ul>
        
        <h3>方案3: 检查163邮箱设置</h3>
        <ol>
            <li>登录163邮箱网页版</li>
            <li>检查是否有发送限制</li>
            <li>重新生成授权码</li>
            <li>确认SMTP服务正常启用</li>
        </ol>
        
        <h3>方案4: 修改邮件内容</h3>
        <p>163邮箱可能对邮件内容有检查，建议：</p>
        <ul>
            <li>使用简单的邮件内容</li>
            <li>避免可疑的关键词</li>
            <li>添加合适的邮件头信息</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关工具</h2>
        <ul>
            <li><a href="smtp-detailed-debug.php">SMTP详细调试</a></li>
            <li><a href="debug-email-sending.php">邮件发送调试</a></li>
            <li><a href="test-forgot-password-fixed.php">测试密码找回功能</a></li>
            <li><a href="../new-lock.html">返回登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
