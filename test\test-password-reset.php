<?php
/**
 * 测试密码找回邮件功能
 */

echo "🧪 测试密码找回邮件功能...\n\n";

// 包含必要的文件
require_once '../server/email-config.php';
require_once '../server/email-sender.php';

// 测试邮件配置
echo "1. 检查邮件配置:\n";
echo "==================\n";

try {
    $config = validateEmailConfig();
    
    if ($config['valid']) {
        echo "✅ 邮件配置有效\n";
        echo "   服务商: " . $config['provider'] . "\n";
        echo "   SMTP: " . $config['smtp_host'] . ":" . $config['smtp_port'] . "\n";
        echo "   用户名: " . $config['username'] . "\n";
        echo "   发件人: " . $config['from_email'] . "\n";
    } else {
        echo "❌ 邮件配置无效: " . $config['error'] . "\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "❌ 配置检查失败: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n" . str_repeat("=", 50) . "\n\n";

// 测试密码重置邮件发送
echo "2. 测试密码重置邮件:\n";
echo "==================\n";

$testEmail = '<EMAIL>';
$testUsername = 'ataehee1';
$testToken = 'test_token_' . time();

try {
    $emailSender = new EmailSender();
    
    // 构造重置邮件内容
    $subject = '密码重置请求';
    $resetLink = "http://localhost/reset-password.php?token=" . $testToken;
    
    $body = "
    <html>
    <head>
        <meta charset='UTF-8'>
    </head>
    <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
        <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
            <h2 style='color: #007bff;'>🔐 密码重置请求</h2>
            
            <p>亲爱的 <strong>{$testUsername}</strong>，</p>
            
            <p>我们收到了您的密码重置请求。如果这是您本人的操作，请点击下面的链接重置密码：</p>
            
            <div style='text-align: center; margin: 30px 0;'>
                <a href='{$resetLink}' 
                   style='background: #007bff; color: white; padding: 12px 30px; 
                          text-decoration: none; border-radius: 5px; display: inline-block;'>
                    重置密码
                </a>
            </div>
            
            <p><strong>重要提醒：</strong></p>
            <ul>
                <li>此链接将在 <strong>24小时</strong> 后失效</li>
                <li>如果您没有请求重置密码，请忽略此邮件</li>
                <li>为了您的账户安全，请不要将此链接分享给他人</li>
            </ul>
            
            <p>如果上面的按钮无法点击，请复制以下链接到浏览器地址栏：</p>
            <p style='background: #f8f9fa; padding: 10px; border-radius: 5px; word-break: break-all;'>
                {$resetLink}
            </p>
            
            <hr style='margin: 30px 0; border: none; border-top: 1px solid #eee;'>
            
            <p style='color: #666; font-size: 14px;'>
                此邮件由系统自动发送，请勿回复。<br>
                如有疑问，请联系系统管理员。
            </p>
        </div>
    </body>
    </html>
    ";
    
    echo "发送测试邮件到: $testEmail\n";
    echo "用户名: $testUsername\n";
    echo "重置令牌: $testToken\n\n";
    
    $result = $emailSender->sendEmail($testEmail, $subject, $body);
    
    if ($result['success']) {
        echo "✅ 密码重置邮件发送成功！\n";
        echo "   收件人: $testEmail\n";
        echo "   主题: $subject\n";
        echo "   请检查邮箱收件箱\n";
    } else {
        echo "❌ 邮件发送失败: " . $result['message'] . "\n";
        if (isset($result['debug'])) {
            echo "   调试信息: " . $result['debug'] . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ 发送异常: " . $e->getMessage() . "\n";
}

echo "\n🔧 如果测试成功，密码找回功能应该可以正常工作了！\n";
?>
