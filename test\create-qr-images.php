<?php
/**
 * 创建二维码图片占位符
 * 为付费解锁系统创建支付宝和QQ二维码的占位图片
 */

// 确保images目录存在
$imagesDir = '../images';
if (!is_dir($imagesDir)) {
    mkdir($imagesDir, 0755, true);
}

// 创建支付宝付款二维码占位图
function createAlipayQR() {
    global $imagesDir;
    
    $width = 200;
    $height = 200;
    
    // 创建画布
    $image = imagecreate($width, $height);
    
    // 定义颜色
    $white = imagecolorallocate($image, 255, 255, 255);
    $blue = imagecolorallocate($image, 0, 123, 255);
    $gray = imagecolorallocate($image, 128, 128, 128);
    
    // 填充背景
    imagefill($image, 0, 0, $white);
    
    // 绘制边框
    imagerectangle($image, 0, 0, $width-1, $height-1, $blue);
    imagerectangle($image, 5, 5, $width-6, $height-6, $blue);
    
    // 绘制二维码模拟图案
    for ($i = 20; $i < $width-20; $i += 10) {
        for ($j = 20; $j < $height-20; $j += 10) {
            if (rand(0, 1)) {
                imagefilledrectangle($image, $i, $j, $i+8, $j+8, $blue);
            }
        }
    }
    
    // 添加文字
    $text = "支付宝付款码";
    $font_size = 3;
    $text_width = imagefontwidth($font_size) * strlen($text);
    $x = ($width - $text_width) / 2;
    $y = $height - 25;
    imagestring($image, $font_size, $x, $y, $text, $blue);
    
    $text2 = "¥19.9";
    $text2_width = imagefontwidth(5) * strlen($text2);
    $x2 = ($width - $text2_width) / 2;
    $y2 = 10;
    imagestring($image, 5, $x2, $y2, $text2, $blue);
    
    // 保存图片
    $filename = $imagesDir . '/alipay-qr.png';
    imagepng($image, $filename);
    imagedestroy($image);
    
    echo "✅ 支付宝二维码创建成功: $filename\n";
}

// 创建QQ联系二维码占位图
function createQQQR() {
    global $imagesDir;
    
    $width = 200;
    $height = 200;
    
    // 创建画布
    $image = imagecreate($width, $height);
    
    // 定义颜色
    $white = imagecolorallocate($image, 255, 255, 255);
    $qq_blue = imagecolorallocate($image, 18, 183, 245);
    $gray = imagecolorallocate($image, 128, 128, 128);
    
    // 填充背景
    imagefill($image, 0, 0, $white);
    
    // 绘制边框
    imagerectangle($image, 0, 0, $width-1, $height-1, $qq_blue);
    imagerectangle($image, 5, 5, $width-6, $height-6, $qq_blue);
    
    // 绘制二维码模拟图案
    for ($i = 20; $i < $width-20; $i += 8) {
        for ($j = 20; $j < $height-20; $j += 8) {
            if (rand(0, 1)) {
                imagefilledrectangle($image, $i, $j, $i+6, $j+6, $qq_blue);
            }
        }
    }
    
    // 添加QQ图标模拟（简单的圆形）
    $center_x = $width / 2;
    $center_y = $height / 2;
    imagefilledellipse($image, $center_x, $center_y, 30, 30, $white);
    imageellipse($image, $center_x, $center_y, 30, 30, $qq_blue);
    
    // 添加文字
    $text = "QQ联系客服";
    $font_size = 3;
    $text_width = imagefontwidth($font_size) * strlen($text);
    $x = ($width - $text_width) / 2;
    $y = $height - 25;
    imagestring($image, $font_size, $x, $y, $text, $qq_blue);
    
    $text2 = "QQ";
    $text2_width = imagefontwidth(5) * strlen($text2);
    $x2 = ($width - $text2_width) / 2;
    $y2 = 10;
    imagestring($image, 5, $x2, $y2, $text2, $qq_blue);
    
    // 保存图片
    $filename = $imagesDir . '/qq-qr.png';
    imagepng($image, $filename);
    imagedestroy($image);
    
    echo "✅ QQ二维码创建成功: $filename\n";
}

// 创建favicon
function createFavicon() {
    global $imagesDir;
    
    $width = 32;
    $height = 32;
    
    // 创建画布
    $image = imagecreate($width, $height);
    
    // 定义颜色
    $white = imagecolorallocate($image, 255, 255, 255);
    $red = imagecolorallocate($image, 255, 102, 102);
    $dark_red = imagecolorallocate($image, 204, 51, 51);
    
    // 填充背景
    imagefill($image, 0, 0, $red);
    
    // 绘制锁图标
    // 锁身
    imagefilledrectangle($image, 8, 16, 24, 28, $dark_red);
    
    // 锁环
    imagearc($image, 16, 12, 12, 8, 0, 180, $dark_red);
    imagearc($image, 16, 12, 10, 6, 0, 180, $white);
    
    // 锁孔
    imagefilledellipse($image, 16, 22, 4, 4, $white);
    
    // 保存图片
    $filename = $imagesDir . '/favicon.ico';
    imagepng($image, $filename);
    imagedestroy($image);
    
    echo "✅ Favicon创建成功: $filename\n";
}

echo "🎨 开始创建二维码图片...\n";
echo "=" . str_repeat("=", 40) . "\n";

// 检查GD库
if (!extension_loaded('gd')) {
    echo "❌ 错误: GD库未安装，无法创建图片\n";
    echo "请安装GD库: sudo apt-get install php-gd\n";
    exit(1);
}

// 创建图片
createAlipayQR();
createQQQR();
createFavicon();

echo "\n🎉 所有二维码图片创建完成！\n";
echo "📁 图片保存位置: $imagesDir/\n";
echo "📋 创建的文件:\n";
echo "   - alipay-qr.png (支付宝付款码)\n";
echo "   - qq-qr.png (QQ联系二维码)\n";
echo "   - favicon.ico (网站图标)\n";
echo "\n💡 提示: 请将真实的二维码图片替换这些占位图片\n";
?>
