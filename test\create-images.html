<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建默认图片</title>
</head>
<body>
    <h1>创建默认图片</h1>
    <button onclick="createImages()">创建图片</button>
    <div id="result"></div>
    
    <script>
        function createImages() {
            // 创建logo图片
            const logoCanvas = document.createElement('canvas');
            logoCanvas.width = 200;
            logoCanvas.height = 100;
            const logoCtx = logoCanvas.getContext('2d');
            
            // 绘制logo背景
            logoCtx.fillStyle = '#0066cc';
            logoCtx.fillRect(0, 0, 200, 100);
            
            // 绘制logo文字
            logoCtx.fillStyle = 'white';
            logoCtx.font = 'bold 24px Arial';
            logoCtx.textAlign = 'center';
            logoCtx.fillText('LOGO', 100, 60);
            
            // 创建汽车图片
            const carCanvas = document.createElement('canvas');
            carCanvas.width = 500;
            carCanvas.height = 300;
            const carCtx = carCanvas.getContext('2d');
            
            // 绘制汽车图片背景
            carCtx.fillStyle = '#cccccc';
            carCtx.fillRect(0, 0, 500, 300);
            
            // 绘制汽车图片文字
            carCtx.fillStyle = '#333333';
            carCtx.font = 'bold 32px Arial';
            carCtx.textAlign = 'center';
            carCtx.fillText('汽车图片', 250, 160);
            
            // 转换为下载链接
            const logoDataUrl = logoCanvas.toDataURL('image/png');
            const carDataUrl = carCanvas.toDataURL('image/png');
            
            // 创建下载链接
            const logoLink = document.createElement('a');
            logoLink.href = logoDataUrl;
            logoLink.download = 'logo.png';
            logoLink.textContent = '下载 logo.png';
            logoLink.style.display = 'block';
            logoLink.style.margin = '10px 0';
            
            const carLink = document.createElement('a');
            carLink.href = carDataUrl;
            carLink.download = 'upload1.png';
            carLink.textContent = '下载 upload1.png';
            carLink.style.display = 'block';
            carLink.style.margin = '10px 0';
            
            const result = document.getElementById('result');
            result.innerHTML = '';
            result.appendChild(logoLink);
            result.appendChild(carLink);
            
            // 显示预览
            const logoImg = document.createElement('img');
            logoImg.src = logoDataUrl;
            logoImg.style.maxWidth = '200px';
            logoImg.style.margin = '10px';
            logoImg.style.border = '1px solid #ccc';
            
            const carImg = document.createElement('img');
            carImg.src = carDataUrl;
            carImg.style.maxWidth = '300px';
            carImg.style.margin = '10px';
            carImg.style.border = '1px solid #ccc';
            
            result.appendChild(document.createElement('br'));
            result.appendChild(logoImg);
            result.appendChild(carImg);
            
            alert('图片已生成！请点击下载链接保存到 images 文件夹中。');
        }
    </script>
</body>
</html>
