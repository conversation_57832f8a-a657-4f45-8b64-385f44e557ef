<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件配置测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .warning {
            color: #856404;
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .info {
            color: #0c5460;
            background: #d1ecf1;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
            font-size: 14px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .config-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 15px 0;
            border-radius: 5px;
            background: #f8f9fa;
        }
        
        .provider-tabs {
            display: flex;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            background: #e9ecef;
            border: 1px solid #ddd;
            cursor: pointer;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
        }
        
        .tab.active {
            background: #007bff;
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>📧 邮件配置测试工具</h1>
    
    <div class="container">
        <h2>配置说明</h2>
        <div class="info">
            <strong>📋 使用步骤：</strong><br>
            1. 选择邮件服务商（QQ、Gmail、Outlook、163等）<br>
            2. 填写邮箱账号和授权码<br>
            3. 测试邮件配置<br>
            4. 发送测试邮件验证<br>
            5. 将配置信息复制到 server/email-config.php
        </div>

        <div class="warning">
            <strong>⚠️ 重要提醒：</strong><br>
            • <strong>QQ邮箱</strong>：需要授权码，不是QQ密码<br>
            • <strong>Gmail</strong>：需要开启两步验证并生成应用专用密码<br>
            • <strong>Outlook</strong>：直接使用Microsoft账户密码，支持现代认证<br>
            • <strong>163邮箱</strong>：需要开启SMTP服务并获取授权码
        </div>
    </div>
    
    <div class="container">
        <h2>邮件服务商配置</h2>
        
        <div class="provider-tabs">
            <div class="tab active" onclick="showTab('qq')">QQ邮箱</div>
            <div class="tab" onclick="showTab('gmail')">Gmail</div>
            <div class="tab" onclick="showTab('outlook')">Outlook</div>
            <div class="tab" onclick="showTab('163')">163邮箱</div>
            <div class="tab" onclick="showTab('aliyun')">阿里云邮箱</div>
        </div>
        
        <!-- QQ邮箱配置 -->
        <div id="qq-tab" class="tab-content active">
            <div class="config-section">
                <h3>🐧 QQ邮箱配置</h3>
                <div class="warning">
                    <strong>⚠️ 重要：</strong> QQ邮箱需要使用授权码，不是QQ密码！
                </div>
                
                <div class="form-group">
                    <label>邮箱地址：</label>
                    <input type="email" id="qq-email" placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label>授权码：</label>
                    <input type="password" id="qq-password" placeholder="QQ邮箱授权码">
                </div>
                
                <div class="info">
                    <strong>📖 获取QQ邮箱授权码：</strong><br>
                    1. 登录QQ邮箱 → 设置 → 账户<br>
                    2. 找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"<br>
                    3. 开启"IMAP/SMTP服务"<br>
                    4. 按提示获取授权码<br>
                    5. 将授权码填入上方密码框
                </div>
            </div>
        </div>
        
        <!-- Gmail配置 -->
        <div id="gmail-tab" class="tab-content">
            <div class="config-section">
                <h3>📧 Gmail配置</h3>
                <div class="warning">
                    <strong>⚠️ 重要：</strong> Gmail需要使用应用专用密码！
                </div>

                <div class="form-group">
                    <label>邮箱地址：</label>
                    <input type="email" id="gmail-email" placeholder="<EMAIL>">
                </div>

                <div class="form-group">
                    <label>应用专用密码：</label>
                    <input type="password" id="gmail-password" placeholder="Gmail应用专用密码">
                </div>

                <div class="info">
                    <strong>📖 获取Gmail应用专用密码：</strong><br>
                    1. 开启两步验证<br>
                    2. Google账户 → 安全性 → 应用专用密码<br>
                    3. 选择"邮件"和设备<br>
                    4. 生成16位应用专用密码<br>
                    5. 将密码填入上方密码框
                </div>
            </div>
        </div>

        <!-- Outlook配置 -->
        <div id="outlook-tab" class="tab-content">
            <div class="config-section">
                <h3>📮 Outlook配置</h3>
                <div class="warning">
                    <strong>⚠️ 重要提醒：</strong> Microsoft正在逐步弃用基本认证，如果账户密码失败，请使用应用专用密码！
                </div>

                <div class="form-group">
                    <label>邮箱地址：</label>
                    <input type="email" id="outlook-email" placeholder="<EMAIL> 或 @hotmail.com">
                </div>

                <div class="form-group">
                    <label>账户密码：</label>
                    <input type="password" id="outlook-password" placeholder="Microsoft账户密码">
                </div>

                <div class="info">
                    <strong>📖 Outlook邮箱配置说明：</strong><br>
                    1. 首先尝试使用Microsoft账户密码<br>
                    2. 需要在Outlook.com中启用POP/IMAP访问<br>
                    3. 如果密码不被识别，可能需要应用专用密码<br><br>

                    <strong>🔧 启用POP/IMAP访问：</strong><br>
                    1. 登录 <a href="https://outlook.live.com/mail/0/options/mail/accounts" target="_blank">Outlook.com设置</a><br>
                    2. 转到"邮件" → "同步电子邮件"<br>
                    3. 启用"让设备和应用使用POP"或"让设备和应用使用IMAP"<br><br>

                    <strong>📧 支持的邮箱类型：</strong><br>
                    • @outlook.com<br>
                    • @hotmail.com<br>
                    • @live.com<br>
                    • @msn.com<br><br>

                    <strong>⚠️ 如果基本密码失败：</strong><br>
                    可能需要 <a href="https://support.microsoft.com/en-us/office/add-your-outlook-com-account-to-another-mail-app-or-smart-device-73f3b178-0009-41ae-aab1-87b80fa94970" target="_blank">应用专用密码</a>，
                    因为Microsoft正在逐步弃用基本认证
                </div>
            </div>
        </div>
        
        <!-- 163邮箱配置 -->
        <div id="163-tab" class="tab-content">
            <div class="config-section">
                <h3>📮 163邮箱配置</h3>
                
                <div class="form-group">
                    <label>邮箱地址：</label>
                    <input type="email" id="163-email" placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label>授权码：</label>
                    <input type="password" id="163-password" placeholder="163邮箱授权码">
                </div>
                
                <div class="info">
                    <strong>📖 获取163邮箱授权码：</strong><br>
                    1. 登录163邮箱 → 设置 → POP3/SMTP/IMAP<br>
                    2. 开启"SMTP服务"<br>
                    3. 按提示获取授权码<br>
                    4. 将授权码填入上方密码框
                </div>
            </div>
        </div>
        
        <!-- 阿里云邮箱配置 -->
        <div id="aliyun-tab" class="tab-content">
            <div class="config-section">
                <h3>☁️ 阿里云邮箱配置</h3>
                
                <div class="form-group">
                    <label>邮箱地址：</label>
                    <input type="email" id="aliyun-email" placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label>邮箱密码：</label>
                    <input type="password" id="aliyun-password" placeholder="邮箱登录密码">
                </div>
                
                <div class="info">
                    <strong>📖 阿里云邮箱配置：</strong><br>
                    阿里云邮箱通常直接使用登录密码，无需额外授权码。
                </div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <h2>测试邮件发送</h2>
        
        <div class="form-group">
            <label>测试邮箱地址：</label>
            <input type="email" id="test-email" placeholder="接收测试邮件的邮箱地址">
        </div>
        
        <button onclick="checkEmailConfig()">检查邮件配置</button>
        <button onclick="generateConfig()" class="btn-success">生成配置代码</button>
        <button onclick="sendTestEmail()" class="btn-warning">发送测试邮件</button>
        
        <div id="result"></div>
    </div>
    
    <div class="container">
        <h2>配置代码</h2>
        <div class="info">
            将下面生成的配置代码复制到 <code>server/email-config.php</code> 文件中：
        </div>
        <div id="config-code" class="code-block">
            点击"生成配置代码"按钮生成配置...
        </div>
    </div>

    <script>
        let currentProvider = 'qq';
        
        function showTab(provider) {
            // 隐藏所有标签页
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(provider + '-tab').classList.add('active');
            event.target.classList.add('active');
            
            currentProvider = provider;
        }
        
        function generateConfig() {
            const email = document.getElementById(currentProvider + '-email').value;
            const password = document.getElementById(currentProvider + '-password').value;
            
            if (!email || !password) {
                document.getElementById('result').innerHTML = 
                    '<div class="error">❌ 请填写完整的邮箱和密码信息</div>';
                return;
            }
            
            const configs = {
                qq: {
                    smtp_host: 'smtp.qq.com',
                    smtp_port: 587,
                    smtp_secure: 'tls'
                },
                gmail: {
                    smtp_host: 'smtp.gmail.com',
                    smtp_port: 587,
                    smtp_secure: 'tls'
                },
                outlook: {
                    smtp_host: 'smtp-mail.outlook.com',
                    smtp_port: 587,
                    smtp_secure: 'tls'
                },
                '163': {
                    smtp_host: 'smtp.163.com',
                    smtp_port: 25,
                    smtp_secure: false
                },
                aliyun: {
                    smtp_host: 'smtp.mxhichina.com',
                    smtp_port: 25,
                    smtp_secure: false
                }
            };
            
            const config = configs[currentProvider];
            const secureValue = config.smtp_secure === false ? 'false' : `'${config.smtp_secure}'`;
            
            const configCode = `// 修改 server/email-config.php 中的配置
$emailConfig['provider'] = '${currentProvider}';
$emailConfig['${currentProvider}']['username'] = '${email}';
$emailConfig['${currentProvider}']['password'] = '${password}';
$emailConfig['${currentProvider}']['from_email'] = '${email}';`;
            
            document.getElementById('config-code').textContent = configCode;
            
            document.getElementById('result').innerHTML = 
                '<div class="success">✅ 配置代码已生成，请复制到 server/email-config.php 文件中</div>';
        }
        
        async function sendTestEmail() {
            const testEmail = document.getElementById('test-email').value;

            if (!testEmail) {
                document.getElementById('result').innerHTML =
                    '<div class="error">❌ 请填写测试邮箱地址</div>';
                return;
            }

            document.getElementById('result').innerHTML =
                '<div class="info">🔄 正在发送测试邮件...</div>';

            try {
                const response = await fetch('../api/test-email.php?action=test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: testEmail
                    })
                });

                const data = await response.json();

                if (data.success) {
                    document.getElementById('result').innerHTML =
                        `<div class="success">✅ ${data.message}<br>
                        <strong>邮件服务商:</strong> ${data.data.provider}<br>
                        <strong>SMTP服务器:</strong> ${data.data.smtp_host}</div>`;
                } else {
                    document.getElementById('result').innerHTML =
                        `<div class="error">❌ ${data.message}</div>`;
                }

            } catch (error) {
                document.getElementById('result').innerHTML =
                    `<div class="error">❌ 请求失败: ${error.message}<br>
                    请确保已正确配置邮件设置</div>`;
            }
        }

        async function checkEmailConfig() {
            document.getElementById('result').innerHTML =
                '<div class="info">🔄 正在检查邮件配置...</div>';

            try {
                const response = await fetch('../api/test-email.php?action=check');
                const data = await response.json();

                if (data.success) {
                    document.getElementById('result').innerHTML =
                        `<div class="success">✅ ${data.message}<br>
                        <strong>邮件服务商:</strong> ${data.data.provider}<br>
                        <strong>SMTP服务器:</strong> ${data.data.smtp_host}<br>
                        <strong>发件人:</strong> ${data.data.from_email}</div>`;
                } else {
                    document.getElementById('result').innerHTML =
                        `<div class="error">❌ ${data.message}</div>`;
                }

            } catch (error) {
                document.getElementById('result').innerHTML =
                    `<div class="error">❌ 检查失败: ${error.message}</div>`;
            }
        }
        
        // 页面加载时设置默认值
        window.addEventListener('load', () => {
            // 可以在这里设置一些默认的测试值
        });
    </script>
</body>
</html>
