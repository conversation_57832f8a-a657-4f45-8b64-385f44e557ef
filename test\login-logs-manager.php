<?php
/**
 * login_logs表管理界面
 * 查看统计信息、执行清理操作
 */

header('Content-Type: text/html; charset=utf-8');

// 引入清理助手
require_once '../server/auto-cleanup-helper.php';

$dbPath = '../server/user_system.db3';

$actionResult = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'cleanup') {
        $actionResult = performCleanup();
    } elseif ($action === 'lightweight_cleanup') {
        require_once '../server/auto-cleanup-helper.php';
        $actionResult = performLightweightCleanup($dbPath);
    }
}

function performCleanup() {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $pdo->beginTransaction();
        
        $deletedCount = 0;
        
        // 删除30天前的记录
        $stmt = $pdo->prepare("
            DELETE FROM login_logs 
            WHERE login_time < datetime('now', '-30 days')
        ");
        $stmt->execute();
        $deletedCount += $stmt->rowCount();
        
        // 如果记录数仍然很多，保留最新的1000条
        $stmt = $pdo->query("SELECT COUNT(*) FROM login_logs");
        $currentCount = $stmt->fetchColumn();
        
        if ($currentCount > 1000) {
            $stmt = $pdo->prepare("
                DELETE FROM login_logs 
                WHERE id NOT IN (
                    SELECT id FROM login_logs 
                    ORDER BY login_time DESC 
                    LIMIT 1000
                )
            ");
            $stmt->execute();
            $deletedCount += $stmt->rowCount();
        }
        
        $pdo->commit();
        
        return [
            'success' => true,
            'deleted_count' => $deletedCount,
            'message' => "清理完成，删除了 {$deletedCount} 条记录"
        ];
        
    } catch (Exception $e) {
        if (isset($pdo)) {
            $pdo->rollBack();
        }
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

function getDetailedStats() {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stats = [];
        
        // 基本统计
        $stmt = $pdo->query("SELECT COUNT(*) FROM login_logs");
        $stats['total_records'] = $stmt->fetchColumn();
        
        // 按状态统计
        $stmt = $pdo->query("
            SELECT status, COUNT(*) as count 
            FROM login_logs 
            GROUP BY status 
            ORDER BY count DESC
        ");
        $stats['by_status'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 时间范围统计
        $timeRanges = [
            '1天内' => '-1 days',
            '7天内' => '-7 days',
            '30天内' => '-30 days',
            '90天内' => '-90 days'
        ];
        
        foreach ($timeRanges as $label => $interval) {
            $stmt = $pdo->prepare("
                SELECT COUNT(*) FROM login_logs 
                WHERE login_time >= datetime('now', ?)
            ");
            $stmt->execute([$interval]);
            $stats['time_ranges'][$label] = $stmt->fetchColumn();
        }
        
        // 最新和最旧记录
        $stmt = $pdo->query("
            SELECT MIN(login_time) as oldest, MAX(login_time) as newest 
            FROM login_logs
        ");
        $timeRange = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['oldest_record'] = $timeRange['oldest'];
        $stats['newest_record'] = $timeRange['newest'];
        
        // 数据库大小
        if (file_exists($dbPath)) {
            $stats['db_size'] = filesize($dbPath);
            $stats['db_size_mb'] = round($stats['db_size'] / 1024 / 1024, 2);
        }
        
        // 最后清理时间
        $stmt = $pdo->prepare("
            SELECT config_value FROM system_config 
            WHERE config_key = 'last_cleanup_time'
        ");
        $stmt->execute();
        $stats['last_cleanup'] = $stmt->fetchColumn();
        
        return $stats;
        
    } catch (Exception $e) {
        return ['error' => $e->getMessage()];
    }
}

$stats = getDetailedStats();
$recommendations = getCleanupRecommendation($dbPath);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>login_logs表管理</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        button { padding: 8px 16px; margin: 5px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
        button.danger { background: #f44336; }
        button.danger:hover { background: #da190b; }
        button.warning { background: #ff9800; }
        button.warning:hover { background: #e68900; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
        .warning-box { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; }
        .recommendation { padding: 10px; margin: 5px 0; border-radius: 3px; }
        .rec-high { background: #f8d7da; border-left: 4px solid #dc3545; }
        .rec-medium { background: #fff3cd; border-left: 4px solid #ffc107; }
        .rec-low { background: #d1ecf1; border-left: 4px solid #17a2b8; }
        .rec-info { background: #d4edda; border-left: 4px solid #28a745; }
    </style>
</head>
<body>
    <h1>🗃️ login_logs表管理</h1>
    <p><strong>管理时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <?php if ($actionResult): ?>
    <div class="test-section">
        <h2>📋 操作结果</h2>
        <?php if ($actionResult['success']): ?>
            <div class="success">
                <h3>✅ 操作成功</h3>
                <p><?php echo htmlspecialchars($actionResult['message'] ?? '操作完成'); ?></p>
                <?php if (isset($actionResult['deleted_count'])): ?>
                    <p><strong>删除记录数:</strong> <?php echo number_format($actionResult['deleted_count']); ?> 条</p>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="error">
                <h3>❌ 操作失败</h3>
                <p><?php echo htmlspecialchars($actionResult['error']); ?></p>
            </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>
    
    <div class="test-section">
        <h2>📊 数据库统计</h2>
        
        <?php if (isset($stats['error'])): ?>
            <p class="error">❌ 获取统计信息失败: <?php echo htmlspecialchars($stats['error']); ?></p>
        <?php else: ?>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3><?php echo number_format($stats['total_records']); ?></h3>
                    <p>总记录数</p>
                </div>
                <div class="stat-card">
                    <h3><?php echo $stats['db_size_mb'] ?? 0; ?> MB</h3>
                    <p>数据库大小</p>
                </div>
                <div class="stat-card">
                    <h3><?php echo $stats['time_ranges']['7天内'] ?? 0; ?></h3>
                    <p>最近7天记录</p>
                </div>
                <div class="stat-card">
                    <h3><?php echo $stats['time_ranges']['30天内'] ?? 0; ?></h3>
                    <p>最近30天记录</p>
                </div>
            </div>
            
            <h3>按状态统计:</h3>
            <table>
                <tr>
                    <th>状态</th>
                    <th>记录数</th>
                    <th>占比</th>
                </tr>
                <?php foreach ($stats['by_status'] as $status): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($status['status']); ?></td>
                        <td><?php echo number_format($status['count']); ?></td>
                        <td><?php echo round(($status['count'] / $stats['total_records']) * 100, 1); ?>%</td>
                    </tr>
                <?php endforeach; ?>
            </table>
            
            <h3>时间范围统计:</h3>
            <table>
                <tr>
                    <th>时间范围</th>
                    <th>记录数</th>
                </tr>
                <?php foreach ($stats['time_ranges'] as $range => $count): ?>
                    <tr>
                        <td><?php echo $range; ?></td>
                        <td><?php echo number_format($count); ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
            
            <p><strong>最旧记录:</strong> <?php echo $stats['oldest_record'] ?? '无'; ?></p>
            <p><strong>最新记录:</strong> <?php echo $stats['newest_record'] ?? '无'; ?></p>
            <p><strong>最后清理:</strong> <?php echo $stats['last_cleanup'] ?? '从未清理'; ?></p>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>💡 清理建议</h2>
        
        <?php if (isset($recommendations['error'])): ?>
            <p class="error">❌ 获取建议失败: <?php echo htmlspecialchars($recommendations['error']); ?></p>
        <?php else: ?>
            <?php foreach ($recommendations['recommendations'] as $rec): ?>
                <div class="recommendation rec-<?php echo $rec['level']; ?>">
                    <strong><?php echo ucfirst($rec['level']); ?>:</strong>
                    <?php echo htmlspecialchars($rec['message']); ?>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>🧹 清理操作</h2>
        
        <div class="warning-box">
            <h3>⚠️ 操作说明</h3>
            <p><strong>轻量级清理:</strong> 删除30天前的失败记录，保留重要日志</p>
            <p><strong>完整清理:</strong> 删除30天前的所有记录，保留最新1000条</p>
            <p><strong>注意:</strong> 清理操作不可逆，请谨慎操作</p>
        </div>
        
        <form method="POST" style="display: inline;">
            <input type="hidden" name="action" value="lightweight_cleanup">
            <button type="submit" class="warning" onclick="return confirm('确定要执行轻量级清理吗？')">
                轻量级清理
            </button>
        </form>
        
        <form method="POST" style="display: inline;">
            <input type="hidden" name="action" value="cleanup">
            <button type="submit" class="danger" onclick="return confirm('确定要执行完整清理吗？这将删除大量历史记录！')">
                完整清理
            </button>
        </form>
    </div>
    
    <div class="test-section">
        <h2>⚙️ 自动清理配置</h2>
        
        <h3>推荐配置:</h3>
        <ul>
            <li><strong>最大记录数:</strong> 1000条（平衡性能和历史记录）</li>
            <li><strong>保留时间:</strong> 30天（满足审计需求）</li>
            <li><strong>清理频率:</strong> 每周自动清理一次</li>
            <li><strong>触发条件:</strong> 记录数超过500条时自动清理</li>
        </ul>
        
        <h3>自动清理机制:</h3>
        <ul>
            <li>✅ <strong>登录时触发:</strong> 成功登录时有10%概率检查清理需求</li>
            <li>✅ <strong>智能清理:</strong> 优先删除失败记录和旧记录</li>
            <li>✅ <strong>保护重要日志:</strong> 保留成功登录、密码重置等重要记录</li>
            <li>✅ <strong>性能优化:</strong> 批量删除，避免影响用户体验</li>
        </ul>
        
        <h3>手动清理命令:</h3>
        <pre>
# 查看统计信息
php server/login-logs-cleaner.php --stats

# 执行清理
php server/login-logs-cleaner.php

# 自动模式（适合cron任务）
php server/login-logs-cleaner.php --auto

# 创建定时任务
php server/login-logs-cleaner.php --create-task
        </pre>
    </div>
    
    <div class="test-section">
        <h2>📈 性能影响分析</h2>
        
        <table>
            <tr>
                <th>记录数范围</th>
                <th>查询性能</th>
                <th>建议操作</th>
            </tr>
            <tr>
                <td>&lt; 500条</td>
                <td class="success">优秀</td>
                <td>无需清理</td>
            </tr>
            <tr>
                <td>500-1000条</td>
                <td class="info">良好</td>
                <td>可选择性清理</td>
            </tr>
            <tr>
                <td>1000-5000条</td>
                <td class="warning">一般</td>
                <td>建议定期清理</td>
            </tr>
            <tr>
                <td>&gt; 5000条</td>
                <td class="error">较差</td>
                <td>立即清理</td>
            </tr>
        </table>
        
        <p><strong>当前状态:</strong> 
            <?php
            $total = $stats['total_records'] ?? 0;
            if ($total < 500) {
                echo '<span class="success">优秀 (' . number_format($total) . ' 条)</span>';
            } elseif ($total < 1000) {
                echo '<span class="info">良好 (' . number_format($total) . ' 条)</span>';
            } elseif ($total < 5000) {
                echo '<span class="warning">一般 (' . number_format($total) . ' 条)</span>';
            } else {
                echo '<span class="error">较差 (' . number_format($total) . ' 条)</span>';
            }
            ?>
        </p>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关工具</h2>
        <ul>
            <li><a href="security-check.php">安全检查工具</a></li>
            <li><a href="final-verification.php">最终验证</a></li>
            <li><a href="../new-lock.html">返回登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
