<?php
/**
 * 激活码绑定API
 * 将激活码绑定到已登录用户
 */

session_start();
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit();
}

// 数据库配置
$dbPath = '../server/user_system.db3';

/**
 * 验证会话令牌
 */
function verifySession($sessionToken) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("
            SELECT us.user_id, u.username, u.status
            FROM user_sessions us
            JOIN users u ON us.user_id = u.id
            WHERE us.session_token = ? 
            AND us.is_active = 1 
            AND us.expires_at > datetime('now')
            AND u.status = 'active'
        ");
        $stmt->execute([$sessionToken]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            // 更新最后访问时间
            $stmt = $pdo->prepare("
                UPDATE user_sessions 
                SET last_access = datetime('now') 
                WHERE session_token = ?
            ");
            $stmt->execute([$sessionToken]);
        }
        
        return $result;
    } catch (Exception $e) {
        error_log("会话验证失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 检查激活码状态
 */
function checkActivationCode($code) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("
            SELECT id, code, is_used, bound_user_id, expires_at, status
            FROM activation_codes 
            WHERE code = ?
        ");
        $stmt->execute([$code]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("检查激活码失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 绑定激活码到用户
 */
function bindActivationCode($codeId, $userId) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 开始事务
        $pdo->beginTransaction();
        
        // 1. 检查用户是否已有激活码
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM activation_codes 
            WHERE bound_user_id = ? AND status = 'used'
        ");
        $stmt->execute([$userId]);
        
        if ($stmt->fetchColumn() > 0) {
            throw new Exception('您已经绑定过激活码了');
        }
        
        // 2. 绑定激活码
        $stmt = $pdo->prepare("
            UPDATE activation_codes 
            SET is_used = 1, bound_user_id = ?, used_at = datetime('now'), status = 'used'
            WHERE id = ?
        ");
        $stmt->execute([$userId, $codeId]);
        
        // 提交事务
        $pdo->commit();
        
        return true;
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("绑定激活码失败: " . $e->getMessage());
        throw $e;
    }
}

/**
 * 记录绑定日志
 */
function logBinding($userId, $code, $success, $reason = null) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("
            INSERT INTO login_logs (user_id, username, ip_address, user_agent, status, failure_reason)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $userId,
            "activation_bind:$code",
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            $success ? 'activation_bound' : 'activation_bind_failed',
            $reason
        ]);
    } catch (Exception $e) {
        error_log("记录绑定日志失败: " . $e->getMessage());
    }
}

try {
    // 获取请求数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('无效的JSON数据');
    }
    
    // 验证必需字段
    if (!isset($data['activation_code']) || empty(trim($data['activation_code']))) {
        throw new Exception('缺少激活码');
    }
    
    if (!isset($data['session_token']) || empty(trim($data['session_token']))) {
        throw new Exception('缺少会话令牌');
    }
    
    $activationCode = trim($data['activation_code']);
    $sessionToken = trim($data['session_token']);
    
    // 验证激活码格式
    if (!preg_match('/^[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}$/', $activationCode)) {
        throw new Exception('激活码格式不正确');
    }
    
    // 验证会话
    $session = verifySession($sessionToken);
    if (!$session) {
        throw new Exception('会话无效或已过期，请重新登录');
    }
    
    // 检查激活码
    $codeInfo = checkActivationCode($activationCode);
    if (!$codeInfo) {
        throw new Exception('激活码不存在');
    }
    
    // 检查激活码状态
    if ($codeInfo['status'] !== 'active') {
        throw new Exception('激活码状态无效');
    }

    // 使用严格类型检查避免SQLite字符串类型问题
    if ((int)$codeInfo['is_used'] === 1) {
        // 检查数据一致性：如果is_used=1但bound_user_id为空，说明数据不一致
        if ($codeInfo['bound_user_id'] === null || $codeInfo['bound_user_id'] === '') {
            // 数据不一致，记录错误并提供修复建议
            error_log("激活码数据不一致: code={$activationCode}, is_used=1, bound_user_id=NULL");
            throw new Exception('激活码状态异常，请联系管理员或稍后重试');
        }

        // 使用严格比较避免类型转换问题
        if ((int)$codeInfo['bound_user_id'] === (int)$session['user_id']) {
            throw new Exception('您已经绑定过这个激活码了');
        } else {
            throw new Exception('激活码已被其他用户使用');
        }
    }
    
    // 检查是否过期
    if (strtotime($codeInfo['expires_at']) <= time()) {
        throw new Exception('激活码已过期');
    }
    
    // 绑定激活码
    bindActivationCode($codeInfo['id'], $session['user_id']);
    
    // 记录成功日志
    logBinding($session['user_id'], $activationCode, true);
    
    echo json_encode([
        'success' => true,
        'message' => '激活码绑定成功！现在可以访问系统了',
        'data' => [
            'user_id' => $session['user_id'],
            'username' => $session['username'],
            'activation_code' => $activationCode,
            'expires_at' => strtotime($codeInfo['expires_at']) * 1000
        ]
    ]);
    
} catch (Exception $e) {
    // 记录失败日志
    if (isset($session) && isset($activationCode)) {
        logBinding($session['user_id'], $activationCode, false, $e->getMessage());
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
