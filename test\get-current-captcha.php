<?php
session_start();

// 获取当前会话的验证码值
$dbPath = '../server/user_system.db3';

try {
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $sessionId = session_id();
    
    echo "<h2>当前会话验证码</h2>\n";
    echo "<p>会话ID: " . htmlspecialchars($sessionId) . "</p>\n";
    
    $stmt = $pdo->prepare("
        SELECT code_value, expires_at, created_at 
        FROM captcha_codes 
        WHERE session_id = ? 
        AND expires_at > datetime('now') 
        AND is_used = 0
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$sessionId]);
    
    $captcha = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($captcha) {
        echo "<p><strong>验证码值: " . htmlspecialchars($captcha['code_value']) . "</strong></p>\n";
        echo "<p>创建时间: " . htmlspecialchars($captcha['created_at']) . "</p>\n";
        echo "<p>过期时间: " . htmlspecialchars($captcha['expires_at']) . "</p>\n";
    } else {
        echo "<p>没有找到有效的验证码</p>\n";
        
        // 显示最近的验证码记录
        $stmt = $pdo->prepare("
            SELECT code_value, expires_at, created_at, is_used
            FROM captcha_codes 
            WHERE session_id = ? 
            ORDER BY created_at DESC 
            LIMIT 3
        ");
        $stmt->execute([$sessionId]);
        
        $recent = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($recent) {
            echo "<h3>最近的验证码记录:</h3>\n";
            echo "<table border='1'>\n";
            echo "<tr><th>验证码</th><th>是否使用</th><th>创建时间</th><th>过期时间</th></tr>\n";
            
            foreach ($recent as $record) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($record['code_value']) . "</td>";
                echo "<td>" . ($record['is_used'] ? '是' : '否') . "</td>";
                echo "<td>" . htmlspecialchars($record['created_at']) . "</td>";
                echo "<td>" . htmlspecialchars($record['expires_at']) . "</td>";
                echo "</tr>\n";
            }
            
            echo "</table>\n";
        }
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage();
}
?>

<p><a href="javascript:history.back()">返回</a></p>
