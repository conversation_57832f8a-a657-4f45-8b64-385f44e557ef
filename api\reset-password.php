<?php
/**
 * 密码重置处理API
 * 验证重置令牌并更新密码
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit();
}

// 数据库配置
$dbPath = '../server/user_system.db3';

/**
 * 验证重置令牌
 */
function verifyResetToken($token) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("
            SELECT email FROM password_resets 
            WHERE reset_token = ? 
            AND expires_at > datetime('now') 
            AND is_used = 0
        ");
        $stmt->execute([$token]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result ? $result['email'] : false;
    } catch (Exception $e) {
        error_log("验证重置令牌失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 更新用户密码
 */
function updateUserPassword($email, $newPassword) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 开始事务
        $pdo->beginTransaction();
        
        // 更新密码 (使用安全的密码哈希算法)
        $passwordHash = password_hash($newPassword, PASSWORD_ARGON2ID);
        $stmt = $pdo->prepare("
            UPDATE users
            SET password_hash = ?
            WHERE email = ? AND status = 'active'
        ");
        $stmt->execute([$passwordHash, $email]);
        
        if ($stmt->rowCount() === 0) {
            throw new Exception('用户不存在或已被禁用');
        }
        
        // 提交事务
        $pdo->commit();
        
        return true;
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("更新密码失败: " . $e->getMessage());
        throw $e;
    }
}

/**
 * 标记重置令牌为已使用
 */
function markTokenAsUsed($token) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("
            UPDATE password_resets 
            SET is_used = 1, used_at = datetime('now') 
            WHERE reset_token = ?
        ");
        $stmt->execute([$token]);
        
        return true;
    } catch (Exception $e) {
        error_log("标记令牌失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 清除用户的所有会话（强制重新登录）
 */
function clearUserSessions($email) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 获取用户ID
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            // 清除该用户的所有会话
            $stmt = $pdo->prepare("UPDATE user_sessions SET is_active = 0 WHERE user_id = ?");
            $stmt->execute([$user['id']]);
        }
        
        return true;
    } catch (Exception $e) {
        error_log("清除会话失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 记录密码重置日志
 */
function logPasswordReset($email, $success, $reason = null) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("
            INSERT INTO login_logs (username, ip_address, user_agent, status, failure_reason)
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            "password_reset_complete:$email",
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            $success ? 'password_reset_success' : 'password_reset_failed',
            $reason
        ]);
    } catch (Exception $e) {
        error_log("记录重置日志失败: " . $e->getMessage());
    }
}

try {
    // 获取请求数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('无效的JSON数据');
    }
    
    // 验证必需字段
    $requiredFields = ['token', 'new_password'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            throw new Exception("缺少必需字段: $field");
        }
    }
    
    $token = trim($data['token']);
    $newPassword = trim($data['new_password']);
    
    // 验证密码强度
    if (strlen($newPassword) < 6) {
        throw new Exception('密码长度至少6位');
    }
    
    // 验证重置令牌
    $email = verifyResetToken($token);
    if (!$email) {
        throw new Exception('重置链接无效或已过期');
    }
    
    // 更新用户密码
    updateUserPassword($email, $newPassword);
    
    // 标记令牌为已使用
    markTokenAsUsed($token);
    
    // 清除用户的所有会话（强制重新登录）
    clearUserSessions($email);
    
    // 记录成功日志
    logPasswordReset($email, true);
    
    echo json_encode([
        'success' => true,
        'message' => '密码重置成功！请使用新密码登录'
    ]);
    
} catch (Exception $e) {
    // 记录失败日志
    if (isset($email)) {
        logPasswordReset($email, false, $e->getMessage());
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
