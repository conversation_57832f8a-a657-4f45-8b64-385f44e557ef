<?php
/**
 * Session存储方案分析工具
 * 分析当前user_sessions表的使用情况并提供优化建议
 */

header('Content-Type: text/html; charset=utf-8');

$dbPath = '../server/user_system.db3';

function analyzeCurrentSessions() {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $analysis = [];
        
        // 总会话数
        $stmt = $pdo->query("SELECT COUNT(*) FROM user_sessions");
        $analysis['total_sessions'] = $stmt->fetchColumn();
        
        // 活跃会话数
        $stmt = $pdo->query("SELECT COUNT(*) FROM user_sessions WHERE is_active = 1");
        $analysis['active_sessions'] = $stmt->fetchColumn();
        
        // 过期会话数
        $stmt = $pdo->query("SELECT COUNT(*) FROM user_sessions WHERE expires_at < datetime('now')");
        $analysis['expired_sessions'] = $stmt->fetchColumn();
        
        // 非活跃但未过期的会话数
        $stmt = $pdo->query("
            SELECT COUNT(*) FROM user_sessions 
            WHERE is_active = 0 AND expires_at > datetime('now')
        ");
        $analysis['inactive_sessions'] = $stmt->fetchColumn();
        
        // 最旧的会话
        $stmt = $pdo->query("SELECT MIN(created_at) FROM user_sessions");
        $analysis['oldest_session'] = $stmt->fetchColumn();
        
        // 最新的会话
        $stmt = $pdo->query("SELECT MAX(created_at) FROM user_sessions");
        $analysis['newest_session'] = $stmt->fetchColumn();
        
        // 按用户统计会话数
        $stmt = $pdo->query("
            SELECT user_id, COUNT(*) as session_count 
            FROM user_sessions 
            GROUP BY user_id 
            ORDER BY session_count DESC 
            LIMIT 10
        ");
        $analysis['sessions_by_user'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 数据库大小
        if (file_exists($dbPath)) {
            $analysis['db_size'] = filesize($dbPath);
            $analysis['db_size_mb'] = round($analysis['db_size'] / 1024 / 1024, 2);
        }
        
        return $analysis;
        
    } catch (Exception $e) {
        return ['error' => $e->getMessage()];
    }
}

$analysis = analyzeCurrentSessions();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session存储方案分析</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
        .warning-box { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; }
        .pros { color: #28a745; }
        .cons { color: #dc3545; }
        code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>📊 Session存储方案分析</h1>
    <p><strong>分析时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="highlight">
        <h3>🎯 核心问题分析</h3>
        <p><strong>您的问题:</strong></p>
        <ul>
            <li>❓ user_sessions表是否有必要放在数据库里？</li>
            <li>❓ 过期的session是否有必要存在？</li>
            <li>❓ 是否有更好的session记录方法？</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>📈 当前Session使用情况</h2>
        
        <?php if (isset($analysis['error'])): ?>
            <p class="error">❌ 分析失败: <?php echo htmlspecialchars($analysis['error']); ?></p>
        <?php else: ?>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3><?php echo number_format($analysis['total_sessions']); ?></h3>
                    <p>总会话数</p>
                </div>
                <div class="stat-card">
                    <h3><?php echo number_format($analysis['active_sessions']); ?></h3>
                    <p>活跃会话</p>
                </div>
                <div class="stat-card">
                    <h3><?php echo number_format($analysis['expired_sessions']); ?></h3>
                    <p>过期会话</p>
                </div>
                <div class="stat-card">
                    <h3><?php echo $analysis['db_size_mb'] ?? 0; ?> MB</h3>
                    <p>数据库大小</p>
                </div>
            </div>
            
            <?php
            $wasteRatio = $analysis['total_sessions'] > 0 ? 
                round((($analysis['expired_sessions'] + $analysis['inactive_sessions']) / $analysis['total_sessions']) * 100, 1) : 0;
            ?>
            
            <div class="<?php echo $wasteRatio > 50 ? 'warning-box' : 'highlight'; ?>">
                <h3><?php echo $wasteRatio > 50 ? '⚠️ 发现存储浪费' : '✅ 存储效率良好'; ?></h3>
                <p><strong>无效会话占比:</strong> <?php echo $wasteRatio; ?>%</p>
                <p><strong>分析:</strong> 
                    <?php if ($wasteRatio > 70): ?>
                        严重浪费，大量无效会话占用存储空间
                    <?php elseif ($wasteRatio > 50): ?>
                        存在浪费，建议清理无效会话
                    <?php elseif ($wasteRatio > 30): ?>
                        轻微浪费，可考虑定期清理
                    <?php else: ?>
                        存储效率良好，无效会话较少
                    <?php endif; ?>
                </p>
            </div>
            
            <h3>详细统计:</h3>
            <table>
                <tr>
                    <th>指标</th>
                    <th>数值</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>总会话数</td>
                    <td><?php echo number_format($analysis['total_sessions']); ?></td>
                    <td>数据库中所有会话记录</td>
                </tr>
                <tr>
                    <td>活跃会话</td>
                    <td class="success"><?php echo number_format($analysis['active_sessions']); ?></td>
                    <td>当前有效的登录会话</td>
                </tr>
                <tr>
                    <td>过期会话</td>
                    <td class="<?php echo $analysis['expired_sessions'] > 0 ? 'warning' : 'success'; ?>">
                        <?php echo number_format($analysis['expired_sessions']); ?>
                    </td>
                    <td>已过期但未清理的会话 <?php echo $analysis['expired_sessions'] > 0 ? '(建议清理)' : ''; ?></td>
                </tr>
                <tr>
                    <td>非活跃会话</td>
                    <td class="<?php echo $analysis['inactive_sessions'] > 10 ? 'warning' : 'info'; ?>">
                        <?php echo number_format($analysis['inactive_sessions']); ?>
                    </td>
                    <td>被挤掉但未过期的会话</td>
                </tr>
                <tr>
                    <td>最旧会话</td>
                    <td><?php echo $analysis['oldest_session'] ?? '无'; ?></td>
                    <td>最早的会话记录时间</td>
                </tr>
                <tr>
                    <td>最新会话</td>
                    <td><?php echo $analysis['newest_session'] ?? '无'; ?></td>
                    <td>最新的会话记录时间</td>
                </tr>
            </table>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>🔍 存储方案对比分析</h2>
        
        <table>
            <tr>
                <th>存储方案</th>
                <th>优点</th>
                <th>缺点</th>
                <th>适用场景</th>
                <th>推荐度</th>
            </tr>
            <tr>
                <td><strong>数据库存储</strong><br>(当前方案)</td>
                <td class="pros">
                    ✅ 持久化存储<br>
                    ✅ 支持复杂查询<br>
                    ✅ 事务保证<br>
                    ✅ 便于审计<br>
                    ✅ 多设备管理
                </td>
                <td class="cons">
                    ❌ 数据库压力大<br>
                    ❌ 需要清理过期数据<br>
                    ❌ I/O开销较大<br>
                    ❌ 查询延迟
                </td>
                <td>
                    • 需要会话审计<br>
                    • 多设备登录管理<br>
                    • 复杂会话策略
                </td>
                <td class="warning">⭐⭐⭐</td>
            </tr>
            <tr>
                <td><strong>Redis存储</strong></td>
                <td class="pros">
                    ✅ 极高性能<br>
                    ✅ 自动过期<br>
                    ✅ 内存存储<br>
                    ✅ 支持集群<br>
                    ✅ 丰富数据类型
                </td>
                <td class="cons">
                    ❌ 需要额外服务<br>
                    ❌ 内存成本<br>
                    ❌ 数据可能丢失<br>
                    ❌ 运维复杂度
                </td>
                <td>
                    • 高并发应用<br>
                    • 分布式系统<br>
                    • 性能要求极高
                </td>
                <td class="success">⭐⭐⭐⭐⭐</td>
            </tr>
            <tr>
                <td><strong>文件存储</strong></td>
                <td class="pros">
                    ✅ 简单易实现<br>
                    ✅ 无需额外服务<br>
                    ✅ 减少数据库压力<br>
                    ✅ 成本低
                </td>
                <td class="cons">
                    ❌ 并发性能差<br>
                    ❌ 文件锁问题<br>
                    ❌ 不支持分布式<br>
                    ❌ 清理复杂
                </td>
                <td>
                    • 小型应用<br>
                    • 单服务器<br>
                    • 简单需求
                </td>
                <td class="info">⭐⭐</td>
            </tr>
            <tr>
                <td><strong>JWT无状态</strong></td>
                <td class="pros">
                    ✅ 无需存储<br>
                    ✅ 完全无状态<br>
                    ✅ 支持分布式<br>
                    ✅ 标准化
                </td>
                <td class="cons">
                    ❌ 无法主动失效<br>
                    ❌ Token较大<br>
                    ❌ 安全风险<br>
                    ❌ 难以撤销
                </td>
                <td>
                    • API服务<br>
                    • 微服务<br>
                    • 无状态需求
                </td>
                <td class="warning">⭐⭐</td>
            </tr>
        </table>
    </div>
    
    <div class="test-section">
        <h2>🎯 针对您项目的具体建议</h2>
        
        <div class="highlight">
            <h3>🥇 推荐方案：优化的数据库存储</h3>
            <p><strong>保留数据库存储的理由：</strong></p>
            <ul>
                <li>✅ <strong>安全审计需求</strong> - 可以追踪用户登录历史</li>
                <li>✅ <strong>多设备管理</strong> - 支持踢掉其他设备登录</li>
                <li>✅ <strong>会话分析</strong> - 可以分析用户行为模式</li>
                <li>✅ <strong>简单部署</strong> - 无需额外服务</li>
            </ul>
            
            <p><strong>但需要以下优化：</strong></p>
            <ol>
                <li><strong>自动清理过期会话</strong> - 定期删除过期记录</li>
                <li><strong>限制会话数量</strong> - 每用户最多保留3-5个会话</li>
                <li><strong>优化查询性能</strong> - 添加合适索引</li>
                <li><strong>会话生命周期管理</strong> - 智能延期和失效</li>
            </ol>
        </div>
        
        <div class="warning-box">
            <h3>🥈 备选方案：混合存储</h3>
            <p><strong>适合未来扩展：</strong></p>
            <ul>
                <li><strong>Redis存储活跃会话</strong> - 快速验证和访问</li>
                <li><strong>数据库存储会话历史</strong> - 审计和分析</li>
                <li><strong>定期同步</strong> - 活跃会话定期写入数据库</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🛠️ 立即可执行的优化</h2>
        
        <h3>1. 清理过期会话</h3>
        <p>立即清理 <?php echo $analysis['expired_sessions'] ?? 0; ?> 个过期会话：</p>
        <pre><code>DELETE FROM user_sessions WHERE expires_at < datetime('now')</code></pre>
        
        <h3>2. 清理非活跃会话</h3>
        <p>清理被挤掉的旧会话：</p>
        <pre><code>DELETE FROM user_sessions WHERE is_active = 0 AND created_at < datetime('now', '-7 days')</code></pre>
        
        <h3>3. 限制每用户会话数</h3>
        <p>保留每用户最新的3个会话：</p>
        <pre><code>DELETE FROM user_sessions WHERE id NOT IN (
  SELECT id FROM (
    SELECT id FROM user_sessions 
    WHERE user_id = ? 
    ORDER BY created_at DESC 
    LIMIT 3
  )
)</code></pre>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关工具</h2>
        <ul>
            <li><a href="session-cleaner.php">Session清理工具</a></li>
            <li><a href="login-logs-manager.php">登录日志管理</a></li>
            <li><a href="security-check.php">安全检查工具</a></li>
            <li><a href="../new-lock.html">返回登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
