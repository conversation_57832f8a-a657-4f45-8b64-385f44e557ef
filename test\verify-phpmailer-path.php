<?php
/**
 * 验证PHPMailer路径
 */

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHPMailer路径验证</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .path-box { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <h1>📍 PHPMailer路径验证</h1>
    <p><strong>验证时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <h2>🔍 当前路径信息</h2>
    <div class="path-box">
        <strong>当前脚本路径:</strong> <?php echo __FILE__; ?><br>
        <strong>当前脚本目录:</strong> <?php echo __DIR__; ?><br>
        <strong>当前工作目录:</strong> <?php echo getcwd(); ?>
    </div>
    
    <h2>📂 PHPMailer依赖检查</h2>
    <?php
    $vendorPath = '../vendor/autoload.php';
    $absolutePath = realpath($vendorPath);
    
    echo "<div class='path-box'>";
    echo "<strong>相对路径:</strong> $vendorPath<br>";
    echo "<strong>绝对路径:</strong> " . ($absolutePath ? $absolutePath : '无法解析') . "<br>";
    echo "<strong>文件存在:</strong> " . (file_exists($vendorPath) ? '是' : '否');
    echo "</div>";
    
    if (file_exists($vendorPath)) {
        echo "<p class='success'>✅ PHPMailer依赖文件存在</p>";
        
        try {
            require_once $vendorPath;
            echo "<p class='success'>✅ PHPMailer自动加载成功</p>";
            
            if (class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
                echo "<p class='success'>✅ PHPMailer类可用</p>";
                
                // 尝试创建PHPMailer实例
                $mail = new PHPMailer\PHPMailer\PHPMailer(true);
                echo "<p class='success'>✅ PHPMailer实例创建成功</p>";
                
                echo "<h3>📋 PHPMailer版本信息</h3>";
                echo "<div class='path-box'>";
                echo "<strong>PHPMailer版本:</strong> " . $mail::VERSION . "<br>";
                echo "</div>";
                
            } else {
                echo "<p class='error'>❌ PHPMailer类不可用</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ PHPMailer加载失败: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p class='error'>❌ PHPMailer依赖文件不存在</p>";
        
        echo "<h3>🔍 查找PHPMailer依赖</h3>";
        $searchPaths = [
            '../vendor/autoload.php',
            '../../vendor/autoload.php',
            '../../../vendor/autoload.php',
        ];
        
        foreach ($searchPaths as $path) {
            $exists = file_exists($path);
            $realPath = realpath($path);
            
            echo "<div class='path-box'>";
            echo "<strong>测试路径:</strong> $path<br>";
            echo "<strong>绝对路径:</strong> " . ($realPath ? $realPath : '不存在') . "<br>";
            echo "<strong>存在:</strong> " . ($exists ? '<span class="success">是</span>' : '<span class="error">否</span>');
            echo "</div>";
            
            if ($exists) {
                echo "<p class='success'>✅ 找到PHPMailer依赖: $path</p>";
                break;
            }
        }
    }
    ?>
    
    <h2>🧪 工作的邮件发送器测试</h2>
    <?php
    try {
        require_once '../server/working-email-sender.php';
        echo "<p class='success'>✅ 工作的邮件发送器加载成功</p>";
        
        $emailSender = new WorkingEmailSender();
        echo "<p class='success'>✅ WorkingEmailSender实例创建成功</p>";
        
        echo "<p class='info'>📧 现在可以测试邮件发送功能了</p>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 工作的邮件发送器加载失败: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <h2>🔗 相关链接</h2>
    <ul>
        <li><a href="test-working-email-sender.php">测试工作的邮件发送器</a></li>
        <li><a href="check-paths.php">详细路径检查</a></li>
        <li><a href="../new-lock.html">返回登录页面</a></li>
    </ul>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
