<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备检测和自动跳转测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .device-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .info-value {
            font-weight: bold;
        }
        .android-tv { color: #28a745; }
        .standard { color: #007bff; }
        .mobile { color: #ffc107; }
        
        .test-button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        
        .simulation-controls {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .log-display {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            height: 200px;
            overflow-y: auto;
            margin: 15px 0;
        }
        
        .redirect-test {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>🔍 设备检测和自动跳转测试</h1>
    
    <!-- 当前设备信息 -->
    <div class="test-container">
        <h2 class="test-title">📱 当前设备信息</h2>
        <div class="device-info" id="deviceInfo">
            <div class="info-item">
                <span>正在检测设备信息...</span>
            </div>
        </div>
        <button class="test-button" onclick="refreshDeviceInfo()">🔄 刷新检测</button>
        <button class="test-button" onclick="exportDeviceInfo()">💾 导出信息</button>
    </div>
    
    <!-- 跳转逻辑测试 -->
    <div class="test-container">
        <h2 class="test-title">🔄 跳转逻辑测试</h2>
        <div class="redirect-test">
            <h4>当前页面建议</h4>
            <div id="redirectRecommendation">正在分析...</div>
        </div>
        <button class="test-button" onclick="testRedirectLogic()">🧪 测试跳转逻辑</button>
        <button class="test-button" onclick="simulateLogin()">🔑 模拟登录跳转</button>
        <button class="test-button" onclick="clearRedirectFlags()">🗑️ 清除跳转标记</button>
    </div>
    
    <!-- Android TV模拟 -->
    <div class="test-container">
        <h2 class="test-title">📺 Android TV模拟测试</h2>
        <div class="simulation-controls">
            <h4>User Agent模拟</h4>
            <p>选择要模拟的设备类型：</p>
            <button class="test-button" onclick="simulateAndroidTV()">📺 Android TV</button>
            <button class="test-button" onclick="simulateDesktop()">💻 桌面设备</button>
            <button class="test-button" onclick="simulateMobile()">📱 移动设备</button>
            <button class="test-button" onclick="resetSimulation()">🔄 重置模拟</button>
        </div>
        <div id="simulationResult"></div>
    </div>
    
    <!-- 实时日志 -->
    <div class="test-container">
        <h2 class="test-title">📋 实时检测日志</h2>
        <div class="log-display" id="logDisplay">
            等待设备检测启动...
        </div>
        <button class="test-button" onclick="clearLog()">🗑️ 清空日志</button>
        <button class="test-button" onclick="exportLog()">💾 导出日志</button>
    </div>
    
    <!-- 手动跳转测试 -->
    <div class="test-container">
        <h2 class="test-title">🎮 手动跳转测试</h2>
        <p>测试不同版本的页面跳转：</p>
        <button class="test-button" onclick="goToStandard()">💻 跳转到标准版</button>
        <button class="test-button" onclick="goToTVOptimized()">📺 跳转到TV优化版</button>
        <button class="test-button" onclick="goToLogin()">🔑 跳转到登录页</button>
    </div>

    <script src="../js/smart-device-detector.js"></script>
    <script>
        let logMessages = [];
        
        // 重写console.log来捕获日志
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.join(' ');
            logMessages.push(`[${new Date().toLocaleTimeString()}] ${message}`);
            updateLogDisplay();
        };
        
        function updateLogDisplay() {
            const logDisplay = document.getElementById('logDisplay');
            logDisplay.innerHTML = logMessages.slice(-20).join('\n'); // 显示最近20条
            logDisplay.scrollTop = logDisplay.scrollHeight;
        }
        
        function refreshDeviceInfo() {
            if (window.smartDeviceDetector) {
                const deviceInfo = window.smartDeviceDetector.deviceInfo;
                displayDeviceInfo(deviceInfo);
                updateRedirectRecommendation(deviceInfo);
            } else {
                console.log('设备检测器未初始化，正在重新初始化...');
                setTimeout(refreshDeviceInfo, 1000);
            }
        }
        
        function displayDeviceInfo(deviceInfo) {
            const container = document.getElementById('deviceInfo');
            
            const deviceTypeClass = deviceInfo.isAndroidTV ? 'android-tv' : 
                                   deviceInfo.isMobile ? 'mobile' : 'standard';
            
            container.innerHTML = `
                <div class="info-item">
                    <span>设备类型:</span>
                    <span class="info-value ${deviceTypeClass}">
                        ${deviceInfo.isAndroidTV ? '📺 Android TV' : 
                          deviceInfo.isMobile ? '📱 移动设备' : 
                          deviceInfo.isTablet ? '📱 平板设备' : '💻 桌面设备'}
                    </span>
                </div>
                <div class="info-item">
                    <span>性能等级:</span>
                    <span class="info-value">${deviceInfo.performanceLevel}</span>
                </div>
                <div class="info-item">
                    <span>屏幕分辨率:</span>
                    <span class="info-value">${deviceInfo.screen.width}x${deviceInfo.screen.height}</span>
                </div>
                <div class="info-item">
                    <span>像素比:</span>
                    <span class="info-value">${deviceInfo.screen.pixelRatio}</span>
                </div>
                <div class="info-item">
                    <span>内存:</span>
                    <span class="info-value">${deviceInfo.memory}GB</span>
                </div>
                <div class="info-item">
                    <span>CPU核心:</span>
                    <span class="info-value">${deviceInfo.cores}</span>
                </div>
                <div class="info-item">
                    <span>User Agent:</span>
                    <span class="info-value" style="font-size: 12px; word-break: break-all;">
                        ${deviceInfo.userAgent.substring(0, 100)}...
                    </span>
                </div>
            `;
        }
        
        function updateRedirectRecommendation(deviceInfo) {
            const container = document.getElementById('redirectRecommendation');
            const currentPage = getCurrentPage();
            
            let recommendation = '';
            if (deviceInfo.isAndroidTV) {
                if (currentPage === 'tv-optimized') {
                    recommendation = '✅ 当前页面适合Android TV设备';
                } else {
                    recommendation = '⚠️ 建议跳转到TV优化版本 (index-tv-optimized.php)';
                }
            } else {
                if (currentPage === 'standard') {
                    recommendation = '✅ 当前页面适合标准设备';
                } else {
                    recommendation = '⚠️ 建议跳转到标准版本 (index.php)';
                }
            }
            
            container.innerHTML = `
                <p><strong>当前页面:</strong> ${currentPage}</p>
                <p><strong>建议:</strong> ${recommendation}</p>
            `;
        }
        
        function getCurrentPage() {
            const path = window.location.pathname;
            const filename = path.split('/').pop();
            
            if (filename.includes('index-tv-optimized')) return 'tv-optimized';
            else if (filename.includes('index')) return 'standard';
            else return 'test-page';
        }
        
        function testRedirectLogic() {
            console.log('🧪 开始测试跳转逻辑...');
            
            if (window.smartDeviceDetector) {
                const deviceInfo = window.smartDeviceDetector.deviceInfo;
                const shouldUseTV = deviceInfo.isAndroidTV;
                const currentPage = getCurrentPage();
                
                console.log(`设备类型: ${deviceInfo.isAndroidTV ? 'Android TV' : '标准设备'}`);
                console.log(`当前页面: ${currentPage}`);
                console.log(`建议使用: ${shouldUseTV ? 'TV优化版' : '标准版'}`);
                
                if (shouldUseTV && currentPage !== 'tv-optimized') {
                    console.log('✅ 应该跳转到TV优化版');
                } else if (!shouldUseTV && currentPage === 'tv-optimized') {
                    console.log('✅ 应该跳转到标准版');
                } else {
                    console.log('✅ 当前页面适合当前设备');
                }
            }
        }
        
        function simulateLogin() {
            console.log('🔑 模拟登录成功，测试设备检测跳转...');
            
            // 模拟设置会话令牌
            sessionStorage.setItem('session_token', 'test_token_' + Date.now());
            
            // 清除跳转标记
            sessionStorage.removeItem('login_redirect_done');
            
            // 触发设备检测跳转
            if (window.smartDeviceDetector) {
                const deviceInfo = window.smartDeviceDetector.deviceInfo;
                if (deviceInfo.isAndroidTV) {
                    console.log('📺 检测到Android TV，应该跳转到优化版本');
                } else {
                    console.log('💻 检测到标准设备，应该跳转到标准版本');
                }
            }
        }
        
        function clearRedirectFlags() {
            sessionStorage.removeItem('device_redirect_done');
            sessionStorage.removeItem('login_redirect_done');
            sessionStorage.removeItem('redirect_reason');
            console.log('🗑️ 已清除所有跳转标记');
        }
        
        function simulateAndroidTV() {
            console.log('📺 模拟Android TV环境...');
            // 注意：这只是模拟，实际检测基于真实的User Agent
            alert('Android TV模拟：在真实的Android TV设备上，系统会自动检测并跳转到优化版本');
        }
        
        function simulateDesktop() {
            console.log('💻 模拟桌面环境...');
            alert('桌面设备模拟：系统会保持在标准版本');
        }
        
        function simulateMobile() {
            console.log('📱 模拟移动设备...');
            alert('移动设备模拟：系统会保持在标准版本');
        }
        
        function resetSimulation() {
            location.reload();
        }
        
        function goToStandard() {
            window.open('../index.php', '_blank');
        }
        
        function goToTVOptimized() {
            window.open('../index-tv-optimized.php', '_blank');
        }
        
        function goToLogin() {
            window.open('../new-lock.html', '_blank');
        }
        
        function clearLog() {
            logMessages = [];
            updateLogDisplay();
        }
        
        function exportDeviceInfo() {
            if (window.smartDeviceDetector) {
                const deviceInfo = window.smartDeviceDetector.getDeviceSummary();
                const data = {
                    timestamp: new Date().toISOString(),
                    deviceInfo: deviceInfo,
                    currentPage: getCurrentPage(),
                    userAgent: navigator.userAgent
                };
                
                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `device-info-${new Date().toISOString().slice(0,19)}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
        }
        
        function exportLog() {
            const logContent = logMessages.join('\n');
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `device-detection-log-${new Date().toISOString().slice(0,19)}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            console.log('📋 设备检测测试页面加载完成');
            
            // 等待设备检测器初始化
            setTimeout(() => {
                refreshDeviceInfo();
            }, 1000);
        });
    </script>
</body>
</html>
