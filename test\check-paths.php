<?php
/**
 * 检查路径信息
 */

echo "<h1>路径信息检查</h1>";

echo "<h2>当前路径信息</h2>";
echo "<p><strong>当前工作目录:</strong> " . getcwd() . "</p>";
echo "<p><strong>当前脚本路径:</strong> " . __FILE__ . "</p>";
echo "<p><strong>当前脚本目录:</strong> " . __DIR__ . "</p>";

echo "<h2>相对路径测试</h2>";

// 测试不同的相对路径
$testPaths = [
    '../../test_email_system/vendor/autoload.php',
    '../test_email_system/vendor/autoload.php',
    '../../test_email_system/',
    '../test_email_system/',
];

foreach ($testPaths as $path) {
    $absolutePath = realpath($path);
    $exists = file_exists($path);
    
    echo "<p><strong>测试路径:</strong> $path</p>";
    echo "<p><strong>绝对路径:</strong> " . ($absolutePath ? $absolutePath : '无法解析') . "</p>";
    echo "<p><strong>文件存在:</strong> " . ($exists ? '是' : '否') . "</p>";
    echo "<hr>";
}

echo "<h2>查找test_email_system目录</h2>";

// 向上查找test_email_system目录
$currentDir = __DIR__;
for ($i = 0; $i < 5; $i++) {
    $testDir = $currentDir . str_repeat('/..', $i) . '/vendor';
    $realPath = realpath($testDir);
    
    echo "<p><strong>尝试路径:</strong> $testDir</p>";
    echo "<p><strong>绝对路径:</strong> " . ($realPath ? $realPath : '不存在') . "</p>";
    
    if ($realPath && is_dir($realPath)) {
        $vendorPath = $realPath . '/vendor/autoload.php';
        echo "<p><strong>vendor路径:</strong> $vendorPath</p>";
        echo "<p><strong>vendor存在:</strong> " . (file_exists($vendorPath) ? '是' : '否') . "</p>";
        
        if (file_exists($vendorPath)) {
            echo "<p style='color: green;'><strong>✅ 找到PHPMailer依赖!</strong></p>";
            echo "<p><strong>相对路径:</strong> " . str_repeat('../', $i+1) . "vendor/autoload.php</p>";
            break;
        }
    }
    echo "<hr>";
}
?>
