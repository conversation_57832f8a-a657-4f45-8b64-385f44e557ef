<?php
/**
 * PHP配置测试文件
 * 用于验证Windows IIS环境下的PHP配置是否正确
 */

// 设置内容类型
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP配置测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>🔧 PHP配置测试报告</h1>
    <p><strong>测试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="test-section">
        <h2>📊 基本配置检查</h2>
        <table>
            <tr>
                <th>配置项</th>
                <th>当前值</th>
                <th>期望值</th>
                <th>状态</th>
            </tr>
            <?php
            $configs = [
                'display_errors' => ['current' => ini_get('display_errors'), 'expected' => '0', 'type' => 'bool'],
                'display_startup_errors' => ['current' => ini_get('display_startup_errors'), 'expected' => '0', 'type' => 'bool'],
                'log_errors' => ['current' => ini_get('log_errors'), 'expected' => '1', 'type' => 'bool'],
                'error_log' => ['current' => ini_get('error_log'), 'expected' => 'D:\\error_log\\php\\php_errors.log', 'type' => 'string'],
                'memory_limit' => ['current' => ini_get('memory_limit'), 'expected' => '256M', 'type' => 'string'],
                'max_execution_time' => ['current' => ini_get('max_execution_time'), 'expected' => '60', 'type' => 'string'],
                'upload_max_filesize' => ['current' => ini_get('upload_max_filesize'), 'expected' => '10M', 'type' => 'string'],
                'date.timezone' => ['current' => ini_get('date.timezone'), 'expected' => 'Asia/Shanghai', 'type' => 'string']
            ];
            
            foreach ($configs as $key => $config) {
                $current = $config['current'];
                $expected = $config['expected'];
                
                if ($config['type'] === 'bool') {
                    $current_display = $current ? 'On' : 'Off';
                    $expected_display = $expected === '1' ? 'On' : 'Off';
                    $status = ($current == $expected) ? 'success' : 'error';
                } else {
                    $current_display = $current ?: '(未设置)';
                    $expected_display = $expected;
                    $status = ($current === $expected) ? 'success' : 'error';
                }
                
                echo "<tr>";
                echo "<td><strong>$key</strong></td>";
                echo "<td>$current_display</td>";
                echo "<td>$expected_display</td>";
                echo "<td class='$status'>" . ($status === 'success' ? '✅ 正确' : '❌ 需要修改') . "</td>";
                echo "</tr>";
            }
            ?>
        </table>
    </div>
    
    <div class="test-section">
        <h2>📁 目录权限测试</h2>
        <?php
        $directories = [
            'D:\\error_log\\php\\',
            'D:\\temp\\php\\sessions\\',
            'D:\\temp\\php\\uploads\\'
        ];
        
        echo "<table>";
        echo "<tr><th>目录</th><th>存在性</th><th>可写性</th><th>状态</th></tr>";
        
        foreach ($directories as $dir) {
            $exists = is_dir($dir);
            $writable = $exists ? is_writable($dir) : false;
            
            echo "<tr>";
            echo "<td><strong>$dir</strong></td>";
            echo "<td>" . ($exists ? '✅ 存在' : '❌ 不存在') . "</td>";
            echo "<td>" . ($writable ? '✅ 可写' : '❌ 不可写') . "</td>";
            
            if ($exists && $writable) {
                echo "<td class='success'>✅ 正常</td>";
            } else {
                echo "<td class='error'>❌ 需要修复</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        ?>
    </div>
    
    <div class="test-section">
        <h2>🧪 错误日志测试</h2>
        <?php
        // 测试错误日志功能
        $test_message = "PHP配置测试 - " . date('Y-m-d H:i:s');
        $log_result = error_log($test_message);
        
        echo "<p><strong>测试消息:</strong> $test_message</p>";
        echo "<p><strong>写入结果:</strong> ";
        if ($log_result) {
            echo "<span class='success'>✅ 成功写入错误日志</span>";
        } else {
            echo "<span class='error'>❌ 写入失败</span>";
        }
        echo "</p>";
        
        // 检查日志文件
        $log_file = ini_get('error_log');
        if ($log_file && file_exists($log_file)) {
            echo "<p><strong>日志文件:</strong> <span class='success'>✅ $log_file 存在</span></p>";
            echo "<p><strong>文件大小:</strong> " . filesize($log_file) . " 字节</p>";
            echo "<p><strong>最后修改:</strong> " . date('Y-m-d H:i:s', filemtime($log_file)) . "</p>";
        } else {
            echo "<p><strong>日志文件:</strong> <span class='error'>❌ $log_file 不存在</span></p>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>🔍 JSON响应测试</h2>
        <p>测试PHP错误是否会污染JSON响应...</p>
        <?php
        // 清理输出缓冲
        if (ob_get_level()) {
            ob_clean();
        }
        
        // 开始新的输出缓冲
        ob_start();
        
        // 故意触发一个警告
        $undefined_var = $non_existent_variable;
        
        // 获取可能的错误输出
        $error_output = ob_get_contents();
        ob_end_clean();
        
        // 测试JSON响应
        $json_data = ['status' => 'success', 'message' => 'JSON测试', 'timestamp' => time()];
        $json_string = json_encode($json_data);
        
        echo "<p><strong>错误输出:</strong> ";
        if (empty($error_output)) {
            echo "<span class='success'>✅ 无错误输出 (正确)</span>";
        } else {
            echo "<span class='error'>❌ 检测到错误输出: " . htmlspecialchars($error_output) . "</span>";
        }
        echo "</p>";
        
        echo "<p><strong>JSON测试:</strong> ";
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "<span class='success'>✅ JSON编码正常</span>";
        } else {
            echo "<span class='error'>❌ JSON编码错误: " . json_last_error_msg() . "</span>";
        }
        echo "</p>";
        
        echo "<p><strong>测试JSON:</strong> <code>" . htmlspecialchars($json_string) . "</code></p>";
        ?>
    </div>
    
    <div class="test-section">
        <h2>📋 系统信息</h2>
        <table>
            <tr><th>项目</th><th>值</th></tr>
            <tr><td><strong>PHP版本</strong></td><td><?php echo PHP_VERSION; ?></td></tr>
            <tr><td><strong>操作系统</strong></td><td><?php echo PHP_OS; ?></td></tr>
            <tr><td><strong>服务器软件</strong></td><td><?php echo $_SERVER['SERVER_SOFTWARE'] ?? '未知'; ?></td></tr>
            <tr><td><strong>PHP SAPI</strong></td><td><?php echo php_sapi_name(); ?></td></tr>
            <tr><td><strong>配置文件</strong></td><td><?php echo php_ini_loaded_file(); ?></td></tr>
            <tr><td><strong>扩展目录</strong></td><td><?php echo ini_get('extension_dir'); ?></td></tr>
        </table>
    </div>
    
    <div class="test-section">
        <h2>🎯 修复建议</h2>
        <?php
        $issues = [];
        
        // 检查各种配置问题
        if (ini_get('display_errors')) {
            $issues[] = "display_errors 仍然为 On，需要设置为 Off";
        }
        
        if (ini_get('display_startup_errors')) {
            $issues[] = "display_startup_errors 仍然为 On，需要设置为 Off";
        }
        
        if (!ini_get('log_errors')) {
            $issues[] = "log_errors 未启用，需要设置为 On";
        }
        
        $error_log = ini_get('error_log');
        if (!$error_log || $error_log !== 'D:\\error_log\\php\\php_errors.log') {
            $issues[] = "error_log 路径不正确，应该设置为 D:\\error_log\\php\\php_errors.log";
        }
        
        if (empty($issues)) {
            echo "<p class='success'>✅ 所有配置都正确！</p>";
        } else {
            echo "<p class='error'>❌ 发现以下问题需要修复：</p>";
            echo "<ul>";
            foreach ($issues as $issue) {
                echo "<li class='error'>$issue</li>";
            }
            echo "</ul>";
            
            echo "<p><strong>修复步骤：</strong></p>";
            echo "<ol>";
            echo "<li>确保使用了修改后的 php.ini 文件</li>";
            echo "<li>运行 setup-directories.bat 创建必要目录</li>";
            echo "<li>执行 iisreset 重启 IIS 服务</li>";
            echo "<li>重新访问此页面验证配置</li>";
            echo "</ol>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关链接</h2>
        <ul>
            <li><a href="phpinfo.php" target="_blank">查看完整 PHP 信息</a></li>
            <li><a href="../api/forgot-password.php" target="_blank">测试密码找回 API</a> (POST请求)</li>
            <li><a href="../test/debug-session-captcha.php" target="_blank">验证码调试页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?> | 🖥️ 服务器: <?php echo $_SERVER['SERVER_NAME'] ?? 'localhost'; ?></small></p>
</body>
</html>
