<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>断网检测功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 900px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .warning {
            color: #856404;
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .info {
            color: #0c5460;
            background: #d1ecf1;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button.danger {
            background: #dc3545;
        }
        
        button.danger:hover {
            background: #c82333;
        }
        
        .test-section {
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin: 20px 0;
        }
        
        .status-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
        }
        
        .network-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            z-index: 1000;
        }
        
        .online {
            background: #d4edda;
            color: #155724;
        }
        
        .offline {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>🌐 断网检测功能测试</h1>
    
    <div class="network-status" id="networkStatus">
        🌐 在线
    </div>
    
    <div class="container">
        <h2>断网检测机制说明</h2>
        <div class="info">
            <strong>🛡️ 防护机制:</strong><br>
            1. <strong>实时网络监控</strong>: 监听online/offline事件<br>
            2. <strong>心跳检测</strong>: 每20秒向服务器发送心跳包<br>
            3. <strong>离线时间限制</strong>: 最大离线时间60秒<br>
            4. <strong>连续失败检测</strong>: 连续3次验证失败触发安全机制<br>
            5. <strong>可疑行为识别</strong>: 检测故意断网等异常行为<br>
            6. <strong>多设备检测</strong>: 服务器端检测同一账号的多个活跃会话
        </div>
    </div>
    
    <div class="container">
        <h2>🧪 功能测试</h2>
        
        <div class="test-section">
            <h3>1. 网络状态监控</h3>
            <p>测试浏览器网络状态检测功能：</p>
            <button onclick="simulateOffline()">模拟断网</button>
            <button onclick="simulateOnline()">模拟联网</button>
            <button onclick="checkNetworkStatus()">检查网络状态</button>
            <div id="networkTestResult"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 心跳检测测试</h3>
            <p>测试与服务器的心跳通信：</p>
            <button onclick="testHeartbeat()">测试心跳</button>
            <button onclick="startHeartbeatMonitor()">开始心跳监控</button>
            <button onclick="stopHeartbeatMonitor()">停止心跳监控</button>
            <div id="heartbeatResult"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 离线时间检测</h3>
            <p>测试离线时间过长的处理：</p>
            <button onclick="simulateLongOffline()">模拟长时间离线</button>
            <button onclick="resetOfflineTimer()">重置离线计时器</button>
            <div id="offlineTestResult"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 可疑行为检测</h3>
            <p>测试恶意断网行为检测：</p>
            <button class="danger" onclick="simulateSuspiciousActivity()">模拟可疑行为</button>
            <button onclick="simulateMultipleSessions()">模拟多设备登录</button>
            <div id="suspiciousTestResult"></div>
        </div>
    </div>
    
    <div class="container">
        <h2>📊 实时状态监控</h2>
        <div class="status-display" id="statusDisplay">
            等待测试开始...
        </div>
    </div>
    
    <div class="container">
        <h2>🔗 快速链接</h2>
        <button onclick="window.location.href='../new-lock.html'">登录页面</button>
        <button onclick="window.location.href='../index.php'">主页面</button>
        <button onclick="window.open('../index.php', '_blank')">新窗口打开主页面</button>
    </div>

    <script>
        let heartbeatMonitor = null;
        let offlineStartTime = null;
        let statusUpdateInterval = null;
        
        // 初始化网络状态监控
        function initNetworkMonitoring() {
            updateNetworkStatus();
            
            window.addEventListener('online', () => {
                updateNetworkStatus();
                logStatus('🌐 网络已连接');
            });
            
            window.addEventListener('offline', () => {
                updateNetworkStatus();
                offlineStartTime = Date.now();
                logStatus('📡 网络已断开');
            });
            
            // 开始状态更新
            statusUpdateInterval = setInterval(updateStatus, 1000);
        }
        
        function updateNetworkStatus() {
            const statusElement = document.getElementById('networkStatus');
            if (navigator.onLine) {
                statusElement.textContent = '🌐 在线';
                statusElement.className = 'network-status online';
            } else {
                statusElement.textContent = '📡 离线';
                statusElement.className = 'network-status offline';
            }
        }
        
        function updateStatus() {
            const statusDisplay = document.getElementById('statusDisplay');
            const currentTime = new Date().toLocaleTimeString();
            const networkStatus = navigator.onLine ? '在线' : '离线';
            
            let offlineInfo = '';
            if (!navigator.onLine && offlineStartTime) {
                const offlineDuration = Math.round((Date.now() - offlineStartTime) / 1000);
                offlineInfo = `\n离线时长: ${offlineDuration}秒`;
                
                if (offlineDuration > 60) {
                    offlineInfo += ' (⚠️ 超过限制)';
                }
            }
            
            statusDisplay.textContent = `
时间: ${currentTime}
网络状态: ${networkStatus}${offlineInfo}
页面活跃: ${document.hasFocus() ? '是' : '否'}
页面可见: ${!document.hidden ? '是' : '否'}
            `.trim();
        }
        
        function logStatus(message) {
            console.log(`[${new Date().toLocaleTimeString()}] ${message}`);
        }
        
        function simulateOffline() {
            const resultDiv = document.getElementById('networkTestResult');
            
            // 模拟离线事件
            window.dispatchEvent(new Event('offline'));
            
            resultDiv.innerHTML = `
                <div class="warning">⚠️ 已模拟断网状态</div>
                <div class="info">注意: 这只是模拟事件，实际网络连接未断开</div>
            `;
        }
        
        function simulateOnline() {
            const resultDiv = document.getElementById('networkTestResult');
            
            // 模拟在线事件
            window.dispatchEvent(new Event('online'));
            offlineStartTime = null;
            
            resultDiv.innerHTML = `
                <div class="success">✅ 已模拟联网状态</div>
            `;
        }
        
        function checkNetworkStatus() {
            const resultDiv = document.getElementById('networkTestResult');
            
            resultDiv.innerHTML = `
                <div class="info">
                    📊 网络状态检查结果:<br>
                    • navigator.onLine: ${navigator.onLine}<br>
                    • 连接类型: ${navigator.connection ? navigator.connection.effectiveType : '未知'}<br>
                    • 用户代理: ${navigator.userAgent.substring(0, 50)}...
                </div>
            `;
        }
        
        async function testHeartbeat() {
            const resultDiv = document.getElementById('heartbeatResult');
            resultDiv.innerHTML = '<div class="info">🔄 正在测试心跳...</div>';
            
            try {
                const response = await fetch('../api/network-heartbeat.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        session_token: 'test_token_for_demo',
                        online_status: navigator.onLine,
                        last_offline_duration: offlineStartTime ? Math.round((Date.now() - offlineStartTime) / 1000) : 0,
                        client_timestamp: Date.now()
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ 心跳测试成功</div>
                        <div class="info">服务器响应: ${data.message}</div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ 心跳测试失败: ${data.message}</div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ 心跳测试请求失败: ${error.message}</div>
                    <div class="warning">这是预期的，因为使用了测试令牌</div>
                `;
            }
        }
        
        function startHeartbeatMonitor() {
            const resultDiv = document.getElementById('heartbeatResult');
            
            if (heartbeatMonitor) {
                resultDiv.innerHTML = '<div class="warning">⚠️ 心跳监控已在运行</div>';
                return;
            }
            
            let heartbeatCount = 0;
            heartbeatMonitor = setInterval(async () => {
                heartbeatCount++;
                logStatus(`💓 发送心跳 #${heartbeatCount}`);
                
                try {
                    await testHeartbeat();
                } catch (error) {
                    logStatus(`💓 心跳失败: ${error.message}`);
                }
            }, 5000); // 每5秒一次（测试用）
            
            resultDiv.innerHTML = '<div class="success">✅ 心跳监控已启动（每5秒一次）</div>';
        }
        
        function stopHeartbeatMonitor() {
            const resultDiv = document.getElementById('heartbeatResult');
            
            if (heartbeatMonitor) {
                clearInterval(heartbeatMonitor);
                heartbeatMonitor = null;
                resultDiv.innerHTML = '<div class="info">🛑 心跳监控已停止</div>';
            } else {
                resultDiv.innerHTML = '<div class="warning">⚠️ 心跳监控未运行</div>';
            }
        }
        
        function simulateLongOffline() {
            const resultDiv = document.getElementById('offlineTestResult');
            
            // 模拟长时间离线
            offlineStartTime = Date.now() - 70000; // 70秒前
            window.dispatchEvent(new Event('offline'));
            
            resultDiv.innerHTML = `
                <div class="error">⚠️ 已模拟长时间离线（70秒）</div>
                <div class="info">系统应该检测到离线时间过长并触发安全机制</div>
            `;
        }
        
        function resetOfflineTimer() {
            const resultDiv = document.getElementById('offlineTestResult');
            
            offlineStartTime = null;
            window.dispatchEvent(new Event('online'));
            
            resultDiv.innerHTML = '<div class="success">✅ 离线计时器已重置</div>';
        }
        
        function simulateSuspiciousActivity() {
            const resultDiv = document.getElementById('suspiciousTestResult');
            
            // 创建可疑行为提示
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
            `;
            
            overlay.innerHTML = `
                <div style="
                    background: white;
                    padding: 40px;
                    border-radius: 10px;
                    text-align: center;
                    max-width: 400px;
                ">
                    <h2 style="color: #f44336; margin-bottom: 20px;">🚨 检测到异常行为</h2>
                    <p style="margin-bottom: 20px;">系统检测到可能的恶意网络操作，为了安全起见将退出登录</p>
                    <div style="color: #666; font-size: 14px;">2秒后自动跳转到登录页面...</div>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        margin-top: 15px;
                        background: #007bff;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 5px;
                        cursor: pointer;
                    ">关闭演示</button>
                </div>
            `;
            
            document.body.appendChild(overlay);
            
            resultDiv.innerHTML = '<div class="error">🚨 可疑行为检测已触发</div>';
        }
        
        function simulateMultipleSessions() {
            const resultDiv = document.getElementById('suspiciousTestResult');
            
            // 创建多设备登录提示
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
            `;
            
            overlay.innerHTML = `
                <div style="
                    background: white;
                    padding: 40px;
                    border-radius: 10px;
                    text-align: center;
                    max-width: 400px;
                ">
                    <h2 style="color: #f44336; margin-bottom: 20px;">🔒 检测到多设备登录</h2>
                    <p style="margin-bottom: 20px;">系统检测到您的账号在其他设备上登录，当前会话已失效</p>
                    <div style="color: #666; font-size: 14px;">即将跳转到登录页面...</div>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        margin-top: 15px;
                        background: #007bff;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 5px;
                        cursor: pointer;
                    ">关闭演示</button>
                </div>
            `;
            
            document.body.appendChild(overlay);
            
            resultDiv.innerHTML = '<div class="error">🔒 多设备登录检测已触发</div>';
        }
        
        // 页面加载时初始化
        window.addEventListener('load', () => {
            initNetworkMonitoring();
            logStatus('🚀 断网检测测试页面已加载');
        });
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            if (heartbeatMonitor) {
                clearInterval(heartbeatMonitor);
            }
            if (statusUpdateInterval) {
                clearInterval(statusUpdateInterval);
            }
        });
    </script>
</body>
</html>
