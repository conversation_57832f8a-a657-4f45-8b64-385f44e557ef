<?php
/**
 * 访问保护测试工具
 * 测试.htaccess是否正确保护敏感文件
 */

header('Content-Type: text/html; charset=utf-8');

$testResult = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_protection'])) {
    $testResult = testAccessProtection();
}

function testAccessProtection() {
    $result = [
        'tests' => [],
        'protected_count' => 0,
        'vulnerable_count' => 0,
        'total_tests' => 0
    ];
    
    // 需要测试保护的URL路径
    $protectedUrls = [
        // vendor目录文件
        'vendor/autoload.php' => 'Composer自动加载文件',
        'vendor/composer/autoload_real.php' => 'Composer核心文件',
        'vendor/composer/autoload_static.php' => 'Composer静态映射',
        'vendor/composer/ClassLoader.php' => 'Composer类加载器',
        'vendor/phpmailer/phpmailer/src/PHPMailer.php' => 'PHPMailer源码',
        'vendor/phpmailer/phpmailer/src/SMTP.php' => 'PHPMailer SMTP类',
        'vendor/composer.json' => 'Composer配置文件',
        
        // server目录文件
        'server/email-config.php' => '邮件配置文件',
        'server/working-email-sender.php' => '邮件发送器源码',
        'server/phpmailer-loader-php73.php' => 'PHP73兼容加载器',
        
        // docs目录文件
        'docs/records.md' => '项目记录文档',
        'docs/说明.txt' => '项目说明文档',
        
        // test目录文件
        'test/security-check.php' => '安全检查工具',
        'test/test-php73-compatibility.php' => 'PHP兼容性测试',
        
        // 其他敏感文件
        '.htaccess' => '主配置文件',
        'vendor/.htaccess' => 'vendor保护文件'
    ];
    
    foreach ($protectedUrls as $url => $description) {
        $testCase = [
            'url' => $url,
            'description' => $description,
            'protected' => false,
            'status_code' => null,
            'error' => null
        ];
        
        try {
            // 构建完整URL
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $host = $_SERVER['HTTP_HOST'];
            $basePath = dirname($_SERVER['REQUEST_URI']);
            $fullUrl = $protocol . '://' . $host . $basePath . '/../' . $url;
            
            // 使用cURL测试访问
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $fullUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_NOBODY, true); // 只获取头部
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            $testCase['status_code'] = $httpCode;
            
            if ($error) {
                $testCase['error'] = $error;
            }
            
            // 判断是否受保护
            // 403 Forbidden = 受保护
            // 404 Not Found = 文件不存在（也算保护）
            // 200 OK = 可访问（不安全）
            if ($httpCode == 403 || $httpCode == 404) {
                $testCase['protected'] = true;
                $result['protected_count']++;
            } else {
                $result['vulnerable_count']++;
            }
            
        } catch (Exception $e) {
            $testCase['error'] = $e->getMessage();
        }
        
        $result['tests'][] = $testCase;
        $result['total_tests']++;
    }
    
    return $result;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访问保护测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        button { padding: 8px 16px; margin: 5px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
        .protected { background: #d4edda; }
        .vulnerable { background: #f8d7da; }
        .highlight { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; }
    </style>
</head>
<body>
    <h1>🛡️ 访问保护测试</h1>
    <p><strong>测试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="highlight">
        <h3>⚠️ 重要说明</h3>
        <p><strong>测试原理:</strong> 通过HTTP请求测试敏感文件是否可以直接访问</p>
        <p><strong>期望结果:</strong></p>
        <ul>
            <li><strong>403 Forbidden</strong> - 文件受.htaccess保护 ✅</li>
            <li><strong>404 Not Found</strong> - 文件不存在（也算安全） ✅</li>
            <li><strong>200 OK</strong> - 文件可直接访问 ❌ 安全风险</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🧪 运行保护测试</h2>
        <form method="POST">
            <button type="submit" name="test_protection">测试访问保护</button>
        </form>
        <p class="info">💡 此测试会尝试访问敏感文件，检查.htaccess保护是否生效</p>
    </div>
    
    <?php if ($testResult): ?>
    <div class="test-section">
        <h2>📊 测试结果统计</h2>
        <?php
        $protectionRate = $testResult['total_tests'] > 0 ? 
            round(($testResult['protected_count'] / $testResult['total_tests']) * 100, 1) : 0;
        ?>
        <div style="padding: 15px; border-radius: 5px; <?php echo $protectionRate >= 90 ? 'background: #d4edda; color: #155724;' : ($protectionRate >= 70 ? 'background: #fff3cd; color: #856404;' : 'background: #f8d7da; color: #721c24;'); ?>">
            <h3>保护率: <?php echo $protectionRate; ?>%</h3>
            <p>
                <strong>受保护:</strong> <?php echo $testResult['protected_count']; ?> 个 | 
                <strong>有风险:</strong> <?php echo $testResult['vulnerable_count']; ?> 个 | 
                <strong>总计:</strong> <?php echo $testResult['total_tests']; ?> 个
            </p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📋 详细测试结果</h2>
        <table>
            <tr>
                <th>文件路径</th>
                <th>描述</th>
                <th>HTTP状态码</th>
                <th>保护状态</th>
                <th>说明</th>
            </tr>
            <?php foreach ($testResult['tests'] as $test): ?>
                <tr class="<?php echo $test['protected'] ? 'protected' : 'vulnerable'; ?>">
                    <td><code><?php echo htmlspecialchars($test['url']); ?></code></td>
                    <td><?php echo htmlspecialchars($test['description']); ?></td>
                    <td>
                        <?php if ($test['status_code']): ?>
                            <strong><?php echo $test['status_code']; ?></strong>
                        <?php else: ?>
                            <span class="error">错误</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($test['protected']): ?>
                            <span class="success">🔒 受保护</span>
                        <?php else: ?>
                            <span class="error">🔓 有风险</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php
                        if ($test['error']) {
                            echo '<span class="error">' . htmlspecialchars($test['error']) . '</span>';
                        } elseif ($test['status_code'] == 403) {
                            echo '<span class="success">Forbidden - .htaccess保护生效</span>';
                        } elseif ($test['status_code'] == 404) {
                            echo '<span class="info">Not Found - 文件不存在</span>';
                        } elseif ($test['status_code'] == 200) {
                            echo '<span class="error">可直接访问 - 需要加强保护</span>';
                        } else {
                            echo '<span class="warning">状态码: ' . $test['status_code'] . '</span>';
                        }
                        ?>
                    </td>
                </tr>
            <?php endforeach; ?>
        </table>
    </div>
    
    <?php if ($testResult['vulnerable_count'] > 0): ?>
    <div class="test-section">
        <h2>⚠️ 安全建议</h2>
        <div class="error" style="padding: 15px; background: #f8d7da; border-radius: 5px;">
            <h3>发现 <?php echo $testResult['vulnerable_count']; ?> 个安全风险</h3>
            <p><strong>建议措施:</strong></p>
            <ol>
                <li>检查根目录.htaccess文件是否正确配置</li>
                <li>检查vendor/.htaccess文件是否存在</li>
                <li>确认Apache mod_rewrite模块已启用</li>
                <li>检查服务器是否支持.htaccess文件</li>
                <li>考虑将敏感文件移到web根目录之外</li>
            </ol>
        </div>
    <?php else: ?>
    <div class="test-section">
        <h2>✅ 安全状态良好</h2>
        <div class="success" style="padding: 15px; background: #d4edda; border-radius: 5px;">
            <h3>🎉 所有敏感文件都受到保护</h3>
            <p>您的.htaccess配置正确，敏感文件无法直接访问。</p>
        </div>
    <?php endif; ?>
    <?php endif; ?>
    
    <div class="test-section">
        <h2>🔒 .htaccess保护机制说明</h2>
        
        <h3>📁 vendor目录保护 (vendor/.htaccess):</h3>
        <ul>
            <li><code>Deny from all</code> - 拒绝所有访问</li>
            <li><code>Options -Indexes</code> - 禁止目录浏览</li>
            <li><code>php_flag engine off</code> - 禁用PHP执行</li>
        </ul>
        
        <h3>🏠 根目录保护 (.htaccess):</h3>
        <ul>
            <li><code>RewriteRule ^vendor/.*$ - [F,L]</code> - 禁止访问vendor目录</li>
            <li><code>RewriteRule ^(docs|test|server)/.*$ - [F,L]</code> - 保护敏感目录</li>
            <li>文件类型过滤 - 保护配置文件、文档文件等</li>
        </ul>
        
        <h3>🛡️ 安全头部:</h3>
        <ul>
            <li><code>X-XSS-Protection</code> - 防止XSS攻击</li>
            <li><code>X-Content-Type-Options</code> - 防止MIME嗅探</li>
            <li><code>X-Frame-Options</code> - 防止点击劫持</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关工具</h2>
        <ul>
            <li><a href="security-check.php">安全检查工具</a></li>
            <li><a href="final-verification.php">最终验证</a></li>
            <li><a href="../new-lock.html">返回登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
