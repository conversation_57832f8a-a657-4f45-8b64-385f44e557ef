#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新用户认证系统 - 激活码生成脚本
生成30天有效期的激活码，格式参考主流商业软件
激活码格式: XXXXX-XXXXX-XXXXX-XXXXX (20位字符，4组，每组5位)
支持新的用户认证数据库结构
"""

import sqlite3
import random
import string
from datetime import datetime, timedelta
import hashlib
import os
import secrets

class ActivationCodeGenerator:
    def __init__(self, db_path=None):
        # 如果未指定路径，使用默认路径
        if db_path is None:
            # 获取当前脚本所在目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            self.db_path = os.path.join(current_dir, 'user_system.db3')
        else:
            self.db_path = db_path

        # 详细的路径调试信息
        print(f"🔍 Python脚本路径: {os.path.abspath(__file__)}")
        print(f"🔍 当前工作目录: {os.getcwd()}")
        print(f"🔍 脚本所在目录: {os.path.dirname(os.path.abspath(__file__))}")
        print(f"🔍 使用数据库路径: {self.db_path}")
        print(f"🔍 数据库绝对路径: {os.path.abspath(self.db_path)}")

        # 检查数据库是否存在
        if not os.path.exists(self.db_path):
            print(f"❌ 错误: 数据库文件不存在: {self.db_path}")
            print(f"❌ 绝对路径: {os.path.abspath(self.db_path)}")

            # 尝试查找可能的数据库文件
            possible_paths = [
                'user_system.db3',
                '../server/user_system.db3',
                './server/user_system.db3',
                os.path.join(os.path.dirname(__file__), 'user_system.db3'),
                os.path.join(os.getcwd(), 'user_system.db3'),
                os.path.join(os.getcwd(), 'server', 'user_system.db3')
            ]

            print("🔍 尝试查找数据库文件:")
            for path in possible_paths:
                abs_path = os.path.abspath(path)
                exists = os.path.exists(path)
                print(f"   {'✅' if exists else '❌'} {path} -> {abs_path}")
                if exists:
                    print(f"   📁 找到数据库文件，建议使用: {abs_path}")
        else:
            print(f"✅ 数据库文件存在")
            # 显示数据库文件信息
            stat = os.stat(self.db_path)
            size_mb = stat.st_size / 1024 / 1024
            print(f"📊 数据库大小: {size_mb:.2f} MB")
            print(f"📅 最后修改: {datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')}")

        # 激活码字符集（排除容易混淆的字符：0, O, I, 1, l）
        self.charset = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'
    
    def generate_code_segment(self, length=5):
        """生成激活码片段（使用安全随机数）"""
        return ''.join(secrets.choice(self.charset) for _ in range(length))
    
    def generate_activation_code(self):
        """生成完整激活码 格式: XXXXX-XXXXX-XXXXX-XXXXX"""
        segments = [self.generate_code_segment() for _ in range(4)]
        return '-'.join(segments)
    
    def validate_code_format(self, code):
        """验证激活码格式"""
        if len(code) != 23:  # 20个字符 + 3个连字符
            return False
        
        parts = code.split('-')
        if len(parts) != 4:
            return False
        
        for part in parts:
            if len(part) != 5:
                return False
            if not all(c in self.charset for c in part):
                return False
        
        return True
    
    def code_exists(self, code):
        """检查激活码是否已存在"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM activation_codes WHERE code = ?", (code,))
        exists = cursor.fetchone()[0] > 0
        
        conn.close()
        return exists
    
    def create_activation_code(self, valid_days=30):
        """创建新的激活码并保存到数据库"""
        print(f"\n🔄 开始生成激活码...")
        print(f"📅 有效期: {valid_days} 天")

        # 再次确认数据库路径
        if not os.path.exists(self.db_path):
            print(f"❌ 致命错误: 数据库文件不存在: {self.db_path}")
            return None

        # 生成唯一激活码
        print("🎲 生成唯一激活码...")
        attempt = 0
        while True:
            attempt += 1
            code = self.generate_activation_code()
            print(f"   尝试 #{attempt}: {code}")
            if not self.code_exists(code):
                print(f"   ✅ 激活码唯一，可以使用")
                break
            else:
                print(f"   ⚠️ 激活码已存在，重新生成")

        # 计算过期时间
        created_at = datetime.now()
        expires_at = created_at + timedelta(days=valid_days)

        print(f"⏰ 时间计算:")
        print(f"   创建时间: {created_at.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   过期时间: {expires_at.strftime('%Y-%m-%d %H:%M:%S')}")

        # 保存到数据库
        print(f"💾 保存到数据库: {self.db_path}")

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 检查数据库连接
            cursor.execute("SELECT COUNT(*) FROM activation_codes")
            current_count = cursor.fetchone()[0]
            print(f"📊 当前数据库中激活码数量: {current_count}")

            # 插入新激活码
            insert_sql = """
                INSERT INTO activation_codes (code, expires_at, status)
                VALUES (?, ?, 'active')
            """
            insert_params = (code, expires_at.strftime('%Y-%m-%d %H:%M:%S'))

            print(f"📝 执行SQL: {insert_sql}")
            print(f"📝 参数: {insert_params}")

            cursor.execute(insert_sql, insert_params)

            # 获取插入的记录ID
            new_id = cursor.lastrowid
            print(f"🆔 新记录ID: {new_id}")

            # 验证插入结果
            cursor.execute("SELECT * FROM activation_codes WHERE id = ?", (new_id,))
            inserted_record = cursor.fetchone()
            print(f"🔍 插入的记录: {inserted_record}")

            # 提交事务
            conn.commit()
            print(f"✅ 数据库事务已提交")

            # 再次验证记录状态
            cursor.execute("SELECT code, is_used, status FROM activation_codes WHERE id = ?", (new_id,))
            final_record = cursor.fetchone()
            print(f"🔍 最终记录状态: {final_record}")

            print(f"\n🎉 激活码生成成功:")
            print(f"   激活码: {code}")
            print(f"   记录ID: {new_id}")
            print(f"   创建时间: {created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   过期时间: {expires_at.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   有效期: {valid_days} 天")
            print(f"   is_used: {final_record[1] if final_record else 'unknown'}")
            print(f"   status: {final_record[2] if final_record else 'unknown'}")

            return {
                'code': code,
                'id': new_id,
                'created_at': created_at,
                'expires_at': expires_at,
                'valid_days': valid_days,
                'is_used': final_record[1] if final_record else None,
                'status': final_record[2] if final_record else None
            }

        except Exception as e:
            print(f"❌ 激活码保存失败: {e}")
            print(f"❌ 错误类型: {type(e).__name__}")
            import traceback
            print(f"❌ 详细错误信息:")
            traceback.print_exc()

            try:
                conn.rollback()
                print(f"🔄 数据库事务已回滚")
            except:
                print(f"⚠️ 回滚失败")

            return None

        finally:
            try:
                conn.close()
                print(f"🔒 数据库连接已关闭")
            except:
                print(f"⚠️ 关闭数据库连接失败")
    
    def batch_generate(self, count=1, valid_days=30):
        """批量生成激活码"""
        print(f"🔄 开始批量生成 {count} 个激活码...")

        codes = []
        for i in range(count):
            print(f"\n📝 生成第 {i+1}/{count} 个激活码:")
            result = self.create_activation_code(valid_days)
            if result:
                codes.append(result)

        print(f"\n✅ 批量生成完成，成功生成 {len(codes)} 个激活码")
        return codes
    
    def list_active_codes(self):
        """列出所有有效的激活码"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("""
            SELECT ac.code, ac.created_at, ac.expires_at, ac.is_used, ac.status, u.username
            FROM activation_codes ac
            LEFT JOIN users u ON ac.bound_user_id = u.id
            WHERE ac.status = 'active' AND ac.expires_at > datetime('now')
            ORDER BY ac.created_at DESC
        """)

        codes = cursor.fetchall()
        conn.close()

        if codes:
            print(f"\n📋 当前有效激活码 ({len(codes)} 个):")
            print("-" * 90)
            print(f"{'激活码':<25} {'创建时间':<20} {'过期时间':<20} {'状态':<10} {'绑定用户':<15}")
            print("-" * 90)

            for code, created, expires, used, status, username in codes:
                used_status = "已使用" if used else "未使用"
                bound_user = username if username else "未绑定"
                print(f"{code:<25} {created:<20} {expires:<20} {used_status:<10} {bound_user:<15}")
        else:
            print("📭 暂无有效激活码")

        return codes

    def list_users_with_codes(self):
        """列出所有用户及其绑定的激活码"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("""
            SELECT u.username, u.email, u.created_at, ac.code, ac.expires_at, ac.status
            FROM users u
            LEFT JOIN activation_codes ac ON ac.bound_user_id = u.id
            ORDER BY u.created_at DESC
        """)

        users = cursor.fetchall()
        conn.close()

        if users:
            print(f"\n👥 用户及激活码绑定情况 ({len(users)} 条记录):")
            print("-" * 100)
            print(f"{'用户名':<15} {'邮箱':<25} {'注册时间':<20} {'激活码':<25} {'过期时间':<15}")
            print("-" * 100)

            for username, email, created, code, expires, status in users:
                code_display = code if code else "未绑定"
                expires_display = expires[:10] if expires else "-"
                print(f"{username:<15} {email:<25} {created:<20} {code_display:<25} {expires_display:<15}")
        else:
            print("📭 暂无用户数据")

        return users

    def get_database_stats(self):
        """获取数据库统计信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 统计各种数据
        stats = {}

        # 用户统计
        cursor.execute("SELECT COUNT(*) FROM users")
        stats['total_users'] = cursor.fetchone()[0]

        # 激活码统计
        cursor.execute("SELECT COUNT(*) FROM activation_codes")
        stats['total_codes'] = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM activation_codes WHERE status = 'active' AND expires_at > datetime('now')")
        stats['active_codes'] = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM activation_codes WHERE is_used = 1")
        stats['used_codes'] = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM activation_codes WHERE expires_at <= datetime('now')")
        stats['expired_codes'] = cursor.fetchone()[0]

        # 会话统计
        cursor.execute("SELECT COUNT(*) FROM user_sessions WHERE is_active = 1")
        stats['active_sessions'] = cursor.fetchone()[0]

        conn.close()

        print(f"\n📊 数据库统计信息:")
        print("-" * 40)
        print(f"总用户数: {stats['total_users']}")
        print(f"总激活码数: {stats['total_codes']}")
        print(f"有效激活码: {stats['active_codes']}")
        print(f"已使用激活码: {stats['used_codes']}")
        print(f"已过期激活码: {stats['expired_codes']}")
        print(f"活跃会话数: {stats['active_sessions']}")

        return stats

def main():
    """主函数"""
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    db_path = os.path.join(current_dir, 'user_system.db3')

    # 确保数据库存在
    if not os.path.exists(db_path):
        print("❌ 数据库不存在，请先运行 create_db.php 初始化数据库")
        print("   命令: php create_db.php")
        print(f"   期望路径: {db_path}")
        return

    generator = ActivationCodeGenerator(db_path)

    print("🔐 新用户认证系统 - 激活码生成器")
    print("=" * 60)

    while True:
        print("\n请选择操作:")
        print("1. 生成单个激活码")
        print("2. 批量生成激活码")
        print("3. 查看有效激活码")
        print("4. 查看用户绑定情况")
        print("5. 数据库统计信息")
        print("6. 退出")

        choice = input("\n请输入选项 (1-6): ").strip()

        if choice == '1':
            days = input("请输入有效天数 (默认30天): ").strip()
            days = int(days) if days.isdigit() else 30
            generator.create_activation_code(days)

        elif choice == '2':
            count = input("请输入生成数量: ").strip()
            if not count.isdigit():
                print("❌ 请输入有效数字")
                continue

            days = input("请输入有效天数 (默认30天): ").strip()
            days = int(days) if days.isdigit() else 30

            generator.batch_generate(int(count), days)

        elif choice == '3':
            generator.list_active_codes()

        elif choice == '4':
            generator.list_users_with_codes()

        elif choice == '5':
            generator.get_database_stats()

        elif choice == '6':
            print("👋 再见!")
            break

        else:
            print("❌ 无效选项，请重新选择")

if __name__ == "__main__":
    main()
