<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单对齐测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 15px;
            margin: 8px 0;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.95);
            font-size: 16px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
            background: white;
        }
        
        .captcha-group {
            display: flex;
            gap: 10px;
            align-items: center;
            margin: 8px 0;
            width: 100%;
            box-sizing: border-box;
        }
        
        .captcha-input {
            flex: 1;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.95);
            font-size: 16px;
            box-sizing: border-box;
            margin: 0;
        }

        .captcha-input:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
            background: white;
        }
        
        .captcha-image {
            width: 120px;
            height: 44px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            flex-shrink: 0;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 12px;
        }
        
        .captcha-image:hover {
            border-color: #4CAF50;
            transform: scale(1.05);
        }
        
        .alignment-guide {
            position: relative;
        }
        
        .alignment-guide::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 2px;
            background: red;
            opacity: 0.5;
            z-index: 10;
        }
        
        .test-section {
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin: 20px 0;
        }
        
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .info {
            color: #0c5460;
            background: #d1ecf1;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>📐 表单对齐测试</h1>
    
    <div class="container">
        <h2>对齐问题说明</h2>
        <div class="info">
            <strong>🎯 修复内容:</strong><br>
            1. 验证码输入框与用户名、密码框左对齐<br>
            2. 验证码图片高度与输入框匹配<br>
            3. 所有表单（登录、注册、密码找回）统一对齐<br>
            4. 添加红色参考线显示对齐效果
        </div>
    </div>
    
    <div class="container">
        <div class="test-section">
            <h3>🔑 登录表单对齐测试</h3>
            <div class="alignment-guide">
                <input type="text" class="form-input" placeholder="用户名" value="testuser">
                <input type="password" class="form-input" placeholder="密码" value="••••••••">
                
                <div class="captcha-group">
                    <input type="text" class="captcha-input" placeholder="验证码" value="A8B2">
                    <div class="captcha-image">验证码图片</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📝 注册表单对齐测试</h3>
            <div class="alignment-guide">
                <input type="text" class="form-input" placeholder="用户名 (3-20位字母数字)" value="newuser">
                <input type="password" class="form-input" placeholder="密码 (至少6位)" value="••••••••">
                <input type="email" class="form-input" placeholder="邮箱 (必填，用于密码找回)" value="<EMAIL>">
                
                <div class="captcha-group">
                    <input type="text" class="captcha-input" placeholder="验证码" value="X9Y3">
                    <div class="captcha-image">验证码图片</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔑 密码找回对齐测试</h3>
            <div class="alignment-guide">
                <input type="email" class="form-input" placeholder="请输入注册邮箱" value="<EMAIL>">
                
                <div class="captcha-group">
                    <input type="text" class="captcha-input" placeholder="验证码" value="M5N7">
                    <div class="captcha-image">验证码图片</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <h2>✅ 修复效果</h2>
        <div class="success">
            <strong>对齐优化完成:</strong><br>
            • ✅ 验证码输入框左边缘与其他输入框完全对齐<br>
            • ✅ 验证码图片高度与输入框匹配（44px）<br>
            • ✅ 所有表单保持一致的对齐效果<br>
            • ✅ 响应式设计保持不变<br>
            • ✅ 添加了防压缩属性确保图片尺寸稳定
        </div>
    </div>
    
    <div class="container">
        <h2>🔧 技术细节</h2>
        <div class="info">
            <strong>CSS修改:</strong><br>
            1. <code>.captcha-group</code>: 添加 <code>width: 100%</code> 和 <code>box-sizing: border-box</code><br>
            2. <code>.captcha-input</code>: 添加 <code>margin: 0</code> 确保无额外边距<br>
            3. <code>.captcha-image</code>: 高度从 <code>40px</code> 调整为 <code>44px</code><br>
            4. <code>.captcha-image</code>: 添加 <code>flex-shrink: 0</code> 防止压缩<br><br>
            
            <strong>对齐原理:</strong><br>
            • 所有输入框使用相同的 <code>padding: 12px 15px</code><br>
            • 验证码组合框使用 <code>flex: 1</code> 自动填充剩余空间<br>
            • 验证码图片固定宽度，不参与弹性布局
        </div>
    </div>
    
    <div class="container">
        <h2>🔗 快速链接</h2>
        <button onclick="window.location.href='../new-lock.html'" style="
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        ">查看实际效果</button>
        
        <button onclick="toggleAlignmentGuide()" style="
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        ">切换对齐参考线</button>
    </div>

    <script>
        function toggleAlignmentGuide() {
            const guides = document.querySelectorAll('.alignment-guide');
            guides.forEach(guide => {
                if (guide.style.position === 'relative') {
                    guide.style.position = '';
                } else {
                    guide.style.position = 'relative';
                }
            });
        }
        
        // 模拟验证码图片点击
        document.querySelectorAll('.captcha-image').forEach(img => {
            img.addEventListener('click', function() {
                this.textContent = '验证码已刷新';
                setTimeout(() => {
                    this.textContent = '验证码图片';
                }, 1000);
            });
        });
    </script>
</body>
</html>
