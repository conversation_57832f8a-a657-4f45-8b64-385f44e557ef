<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logo响应式布局测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .viewport-simulator {
            border: 2px solid #ddd;
            margin: 15px 0;
            position: relative;
            background: linear-gradient(135deg, #ff4444 0%, #cc0000 50%, #990000 100%);
            overflow: hidden;
        }
        .viewport-label {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            z-index: 10;
        }
        .logo-test {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        .logo-test img {
            display: block;
            object-fit: contain;
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        /* 模拟不同屏幕尺寸 */
        .mobile-320 {
            width: 320px;
            height: 200px;
        }
        .mobile-320 .logo-test img {
            max-width: 60px;
            max-height: 40px;
            margin-left: 2px;
            margin-top: 2px;
        }
        
        .mobile-480 {
            width: 480px;
            height: 250px;
        }
        .mobile-480 .logo-test img {
            max-width: 80px;
            max-height: 50px;
            margin-left: 3px;
            margin-top: 3px;
        }
        
        .tablet-768 {
            width: 768px;
            height: 300px;
        }
        .tablet-768 .logo-test img {
            max-width: 100px;
            max-height: 60px;
            margin-left: 5px;
            margin-top: 5px;
        }
        
        .desktop-1024 {
            width: 1024px;
            height: 400px;
        }
        .desktop-1024 .logo-test img {
            max-width: 120px;
            max-height: 80px;
            margin-left: 10px;
            margin-top: 10px;
        }
        
        .large-1440 {
            width: 1440px;
            height: 500px;
        }
        .large-1440 .logo-test img {
            max-width: 160px;
            max-height: 100px;
            margin-left: 15px;
            margin-top: 15px;
        }
        
        .xlarge-1920 {
            width: 1920px;
            height: 600px;
            transform: scale(0.5);
            transform-origin: top left;
        }
        .xlarge-1920 .logo-test img {
            max-width: 200px;
            max-height: 120px;
            margin-left: 20px;
            margin-top: 20px;
        }
        
        .size-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .control-panel {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .test-button {
            background-color: #007bff;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 12px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🖼️ Logo响应式布局测试</h1>
    
    <!-- 测试说明 -->
    <div class="test-container">
        <h2 class="test-title">📋 测试说明</h2>
        <p>此页面模拟不同屏幕尺寸下logo的显示效果，验证响应式布局是否正确。</p>
        <div class="size-info">
            <strong>修复前问题:</strong> 使用视口单位(vw/vh)导致logo在页面缩放时反向变大<br>
            <strong>修复后方案:</strong> 使用固定像素单位(px)配合响应式断点
        </div>
    </div>
    
    <!-- 控制面板 -->
    <div class="test-container">
        <h2 class="test-title">🎮 测试控制</h2>
        <div class="control-panel">
            <h4>快速测试</h4>
            <button class="test-button" onclick="testAllSizes()">🔄 测试所有尺寸</button>
            <button class="test-button" onclick="showSizeInfo()">📊 显示尺寸信息</button>
            <button class="test-button" onclick="openRealPages()">🔗 打开真实页面</button>
        </div>
    </div>
    
    <!-- 超小屏幕 (320px) -->
    <div class="test-container">
        <h3>📱 超小屏幕 (320px) - 老旧手机</h3>
        <div class="viewport-simulator mobile-320">
            <div class="viewport-label">320×200</div>
            <div class="logo-test">
                <img src="../images/logo.png" alt="Logo" onerror="this.src='../uploads/logo.png'">
            </div>
        </div>
        <div class="size-info">
            Logo尺寸: 60×40px | 边距: 2px | 适合: 老旧手机、小屏设备
        </div>
    </div>
    
    <!-- 小屏幕 (480px) -->
    <div class="test-container">
        <h3>📱 小屏幕 (480px) - 现代手机</h3>
        <div class="viewport-simulator mobile-480">
            <div class="viewport-label">480×250</div>
            <div class="logo-test">
                <img src="../images/logo.png" alt="Logo" onerror="this.src='../uploads/logo.png'">
            </div>
        </div>
        <div class="size-info">
            Logo尺寸: 80×50px | 边距: 3px | 适合: 现代手机竖屏
        </div>
    </div>
    
    <!-- 平板 (768px) -->
    <div class="test-container">
        <h3>📱 平板 (768px) - iPad等</h3>
        <div class="viewport-simulator tablet-768">
            <div class="viewport-label">768×300</div>
            <div class="logo-test">
                <img src="../images/logo.png" alt="Logo" onerror="this.src='../uploads/logo.png'">
            </div>
        </div>
        <div class="size-info">
            Logo尺寸: 100×60px | 边距: 5px | 适合: 平板设备
        </div>
    </div>
    
    <!-- 桌面 (1024px) -->
    <div class="test-container">
        <h3>💻 桌面 (1024px) - 小笔记本</h3>
        <div class="viewport-simulator desktop-1024">
            <div class="viewport-label">1024×400</div>
            <div class="logo-test">
                <img src="../images/logo.png" alt="Logo" onerror="this.src='../uploads/logo.png'">
            </div>
        </div>
        <div class="size-info">
            Logo尺寸: 120×80px | 边距: 10px | 适合: 小笔记本、标准桌面
        </div>
    </div>
    
    <!-- 大屏幕 (1440px) -->
    <div class="test-container">
        <h3>🖥️ 大屏幕 (1440px) - 大显示器</h3>
        <div class="viewport-simulator large-1440">
            <div class="viewport-label">1440×500</div>
            <div class="logo-test">
                <img src="../images/logo.png" alt="Logo" onerror="this.src='../uploads/logo.png'">
            </div>
        </div>
        <div class="size-info">
            Logo尺寸: 160×100px | 边距: 15px | 适合: 大显示器、高分辨率屏幕
        </div>
    </div>
    
    <!-- 超大屏幕 (1920px) -->
    <div class="test-container">
        <h3>📺 超大屏幕 (1920px) - 4K显示器/电视</h3>
        <div class="viewport-simulator xlarge-1920">
            <div class="viewport-label">1920×600 (缩放50%)</div>
            <div class="logo-test">
                <img src="../images/logo.png" alt="Logo" onerror="this.src='../uploads/logo.png'">
            </div>
        </div>
        <div class="size-info">
            Logo尺寸: 200×120px | 边距: 20px | 适合: 4K显示器、大电视、Android TV
        </div>
    </div>

    <script>
        function testAllSizes() {
            const logos = document.querySelectorAll('.logo-test img');
            let index = 0;
            
            function highlightNext() {
                // 移除之前的高亮
                logos.forEach(logo => {
                    logo.style.border = '1px solid rgba(255,255,255,0.3)';
                });
                
                if (index < logos.length) {
                    // 高亮当前logo
                    logos[index].style.border = '3px solid #ffff00';
                    logos[index].scrollIntoView({ behavior: 'smooth', block: 'center' });
                    
                    index++;
                    setTimeout(highlightNext, 1500);
                } else {
                    console.log('✅ 所有尺寸测试完成');
                }
            }
            
            highlightNext();
        }
        
        function showSizeInfo() {
            const logos = document.querySelectorAll('.logo-test img');
            const info = [];
            
            logos.forEach((logo, index) => {
                const rect = logo.getBoundingClientRect();
                const computed = window.getComputedStyle(logo);
                
                info.push({
                    index: index + 1,
                    maxWidth: computed.maxWidth,
                    maxHeight: computed.maxHeight,
                    actualWidth: Math.round(rect.width),
                    actualHeight: Math.round(rect.height),
                    marginLeft: computed.marginLeft,
                    marginTop: computed.marginTop
                });
            });
            
            console.table(info);
            alert('Logo尺寸信息已输出到控制台，请按F12查看');
        }
        
        function openRealPages() {
            window.open('../index.php', '_blank');
            window.open('../index-tv-optimized.php', '_blank');
        }
        
        // 页面加载完成后的提示
        window.addEventListener('load', () => {
            console.log('📋 Logo响应式测试页面加载完成');
            console.log('🔍 观察不同屏幕尺寸下logo的大小变化');
            console.log('✅ 修复后logo应该在小屏幕上更小，大屏幕上更大');
        });
    </script>
</body>
</html>
