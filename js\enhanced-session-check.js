/**
 * 增强版会话检查系统
 * 集成设备检测和智能跳转功能
 */

class EnhancedSessionChecker {
    constructor() {
        this.sessionToken = null;
        this.userInfo = null;
        this.deviceInfo = null;
        this.checkInterval = null;
        this.init();
    }

    async init() {
        console.log('🔧 增强版会话检查系统启动');
        
        // 等待设备检测完成
        await this.waitForDeviceDetection();
        
        // 获取会话令牌
        this.sessionToken = this.getSessionToken();
        
        if (!this.sessionToken) {
            console.log('❌ 未找到会话令牌，跳转到登录页面');
            this.redirectToLogin();
            return;
        }
        
        // 验证会话
        const isValid = await this.verifySession();
        
        if (!isValid) {
            console.log('❌ 会话验证失败，跳转到登录页面');
            this.redirectToLogin();
            return;
        }
        
        console.log('✅ 会话验证通过');
        
        // 检查是否需要根据设备类型跳转
        await this.checkDeviceBasedRedirect();
        
        // 初始化主页面
        this.initMainPage();
        
        // 启动定期检查
        this.startPeriodicCheck();
    }

    /**
     * 等待设备检测完成
     */
    async waitForDeviceDetection() {
        return new Promise((resolve) => {
            const checkDevice = () => {
                if (window.smartDeviceDetector && window.deviceInfo) {
                    this.deviceInfo = window.deviceInfo;
                    resolve();
                } else {
                    setTimeout(checkDevice, 100);
                }
            };
            checkDevice();
        });
    }

    /**
     * 检查是否需要根据设备类型跳转
     */
    async checkDeviceBasedRedirect() {
        if (!this.deviceInfo) return;

        // 检查是否是手动切换 - 手动切换优先级最高
        const manualSwitch = sessionStorage.getItem('manual_version_switch') === 'true';

        if (manualSwitch) {
            console.log('🔧 检测到手动版本切换，会话检查跳过自动跳转');
            return;
        }

        // 检查是否已经有设备检测在进行中
        const deviceRedirectDone = sessionStorage.getItem('device_redirect_done') === 'true';

        if (deviceRedirectDone) {
            console.log('🔧 设备检测已完成，会话检查跳过重复跳转');
            return;
        }

        const currentPage = this.getCurrentPage();
        const shouldUseTV = this.deviceInfo.isAndroidTV;

        // 避免重复重定向
        if (sessionStorage.getItem('login_redirect_done') === 'true') {
            return;
        }

        if (shouldUseTV && currentPage !== 'tv-optimized') {
            console.log('📺 登录成功，跳转到Android TV优化版本');
            sessionStorage.setItem('login_redirect_done', 'true');
            this.redirectToTVVersion();
        } else if (!shouldUseTV && currentPage === 'tv-optimized') {
            console.log('💻 登录成功，跳转到标准版本');
            sessionStorage.setItem('login_redirect_done', 'true');
            this.redirectToStandardVersion();
        }
    }

    /**
     * 获取当前页面类型
     */
    getCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop();
        
        if (filename.includes('index-tv-optimized')) return 'tv-optimized';
        else if (filename.includes('index')) return 'standard';
        else return 'other';
    }

    /**
     * 跳转到TV优化版本
     */
    redirectToTVVersion() {
        const urlParams = new URLSearchParams(window.location.search);
        const targetUrl = 'index-tv-optimized.php' + (urlParams.toString() ? '?' + urlParams.toString() : '');
        
        this.showRedirectMessage('正在为您切换到Android TV优化版本...', () => {
            window.location.href = targetUrl;
        });
    }

    /**
     * 跳转到标准版本
     */
    redirectToStandardVersion() {
        const urlParams = new URLSearchParams(window.location.search);
        const targetUrl = 'index.php' + (urlParams.toString() ? '?' + urlParams.toString() : '');
        
        this.showRedirectMessage('正在为您切换到标准版本...', () => {
            window.location.href = targetUrl;
        });
    }

    /**
     * 显示跳转提示消息
     */
    showRedirectMessage(message, callback) {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        `;
        
        const messageBox = document.createElement('div');
        messageBox.style.cssText = `
            background: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        `;
        
        messageBox.innerHTML = `
            <div style="font-size: 18px; margin-bottom: 20px; color: #333;">
                🔄 ${message}
            </div>
            <div style="font-size: 14px; color: #666;">
                请稍候...
            </div>
        `;
        
        overlay.appendChild(messageBox);
        document.body.appendChild(overlay);
        
        setTimeout(() => {
            callback();
        }, 2000);
    }

    /**
     * 获取会话令牌
     */
    getSessionToken() {
        return localStorage.getItem('session_token') || 
               sessionStorage.getItem('session_token') ||
               this.getCookie('session_token');
    }

    /**
     * 获取Cookie值
     */
    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    }

    /**
     * 验证会话
     */
    async verifySession() {
        try {
            const response = await fetch('api/verify-session.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    session_token: this.sessionToken
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.userInfo = data.user_info;
                return true;
            } else {
                console.log('会话验证失败:', data.message);
                return false;
            }
        } catch (error) {
            console.error('会话验证错误:', error);
            return false;
        }
    }

    /**
     * 重定向到登录页面
     */
    redirectToLogin() {
        // 清除无效的会话令牌
        localStorage.removeItem('session_token');
        sessionStorage.removeItem('session_token');
        
        // 显示跳转提示
        this.showRedirectMessage('正在跳转到登录页面...', () => {
            window.location.href = 'new-lock.html';
        });
    }

    /**
     * 初始化主页面
     */
    initMainPage() {
        console.log('🎉 主页面初始化完成');

        // 注释掉动态生成的图标，现在使用静态HTML+CSS实现
        // this.displayUserInfo();
        // this.displayVersionSwitcher();

        // 清除重定向标记
        sessionStorage.removeItem('login_redirect_done');
    }

    /**
     * 显示用户信息 - 与原版本保持一致
     */
    displayUserInfo() {
        // 如果没有用户信息，尝试从会话中获取
        if (!this.userInfo) {
            this.userInfo = {
                username: 'testlogin',
                email: '<EMAIL>',
                activation_code: 'TEST7-26ABC-DEFGH-IJKLM',
                activation_expires: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60)
            };
        }

        // 创建用户图标容器
        const userContainer = document.createElement('div');
        userContainer.id = 'userInfoContainer';
        userContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        `;

        // 创建用户图标按钮
        const userIcon = document.createElement('div');
        userIcon.id = 'userIcon';
        userIcon.style.cssText = `
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 2px solid rgba(0,0,0,0.1);
        `;

        // 用户头像或图标
        userIcon.innerHTML = `
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#333" stroke-width="2">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
            </svg>
        `;

        // 创建下拉菜单 - 恢复原始的透明度动画样式
        const dropdown = document.createElement('div');
        dropdown.id = 'userDropdown';
        dropdown.style.cssText = `
            position: absolute;
            top: 50px;
            right: 0;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            padding: 15px;
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.1);
            overflow: hidden;
        `;

        // 菜单内容 - 恢复原始的详细信息显示
        const expiresDate = this.userInfo.activation_expires ?
            new Date(this.userInfo.activation_expires * 1000) :
            new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 默认30天后过期

        dropdown.innerHTML = `
            <div style="padding-bottom: 10px; border-bottom: 1px solid #eee; margin-bottom: 10px;">
                <div style="font-weight: bold; color: #333; margin-bottom: 5px;">👤 ${this.userInfo.username || '用户'}</div>
                <div style="font-size: 12px; color: #666;">已登录</div>
                ${this.deviceInfo && this.deviceInfo.isAndroidTV ?
                    '<div style="font-size: 11px; color: #007bff; margin-top: 5px;">📺 Android TV优化版</div>' :
                    '<div style="font-size: 11px; color: #28a745; margin-top: 5px;">💻 标准版</div>'
                }
            </div>
            <div style="margin-bottom: 10px;">
                <div style="font-size: 12px; color: #666; margin-bottom: 3px;">激活码</div>
                <div style="font-size: 13px; color: #333; font-family: monospace;">${this.userInfo.activation_code || 'N/A'}</div>
            </div>
            <div style="margin-bottom: 15px;">
                <div style="font-size: 12px; color: #666; margin-bottom: 3px;">到期时间</div>
                <div style="font-size: 13px; color: #333;">${expiresDate.toLocaleDateString()}</div>
            </div>
            <button id="logoutBtn" style="
                width: 100%;
                background: #f44336;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 13px;
                transition: background 0.3s ease;
            ">🚪 退出登录</button>
        `;

        // 组装元素
        userContainer.appendChild(userIcon);
        userContainer.appendChild(dropdown);
        document.body.appendChild(userContainer);

        // 绑定事件
        this.bindUserMenuEvents(userIcon, dropdown);
    }

    /**
     * 绑定用户菜单事件 - 恢复原始的悬停和透明度逻辑
     */
    bindUserMenuEvents(userIcon, dropdown) {
        let hideTimeout = null;
        let isMenuVisible = false;

        // 鼠标悬停显示菜单
        userIcon.addEventListener('mouseenter', () => {
            clearTimeout(hideTimeout);
            isMenuVisible = true;

            // 显示菜单
            dropdown.style.opacity = '1';
            dropdown.style.visibility = 'visible';
            dropdown.style.transform = 'translateY(0)';
            dropdown.style.display = 'block';

            // 图标高亮
            userIcon.style.background = 'rgba(76, 175, 80, 0.1)';
            userIcon.style.borderColor = 'rgba(76, 175, 80, 0.3)';
            userIcon.style.opacity = '1'; // 恢复完全不透明
        });

        // 鼠标离开图标
        userIcon.addEventListener('mouseleave', () => {
            if (!isMenuVisible) return;

            hideTimeout = setTimeout(() => {
                this.hideUserMenu(userIcon, dropdown);
                isMenuVisible = false;
            }, 100); // 短暂延迟，允许鼠标移到菜单上
        });

        // 鼠标进入菜单
        dropdown.addEventListener('mouseenter', () => {
            clearTimeout(hideTimeout);
        });

        // 鼠标离开菜单
        dropdown.addEventListener('mouseleave', () => {
            hideTimeout = setTimeout(() => {
                this.hideUserMenu(userIcon, dropdown);
                isMenuVisible = false;
            }, 100);
        });

        // 退出登录按钮事件
        const logoutBtn = dropdown.querySelector('#logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.logout();
            });

            logoutBtn.addEventListener('mouseenter', () => {
                logoutBtn.style.background = '#d32f2f';
            });

            logoutBtn.addEventListener('mouseleave', () => {
                logoutBtn.style.background = '#f44336';
            });
        }

        // 3秒后降低透明度到60% (原始功能)
        setTimeout(() => {
            if (!isMenuVisible) {
                userIcon.style.opacity = '0.6';
            }
        }, 3000);
    }

    /**
     * 隐藏用户菜单
     */
    hideUserMenu(userIcon, dropdown) {
        // 隐藏菜单
        dropdown.style.opacity = '0';
        dropdown.style.visibility = 'hidden';
        dropdown.style.transform = 'translateY(-10px)';
        dropdown.style.display = 'none';

        // 恢复图标样式
        userIcon.style.background = 'rgba(255, 255, 255, 0.9)';
        userIcon.style.borderColor = 'rgba(0,0,0,0.1)';

        // 3秒后降低透明度到60% (原始功能)
        setTimeout(() => {
            userIcon.style.opacity = '0.6';
        }, 3000);
    }

    /**
     * 显示版本切换器
     */
    displayVersionSwitcher() {
        // 检查是否已存在
        if (document.getElementById('versionSwitcher')) return;

        const currentPath = window.location.pathname;
        const isTV = currentPath.includes('index-tv-optimized.php');
        const isStandard = currentPath.includes('index.php');

        // 只在主页面显示切换器
        if (!isTV && !isStandard) return;

        // 创建版本切换器容器
        const switcher = document.createElement('div');
        switcher.id = 'versionSwitcher';
        switcher.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 999;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            opacity: 0.3;
            backdrop-filter: blur(5px);
            border: 2px solid rgba(255, 255, 255, 0.2);
        `;

        // 图标内容
        const icon = isTV ? '💻' : '📺';
        const tooltip = isTV ? '切换到标准版' : '切换到TV优化版';

        switcher.innerHTML = `
            <span style="font-size: 20px; user-select: none;" title="${tooltip}">${icon}</span>
        `;

        // 悬停效果
        switcher.addEventListener('mouseenter', () => {
            switcher.style.opacity = '0.8';
            switcher.style.transform = 'scale(1.1)';
        });

        switcher.addEventListener('mouseleave', () => {
            switcher.style.opacity = '0.3';
            switcher.style.transform = 'scale(1)';
        });

        // 点击切换
        switcher.addEventListener('click', () => {
            this.switchVersion();
        });

        document.body.appendChild(switcher);
    }

    /**
     * 切换版本
     */
    switchVersion() {
        const currentPath = window.location.pathname;

        if (currentPath.includes('index-tv-optimized.php')) {
            // 切换到标准版本
            if (window.smartDeviceDetector) {
                window.smartDeviceDetector.manualSwitchToStandard();
            } else {
                // 备用方案
                sessionStorage.setItem('manual_version_switch', 'true');
                sessionStorage.setItem('preferred_version', 'index.php');
                window.location.href = 'index.php';
            }
        } else if (currentPath.includes('index.php')) {
            // 切换到TV版本
            if (window.smartDeviceDetector) {
                window.smartDeviceDetector.manualSwitchToTV();
            } else {
                // 备用方案
                sessionStorage.setItem('manual_version_switch', 'true');
                sessionStorage.setItem('preferred_version', 'index-tv-optimized.php');
                window.location.href = 'index-tv-optimized.php';
            }
        }
    }

    /**
     * 退出登录
     */
    async logout() {
        try {
            await fetch('api/logout.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    session_token: this.sessionToken
                })
            });
        } catch (error) {
            console.error('退出登录错误:', error);
        }

        // 清除本地存储
        localStorage.removeItem('session_token');
        sessionStorage.removeItem('session_token');
        sessionStorage.removeItem('login_redirect_done');
        sessionStorage.removeItem('device_redirect_done');

        // 跳转到登录页面
        this.redirectToLogin();
    }

    /**
     * 启动定期检查
     */
    startPeriodicCheck() {
        this.checkInterval = setInterval(async () => {
            const isValid = await this.verifySession();
            if (!isValid) {
                console.log('定期检查：会话已失效');
                this.redirectToLogin();
            }
        }, 30 * 60 * 1000); // 每30分钟检查一次（适合商家广告长期展示）
    }
}

// 自动初始化
if (typeof window !== 'undefined') {
    window.addEventListener('DOMContentLoaded', () => {
        window.enhancedSessionChecker = new EnhancedSessionChecker();
    });
}

window.EnhancedSessionChecker = EnhancedSessionChecker;
