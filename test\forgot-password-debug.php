<?php
/**
 * 密码找回功能调试页面
 * 用于测试和调试密码找回API
 */

session_start();
header('Content-Type: text/html; charset=utf-8');

// 获取当前会话ID
$sessionId = session_id();

// 获取最新的验证码
function getCurrentCaptcha($sessionId) {
    $dbPath = '../server/user_system.db3';
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("
            SELECT code_value FROM captcha_codes 
            WHERE session_id = ? AND expires_at > datetime('now') AND is_used = 0
            ORDER BY created_at DESC LIMIT 1
        ");
        $stmt->execute([$sessionId]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['code_value'] : null;
    } catch (Exception $e) {
        return null;
    }
}

$currentCaptcha = getCurrentCaptcha($sessionId);

// 处理测试请求
$testResult = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_api'])) {
    $username = $_POST['username'] ?? '';
    $email = $_POST['email'] ?? '';
    $captcha = $_POST['captcha'] ?? '';
    
    // 构建POST数据
    $postData = json_encode([
        'username' => $username,
        'email' => $email,
        'captcha' => $captcha
    ]);
    
    // 发送API请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://promo.xxgogo.ggff.net:8866/api/forgot-password.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Cookie: PHPSESSID=' . $sessionId
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $testResult = [
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error,
        'post_data' => $postData
    ];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码找回功能调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
        input, button { padding: 8px; margin: 5px; }
        button { background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
    </style>
</head>
<body>
    <h1>🔧 密码找回功能调试</h1>
    <p><strong>调试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="test-section">
        <h2>📊 会话信息</h2>
        <p><strong>当前会话ID:</strong> <?php echo $sessionId; ?></p>
        <p><strong>当前有效验证码:</strong> 
            <?php if ($currentCaptcha): ?>
                <span class="success"><?php echo $currentCaptcha; ?></span>
            <?php else: ?>
                <span class="error">无有效验证码</span>
            <?php endif; ?>
        </p>
        <p><a href="../api/captcha.php" target="_blank">生成新验证码</a></p>
    </div>
    
    <div class="test-section">
        <h2>🧪 API测试</h2>
        <form method="POST">
            <div>
                <label>用户名:</label>
                <input type="text" name="username" value="<?php echo $_POST['username'] ?? 'testuser'; ?>" required>
            </div>
            <div>
                <label>邮箱:</label>
                <input type="email" name="email" value="<?php echo $_POST['email'] ?? '<EMAIL>'; ?>" required>
            </div>
            <div>
                <label>验证码:</label>
                <input type="text" name="captcha" value="<?php echo $currentCaptcha ?? ''; ?>" required>
                <small>(当前有效验证码已自动填入)</small>
            </div>
            <div>
                <button type="submit" name="test_api">测试密码找回API</button>
            </div>
        </form>
    </div>
    
    <?php if ($testResult): ?>
    <div class="test-section">
        <h2>📋 测试结果</h2>
        
        <h3>HTTP状态码</h3>
        <p class="<?php echo $testResult['http_code'] == 200 ? 'success' : 'error'; ?>">
            <?php echo $testResult['http_code']; ?>
        </p>
        
        <h3>发送的数据</h3>
        <pre><?php echo htmlspecialchars($testResult['post_data']); ?></pre>
        
        <h3>API响应</h3>
        <pre><?php echo htmlspecialchars($testResult['response']); ?></pre>
        
        <?php if ($testResult['error']): ?>
        <h3>cURL错误</h3>
        <p class="error"><?php echo htmlspecialchars($testResult['error']); ?></p>
        <?php endif; ?>
        
        <?php
        // 尝试解析JSON响应
        $jsonResponse = json_decode($testResult['response'], true);
        if ($jsonResponse):
        ?>
        <h3>解析后的响应</h3>
        <pre><?php echo htmlspecialchars(json_encode($jsonResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
        <?php endif; ?>
    </div>
    <?php endif; ?>
    
    <div class="test-section">
        <h2>🔍 数据库检查</h2>
        <?php
        // 检查数据库中的用户
        $dbPath = '../server/user_system.db3';
        try {
            $pdo = new PDO("sqlite:$dbPath");
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            echo "<h3>用户表中的数据 (前5条):</h3>";
            $stmt = $pdo->query("SELECT id, username, email, status FROM users LIMIT 5");
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if ($users) {
                echo "<table border='1' cellpadding='5'>";
                echo "<tr><th>ID</th><th>用户名</th><th>邮箱</th><th>状态</th></tr>";
                foreach ($users as $user) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($user['id']) . "</td>";
                    echo "<td>" . htmlspecialchars($user['username']) . "</td>";
                    echo "<td>" . htmlspecialchars($user['email']) . "</td>";
                    echo "<td>" . htmlspecialchars($user['status']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p class='warning'>用户表中没有数据</p>";
            }
            
            echo "<h3>验证码表中的数据 (最新5条):</h3>";
            $stmt = $pdo->query("
                SELECT id, code_value, session_id, is_used, expires_at, created_at 
                FROM captcha_codes 
                ORDER BY created_at DESC LIMIT 5
            ");
            $captchas = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if ($captchas) {
                echo "<table border='1' cellpadding='5'>";
                echo "<tr><th>ID</th><th>验证码</th><th>会话ID</th><th>已使用</th><th>过期时间</th><th>创建时间</th></tr>";
                foreach ($captchas as $captcha) {
                    $isCurrentSession = $captcha['session_id'] === $sessionId;
                    $rowClass = $isCurrentSession ? 'style="background-color: #e8f5e8;"' : '';
                    echo "<tr $rowClass>";
                    echo "<td>" . htmlspecialchars($captcha['id']) . "</td>";
                    echo "<td><strong>" . htmlspecialchars($captcha['code_value']) . "</strong></td>";
                    echo "<td>" . htmlspecialchars(substr($captcha['session_id'], 0, 20)) . "...</td>";
                    echo "<td>" . ($captcha['is_used'] ? '是' : '否') . "</td>";
                    echo "<td>" . htmlspecialchars($captcha['expires_at']) . "</td>";
                    echo "<td>" . htmlspecialchars($captcha['created_at']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                echo "<p><small>绿色背景表示当前会话的验证码</small></p>";
            } else {
                echo "<p class='warning'>验证码表中没有数据</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>数据库查询失败: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关链接</h2>
        <ul>
            <li><a href="debug-session-captcha.php">会话和验证码调试</a></li>
            <li><a href="php-config-test.php">PHP配置测试</a></li>
            <li><a href="../new-lock.html">返回登录页面</a></li>
            <li><a href="../api/captcha.php" target="_blank">生成验证码</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
