<?php
/**
 * 服务器写权限测试工具
 * 测试IIS和PHP是否有文件写入权限
 */

header('Content-Type: text/html; charset=utf-8');

// 处理文件创建请求
if (isset($_POST['create_file'])) {
    $testResults = [];
    
    // 测试1: 在test文件夹创建test.txt
    $testFile = __DIR__ . '/test.txt';
    try {
        $content = "HELLO WORLD\n";
        $content .= "创建时间: " . date('Y-m-d H:i:s') . "\n";
        $content .= "PHP版本: " . PHP_VERSION . "\n";
        $content .= "服务器软件: " . $_SERVER['SERVER_SOFTWARE'] . "\n";
        
        $result = file_put_contents($testFile, $content);
        if ($result !== false) {
            $testResults[] = [
                'type' => 'success',
                'message' => "✅ 成功创建 test.txt 文件 ($result 字节)"
            ];
        } else {
            $testResults[] = [
                'type' => 'error', 
                'message' => "❌ 无法创建 test.txt 文件"
            ];
        }
    } catch (Exception $e) {
        $testResults[] = [
            'type' => 'error',
            'message' => "❌ 创建文件异常: " . $e->getMessage()
        ];
    }
    
    // 测试2: 在server文件夹创建测试文件
    $serverTestFile = __DIR__ . '/../server/server-write-test.txt';
    try {
        $content = "SERVER WRITE TEST\n";
        $content .= "创建时间: " . date('Y-m-d H:i:s') . "\n";
        
        $result = file_put_contents($serverTestFile, $content);
        if ($result !== false) {
            $testResults[] = [
                'type' => 'success',
                'message' => "✅ 成功在server文件夹创建测试文件 ($result 字节)"
            ];
        } else {
            $testResults[] = [
                'type' => 'error',
                'message' => "❌ 无法在server文件夹创建文件"
            ];
        }
    } catch (Exception $e) {
        $testResults[] = [
            'type' => 'error',
            'message' => "❌ 在server文件夹创建文件异常: " . $e->getMessage()
        ];
    }
    
    // 测试3: 测试数据库文件权限
    $dbFile = __DIR__ . '/../server/user_system.db3';
    if (file_exists($dbFile)) {
        $testResults[] = [
            'type' => 'info',
            'message' => "📁 数据库文件存在: " . realpath($dbFile)
        ];
        
        $testResults[] = [
            'type' => is_readable($dbFile) ? 'success' : 'error',
            'message' => (is_readable($dbFile) ? "✅" : "❌") . " 数据库文件可读"
        ];
        
        $testResults[] = [
            'type' => is_writable($dbFile) ? 'success' : 'error', 
            'message' => (is_writable($dbFile) ? "✅" : "❌") . " 数据库文件可写"
        ];
        
        // 获取文件权限
        $perms = fileperms($dbFile);
        $permsOctal = substr(sprintf('%o', $perms), -3);
        $testResults[] = [
            'type' => 'info',
            'message' => "📋 数据库文件权限: $permsOctal"
        ];
    } else {
        $testResults[] = [
            'type' => 'error',
            'message' => "❌ 数据库文件不存在"
        ];
    }
}

// 处理文件删除请求
if (isset($_POST['delete_files'])) {
    $deleteResults = [];
    
    $filesToDelete = [
        __DIR__ . '/test.txt',
        __DIR__ . '/../server/server-write-test.txt'
    ];
    
    foreach ($filesToDelete as $file) {
        if (file_exists($file)) {
            if (unlink($file)) {
                $deleteResults[] = [
                    'type' => 'success',
                    'message' => "✅ 成功删除: " . basename($file)
                ];
            } else {
                $deleteResults[] = [
                    'type' => 'error',
                    'message' => "❌ 无法删除: " . basename($file)
                ];
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器写权限测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 900px;
            margin: 20px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2em;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 5px solid;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .system-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }
        .file-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .file-item {
            padding: 8px;
            margin: 5px 0;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #007bff;
        }
        .exists {
            border-left-color: #28a745;
        }
        .not-exists {
            border-left-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 服务器写权限测试工具</h1>
        
        <!-- 系统信息 -->
        <div class="system-info">
            <h3>📋 系统信息</h3>
            <p><strong>PHP版本:</strong> <?php echo PHP_VERSION; ?></p>
            <p><strong>服务器软件:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
            <p><strong>操作系统:</strong> <?php echo PHP_OS; ?></p>
            <p><strong>当前用户:</strong> <?php echo get_current_user(); ?></p>
            <p><strong>当前目录:</strong> <?php echo __DIR__; ?></p>
            <p><strong>脚本执行时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>

        <!-- 目录权限检查 -->
        <div class="system-info">
            <h3>📁 目录权限检查</h3>
            <?php
            $directories = [
                'test目录' => __DIR__,
                'server目录' => __DIR__ . '/../server',
                '根目录' => __DIR__ . '/..'
            ];
            
            foreach ($directories as $name => $path) {
                $realPath = realpath($path);
                $readable = is_readable($path);
                $writable = is_writable($path);
                $perms = file_exists($path) ? substr(sprintf('%o', fileperms($path)), -3) : 'N/A';
                
                echo "<div class='file-item " . ($writable ? 'exists' : 'not-exists') . "'>";
                echo "<strong>$name:</strong> $realPath<br>";
                echo "权限: $perms | 可读: " . ($readable ? '✅' : '❌') . " | 可写: " . ($writable ? '✅' : '❌');
                echo "</div>";
            }
            ?>
        </div>

        <!-- 测试按钮 -->
        <div style="text-align: center; margin: 30px 0;">
            <form method="post" style="display: inline;">
                <button type="submit" name="create_file" class="btn btn-success">
                    📝 创建测试文件
                </button>
            </form>
            
            <form method="post" style="display: inline;">
                <button type="submit" name="delete_files" class="btn btn-danger" 
                        onclick="return confirm('确定要删除测试文件吗？')">
                    🗑️ 删除测试文件
                </button>
            </form>
        </div>

        <!-- 显示测试结果 -->
        <?php if (isset($testResults)): ?>
            <h2>📊 文件创建测试结果</h2>
            <?php foreach ($testResults as $result): ?>
                <div class="status <?php echo $result['type']; ?>">
                    <?php echo $result['message']; ?>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>

        <?php if (isset($deleteResults)): ?>
            <h2>🗑️ 文件删除测试结果</h2>
            <?php foreach ($deleteResults as $result): ?>
                <div class="status <?php echo $result['type']; ?>">
                    <?php echo $result['message']; ?>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>

        <!-- 当前文件状态 -->
        <div class="file-list">
            <h3>📄 当前测试文件状态</h3>
            <?php
            $testFiles = [
                'test.txt' => __DIR__ . '/test.txt',
                'server-write-test.txt' => __DIR__ . '/../server/server-write-test.txt',
                'user_system.db3' => __DIR__ . '/../server/user_system.db3'
            ];
            
            foreach ($testFiles as $name => $path) {
                $exists = file_exists($path);
                $size = $exists ? filesize($path) : 0;
                $modified = $exists ? date('Y-m-d H:i:s', filemtime($path)) : 'N/A';
                
                echo "<div class='file-item " . ($exists ? 'exists' : 'not-exists') . "'>";
                echo "<strong>$name:</strong> " . ($exists ? '✅ 存在' : '❌ 不存在');
                if ($exists) {
                    echo " | 大小: {$size}字节 | 修改时间: $modified";
                }
                echo "</div>";
            }
            ?>
        </div>

        <!-- 解决方案建议 -->
        <div class="system-info">
            <h3>💡 权限问题解决方案</h3>
            <p><strong>如果测试失败，请尝试以下解决方案：</strong></p>
            
            <h4>Windows + IIS:</h4>
            <ol>
                <li>右键点击项目文件夹 → 属性 → 安全</li>
                <li>添加 "IIS_IUSRS" 用户，给予"完全控制"权限</li>
                <li>添加 "IUSR" 用户，给予"完全控制"权限</li>
                <li>确保 "Users" 组有"修改"权限</li>
            </ol>
            
            <h4>Windows + Apache:</h4>
            <ol>
                <li>确保Apache服务以管理员权限运行</li>
                <li>或者给项目文件夹添加 "Everyone" 的写权限</li>
            </ol>
            
            <h4>Linux:</h4>
            <div style="background: #f1f1f1; padding: 10px; border-radius: 5px; font-family: monospace;">
                chmod 755 /path/to/project/<br>
                chmod 666 /path/to/project/server/user_system.db3<br>
                chown -R www-data:www-data /path/to/project/
            </div>
        </div>
    </div>
</body>
</html>
