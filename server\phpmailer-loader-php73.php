<?php
/**
 * PHP 7.3兼容的PHPMailer加载器
 * 绕过Composer的版本检查，直接加载PHPMailer
 */

// 检查PHP版本
if (PHP_VERSION_ID < 70300) {
    throw new Exception('需要PHP 7.3.0或更高版本，当前版本: ' . PHP_VERSION);
}

// 直接加载PHPMailer类文件，绕过Composer检查
$phpmailerPath = __DIR__ . '/../vendor/phpmailer/phpmailer/src';

if (!is_dir($phpmailerPath)) {
    throw new Exception("PHPMailer目录不存在: $phpmailerPath");
}

// 手动加载PHPMailer核心文件
require_once $phpmailerPath . '/Exception.php';
require_once $phpmailerPath . '/PHPMailer.php';
require_once $phpmailerPath . '/SMTP.php';

// 验证类是否加载成功
if (!class_exists('<PERSON>HPMail<PERSON>\\PHPMailer\\PHPMailer')) {
    throw new Exception('PHPMailer类加载失败');
}

/**
 * PHP 7.3兼容的邮件发送器
 */
class PHP73EmailSender
{
    private $mail;
    private $config;
    
    public function __construct()
    {
        // 邮件配置
        $this->config = [
            'smtp_host' => 'smtp.163.com',
            'smtp_port' => 465,
            'smtp_secure' => 'ssl',
            'smtp_username' => '<EMAIL>',
            'smtp_password' => 'BJXfKJw32HgDSMSg',
            'from_email' => '<EMAIL>',
            'from_name' => '倒计时系统',
            'debug' => 0
        ];
        
        $this->mail = new PHPMailer\PHPMailer\PHPMailer(true);
        $this->setupSMTP();
    }
    
    private function setupSMTP()
    {
        try {
            $this->mail->isSMTP();
            $this->mail->Host = $this->config['smtp_host'];
            $this->mail->SMTPAuth = true;
            $this->mail->Username = $this->config['smtp_username'];
            $this->mail->Password = $this->config['smtp_password'];
            $this->mail->SMTPSecure = $this->config['smtp_secure'];
            $this->mail->Port = $this->config['smtp_port'];
            $this->mail->CharSet = 'UTF-8';
            $this->mail->SMTPDebug = $this->config['debug'];
        } catch (Exception $e) {
            throw new Exception("SMTP配置失败: {$this->mail->ErrorInfo}");
        }
    }
    
    /**
     * 发送邮件
     */
    public function sendEmail($to, $subject, $body, $isHtml = true)
    {
        try {
            $this->mail->setFrom($this->config['from_email'], $this->config['from_name']);
            $this->mail->addAddress($to);
            $this->mail->isHTML($isHtml);
            $this->mail->Subject = $subject;
            $this->mail->Body = $body;
            
            $result = $this->mail->send();
            $this->mail->clearAddresses();
            
            return [
                'success' => true,
                'message' => '邮件发送成功'
            ];
            
        } catch (Exception $e) {
            $this->mail->clearAddresses();
            return [
                'success' => false,
                'message' => "邮件发送失败: {$this->mail->ErrorInfo}",
                'debug' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 发送密码重置邮件
     */
    public function sendPasswordResetEmail($to, $username, $token)
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $resetLink = $protocol . "://" . $_SERVER['HTTP_HOST'] . "/reset-password.html?token=" . $token;
        
        $subject = "密码重置 - 倒计时系统";
        
        $body = "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>密码重置</title>
        </head>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h2 style='color: #4CAF50;'>🔐 密码重置请求</h2>
                
                <p>亲爱的 <strong>{$username}</strong>，</p>
                
                <p>我们收到了您的密码重置请求。请点击下面的链接来重置您的密码：</p>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <a href='{$resetLink}' 
                       style='background-color: #4CAF50; color: white; padding: 12px 30px; 
                              text-decoration: none; border-radius: 5px; display: inline-block;'>
                        重置密码
                    </a>
                </div>
                
                <p>或者复制以下链接到浏览器地址栏：</p>
                <p style='background-color: #f5f5f5; padding: 10px; border-radius: 3px; word-break: break-all;'>
                    {$resetLink}
                </p>
                
                <div style='margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;'>
                    <p style='color: #666; font-size: 14px;'>
                        <strong>安全提示：</strong><br>
                        • 此链接将在24小时后失效<br>
                        • 如果您没有请求密码重置，请忽略此邮件<br>
                        • 请勿将此链接分享给他人
                    </p>
                </div>
                
                <div style='margin-top: 20px; text-align: center; color: #999; font-size: 12px;'>
                    <p>此邮件由倒计时系统自动发送，请勿回复。</p>
                    <p>发送时间：" . date('Y-m-d H:i:s') . "</p>
                </div>
            </div>
        </body>
        </html>
        ";
        
        return $this->sendEmail($to, $subject, $body, true);
    }
    
    /**
     * 测试连接
     */
    public function testConnection()
    {
        try {
            $originalDebug = $this->mail->SMTPDebug;
            $this->mail->SMTPDebug = 2;
            
            $this->mail->setFrom($this->config['from_email'], $this->config['from_name']);
            $this->mail->addAddress($this->config['from_email']);
            $this->mail->isHTML(true);
            $this->mail->Subject = 'PHP 7.3 SMTP连接测试';
            $this->mail->Body = '这是PHP 7.3兼容版本的SMTP连接测试邮件，发送时间：' . date('Y-m-d H:i:s');
            
            $result = $this->mail->send();
            
            $this->mail->SMTPDebug = $originalDebug;
            $this->mail->clearAddresses();
            
            return [
                'success' => true,
                'message' => 'PHP 7.3 SMTP连接测试成功'
            ];
            
        } catch (Exception $e) {
            $this->mail->SMTPDebug = $originalDebug;
            $this->mail->clearAddresses();
            
            return [
                'success' => false,
                'message' => "SMTP连接测试失败: {$this->mail->ErrorInfo}",
                'debug' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取PHPMailer版本信息
     */
    public function getVersion()
    {
        return [
            'phpmailer_version' => $this->mail::VERSION,
            'php_version' => PHP_VERSION,
            'compatible' => true
        ];
    }
}

/**
 * 快速发送邮件函数
 */
function sendEmailPHP73($to, $subject, $body, $isHtml = true) {
    $sender = new PHP73EmailSender();
    return $sender->sendEmail($to, $subject, $body, $isHtml);
}
?>
