<?php
// 创建默认图片文件

// 创建images目录
if (!file_exists('../images')) {
    mkdir('../images', 0777, true);
}

// 创建默认logo.png (200x80像素，蓝色背景，白色文字)
$logoWidth = 200;
$logoHeight = 80;
$logoImage = imagecreate($logoWidth, $logoHeight);

// 设置颜色
$logoBlue = imagecolorallocate($logoImage, 0, 100, 200);
$logoWhite = imagecolorallocate($logoImage, 255, 255, 255);

// 填充背景
imagefill($logoImage, 0, 0, $logoBlue);

// 添加文字
$logoText = "LOGO";
imagestring($logoImage, 5, 70, 30, $logoText, $logoWhite);

// 保存logo图片
imagepng($logoImage, '../images/logo.png');
imagedestroy($logoImage);

// 创建默认upload1.png (300x200像素，灰色背景，黑色文字)
$carWidth = 300;
$carHeight = 200;
$carImage = imagecreate($carWidth, $carHeight);

// 设置颜色
$carGray = imagecolorallocate($carImage, 200, 200, 200);
$carBlack = imagecolorallocate($carImage, 0, 0, 0);

// 填充背景
imagefill($carImage, 0, 0, $carGray);

// 添加文字
$carText = "CAR IMAGE";
imagestring($carImage, 5, 100, 90, $carText, $carBlack);

// 保存汽车图片
imagepng($carImage, '../images/upload1.png');
imagedestroy($carImage);

echo "默认图片创建成功！\n";
echo "- images/logo.png (200x80)\n";
echo "- images/upload1.png (300x200)\n";
?>
