<?php
/**
 * 激活码数据不一致修复工具
 * 专门修复 is_used=1 但 bound_user_id=NULL 的问题
 */

header('Content-Type: text/html; charset=utf-8');

$dbPath = '../server/user_system.db3';

function fixActivationCodeInconsistency($code = null) {
    global $dbPath;
    
    $result = [
        'success' => false,
        'message' => '',
        'fixed_codes' => [],
        'analysis' => []
    ];
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 如果指定了激活码，只修复该激活码
        if ($code) {
            $result = fixSpecificCode($pdo, $code);
        } else {
            $result = fixAllInconsistentCodes($pdo);
        }
        
    } catch (Exception $e) {
        $result['message'] = "修复失败: " . $e->getMessage();
    }
    
    return $result;
}

function fixSpecificCode($pdo, $code) {
    $result = [
        'success' => false,
        'message' => '',
        'fixed_codes' => [],
        'analysis' => []
    ];
    
    // 查询激活码信息
    $stmt = $pdo->prepare("
        SELECT id, code, is_used, bound_user_id, status, created_at, expires_at
        FROM activation_codes 
        WHERE code = ?
    ");
    $stmt->execute([$code]);
    $codeInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$codeInfo) {
        $result['message'] = "激活码 '$code' 不存在";
        return $result;
    }
    
    $result['analysis']['before'] = $codeInfo;
    
    // 检查是否存在不一致 (使用严格类型检查)
    if ((int)$codeInfo['is_used'] === 1 && $codeInfo['bound_user_id'] === null) {
        // 开始修复
        $pdo->beginTransaction();
        
        try {
            // 修复方案：重置激活码为未使用状态
            $stmt = $pdo->prepare("
                UPDATE activation_codes 
                SET is_used = 0, status = 'active', used_at = NULL
                WHERE id = ?
            ");
            $stmt->execute([$codeInfo['id']]);
            
            // 验证修复结果
            $stmt = $pdo->prepare("
                SELECT id, code, is_used, bound_user_id, status, used_at
                FROM activation_codes 
                WHERE id = ?
            ");
            $stmt->execute([$codeInfo['id']]);
            $fixedInfo = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $pdo->commit();
            
            $result['success'] = true;
            $result['message'] = "激活码 '$code' 修复成功！已重置为可用状态。";
            $result['fixed_codes'][] = $code;
            $result['analysis']['after'] = $fixedInfo;
            
        } catch (Exception $e) {
            $pdo->rollBack();
            throw $e;
        }
        
    } else if ((int)$codeInfo['is_used'] === 0) {
        $result['success'] = true;
        $result['message'] = "激活码 '$code' 状态正常，无需修复。";
    } else if ((int)$codeInfo['is_used'] === 1 && $codeInfo['bound_user_id'] !== null) {
        $result['success'] = true;
        $result['message'] = "激活码 '$code' 已正常绑定到用户 ID: {$codeInfo['bound_user_id']}，无需修复。";
    }
    
    return $result;
}

function fixAllInconsistentCodes($pdo) {
    $result = [
        'success' => false,
        'message' => '',
        'fixed_codes' => [],
        'analysis' => []
    ];
    
    // 查找所有不一致的激活码
    $stmt = $pdo->query("
        SELECT id, code, is_used, bound_user_id, status, created_at, expires_at
        FROM activation_codes 
        WHERE is_used = 1 AND bound_user_id IS NULL
        ORDER BY created_at DESC
    ");
    
    $inconsistentCodes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($inconsistentCodes)) {
        $result['success'] = true;
        $result['message'] = "没有发现数据不一致的激活码。";
        return $result;
    }
    
    $result['analysis']['found_inconsistent'] = count($inconsistentCodes);
    
    // 批量修复
    $pdo->beginTransaction();
    
    try {
        foreach ($inconsistentCodes as $codeInfo) {
            // 重置为未使用状态
            $stmt = $pdo->prepare("
                UPDATE activation_codes 
                SET is_used = 0, status = 'active', used_at = NULL
                WHERE id = ?
            ");
            $stmt->execute([$codeInfo['id']]);
            
            $result['fixed_codes'][] = $codeInfo['code'];
        }
        
        $pdo->commit();
        
        $result['success'] = true;
        $result['message'] = "成功修复 " . count($result['fixed_codes']) . " 个不一致的激活码。";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
    
    return $result;
}

function getInconsistentCodes() {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->query("
            SELECT code, is_used, bound_user_id, status, created_at, expires_at
            FROM activation_codes 
            WHERE is_used = 1 AND bound_user_id IS NULL
            ORDER BY created_at DESC
        ");
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        return [];
    }
}

// 处理修复请求
$fixResult = null;
if (isset($_POST['action'])) {
    if ($_POST['action'] === 'fix_specific' && !empty($_POST['code'])) {
        $fixResult = fixActivationCodeInconsistency($_POST['code']);
    } elseif ($_POST['action'] === 'fix_all') {
        $fixResult = fixActivationCodeInconsistency();
    }
}

$inconsistentCodes = getInconsistentCodes();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>激活码数据不一致修复工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
        .warning-box { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; }
        .error-box { background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .form { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .form input, .form button { padding: 8px; margin: 5px; }
        .form button { background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .form button:hover { background: #0056b3; }
        .form button.danger { background: #dc3545; }
        .form button.danger:hover { background: #c82333; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔧 激活码数据不一致修复工具</h1>
    <p><strong>修复时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <?php if ($fixResult): ?>
        <div class="section">
            <h2>🔍 修复结果</h2>
            
            <div class="<?php echo $fixResult['success'] ? 'highlight' : 'error-box'; ?>">
                <h3><?php echo $fixResult['success'] ? '✅ 修复成功' : '❌ 修复失败'; ?></h3>
                <p><strong>结果:</strong> <?php echo htmlspecialchars($fixResult['message']); ?></p>
                
                <?php if (!empty($fixResult['fixed_codes'])): ?>
                    <p><strong>已修复的激活码:</strong></p>
                    <ul>
                        <?php foreach ($fixResult['fixed_codes'] as $code): ?>
                            <li><code><?php echo htmlspecialchars($code); ?></code></li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
            
            <?php if (!empty($fixResult['analysis'])): ?>
                <h3>🔧 修复详情:</h3>
                <pre><?php echo htmlspecialchars(json_encode($fixResult['analysis'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
            <?php endif; ?>
        </div>
    <?php endif; ?>
    
    <div class="section">
        <h2>🚨 发现的数据不一致问题</h2>
        
        <?php if (empty($inconsistentCodes)): ?>
            <div class="highlight">
                <p class="success">✅ 没有发现数据不一致的激活码！</p>
            </div>
        <?php else: ?>
            <div class="warning-box">
                <p class="warning">⚠️ 发现 <?php echo count($inconsistentCodes); ?> 个数据不一致的激活码</p>
                <p><strong>问题描述:</strong> 这些激活码被标记为已使用 (is_used=1)，但没有绑定到任何用户 (bound_user_id=NULL)</p>
            </div>
            
            <table>
                <tr>
                    <th>激活码</th>
                    <th>is_used</th>
                    <th>bound_user_id</th>
                    <th>status</th>
                    <th>创建时间</th>
                    <th>过期时间</th>
                </tr>
                <?php foreach ($inconsistentCodes as $code): ?>
                    <tr>
                        <td><code><?php echo htmlspecialchars($code['code']); ?></code></td>
                        <td><?php echo $code['is_used'] ? '✅ 是' : '❌ 否'; ?></td>
                        <td><?php echo $code['bound_user_id'] ?? '❌ NULL'; ?></td>
                        <td><?php echo htmlspecialchars($code['status']); ?></td>
                        <td><?php echo htmlspecialchars($code['created_at']); ?></td>
                        <td><?php echo htmlspecialchars($code['expires_at']); ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        <?php endif; ?>
    </div>
    
    <div class="section">
        <h2>🔧 修复操作</h2>
        
        <div class="form">
            <h3>修复特定激活码</h3>
            <form method="POST">
                <input type="hidden" name="action" value="fix_specific">
                <label>激活码:</label><br>
                <input type="text" name="code" value="N3WK7-7YJW7-JHXB4-NWWEJ" 
                       placeholder="XXXXX-XXXXX-XXXXX-XXXXX" required>
                <button type="submit">修复此激活码</button>
            </form>
        </div>
        
        <?php if (!empty($inconsistentCodes)): ?>
            <div class="form">
                <h3>批量修复所有不一致的激活码</h3>
                <form method="POST" onsubmit="return confirm('确定要修复所有 <?php echo count($inconsistentCodes); ?> 个不一致的激活码吗？');">
                    <input type="hidden" name="action" value="fix_all">
                    <p>将修复 <?php echo count($inconsistentCodes); ?> 个数据不一致的激活码</p>
                    <button type="submit" class="danger">批量修复所有</button>
                </form>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="section">
        <h2>💡 修复说明</h2>
        
        <h3>修复策略:</h3>
        <ul>
            <li><strong>重置为未使用状态:</strong> 将 is_used 设为 0</li>
            <li><strong>恢复活跃状态:</strong> 将 status 设为 'active'</li>
            <li><strong>清除使用时间:</strong> 将 used_at 设为 NULL</li>
            <li><strong>保持其他信息:</strong> 创建时间、过期时间等保持不变</li>
        </ul>
        
        <h3>修复后效果:</h3>
        <ul>
            <li>✅ 激活码可以正常绑定到用户</li>
            <li>✅ 不再出现"激活码已被其他用户使用"错误</li>
            <li>✅ 数据库状态恢复一致性</li>
        </ul>
        
        <h3>安全性:</h3>
        <ul>
            <li>✅ 使用数据库事务，确保操作原子性</li>
            <li>✅ 只修复确实存在问题的激活码</li>
            <li>✅ 不影响正常绑定的激活码</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>🔗 相关工具</h2>
        <ul>
            <li><a href="activation-code-diagnosis.php">激活码状态诊断</a></li>
            <li><a href="activation-binding-test.php">激活码绑定测试</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
