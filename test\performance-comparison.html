<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Android TV性能对比测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .version-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .version-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .performance-metrics {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .metric-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .metric-value {
            font-weight: bold;
        }
        .good { color: #28a745; }
        .warning { color: #ffc107; }
        .danger { color: #dc3545; }
        
        .test-button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        
        .optimization-list {
            background: #e7f3ff;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin: 15px 0;
        }
        .optimization-list h4 {
            margin-top: 0;
            color: #0056b3;
        }
        .optimization-list ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .optimization-list li {
            margin: 5px 0;
        }
        
        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .performance-monitor {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .monitor-display {
            font-family: 'Courier New', monospace;
            background: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            min-height: 100px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🚀 Android TV倒计时性能优化对比</h1>
    
    <div class="comparison-container">
        <!-- 原版本 -->
        <div class="version-card">
            <h2 class="version-title">📊 原版本 (index.php)</h2>
            
            <div class="performance-metrics">
                <h4>性能指标</h4>
                <div class="metric-item">
                    <span>更新频率:</span>
                    <span class="metric-value danger">10ms (100fps)</span>
                </div>
                <div class="metric-item">
                    <span>DOM更新:</span>
                    <span class="metric-value danger">每次5个元素</span>
                </div>
                <div class="metric-item">
                    <span>CSS动画:</span>
                    <span class="metric-value warning">4个同时运行</span>
                </div>
                <div class="metric-item">
                    <span>字符串操作:</span>
                    <span class="metric-value danger">每次5个padStart</span>
                </div>
                <div class="metric-item">
                    <span>GPU负载:</span>
                    <span class="metric-value danger">高 (多动画)</span>
                </div>
            </div>
            
            <button class="test-button" onclick="openOriginal()">🔗 测试原版本</button>
            <button class="test-button" onclick="startMonitoring('original')">📈 性能监控</button>
        </div>
        
        <!-- 优化版本 -->
        <div class="version-card">
            <h2 class="version-title">⚡ 优化版本 (index-tv-optimized.php)</h2>
            
            <div class="performance-metrics">
                <h4>性能指标</h4>
                <div class="metric-item">
                    <span>更新频率:</span>
                    <span class="metric-value good">50ms (20fps)</span>
                </div>
                <div class="metric-item">
                    <span>DOM更新:</span>
                    <span class="metric-value good">仅变化元素</span>
                </div>
                <div class="metric-item">
                    <span>CSS动画:</span>
                    <span class="metric-value good">1个简化动画</span>
                </div>
                <div class="metric-item">
                    <span>字符串操作:</span>
                    <span class="metric-value good">预缓存填充</span>
                </div>
                <div class="metric-item">
                    <span>GPU负载:</span>
                    <span class="metric-value good">低 (静态渲染)</span>
                </div>
            </div>
            
            <button class="test-button" onclick="openOptimized()">🔗 测试优化版本</button>
            <button class="test-button" onclick="startMonitoring('optimized')">📈 性能监控</button>
        </div>
    </div>
    
    <!-- 优化详情 -->
    <div class="optimization-list">
        <h4>🎯 Android TV专项优化</h4>
        <ul>
            <li><strong>降低更新频率</strong>: 从10ms改为50ms，减少80%的计算负载</li>
            <li><strong>智能DOM更新</strong>: 只更新发生变化的元素，避免无效重绘</li>
            <li><strong>字符串缓存</strong>: 预计算padStart结果，消除重复计算</li>
            <li><strong>requestAnimationFrame</strong>: 使用浏览器优化的动画帧</li>
            <li><strong>页面可见性API</strong>: 页面不可见时暂停倒计时</li>
            <li><strong>减少CSS动画</strong>: 移除复杂动画，降低GPU负载</li>
            <li><strong>静态背景</strong>: 移除背景动画，减少重绘</li>
            <li><strong>GPU优化</strong>: 合理使用transform3d和will-change</li>
        </ul>
    </div>
    
    <!-- 性能监控 -->
    <div class="performance-monitor">
        <h4>📊 实时性能监控</h4>
        <p>点击上方的"性能监控"按钮开始监控CPU和内存使用情况</p>
        <div class="monitor-display" id="performanceLog">
            等待开始监控...
        </div>
        <button class="test-button" onclick="clearLog()">🗑️ 清空日志</button>
        <button class="test-button" onclick="exportLog()">💾 导出日志</button>
    </div>
    
    <!-- 测试建议 -->
    <div class="optimization-list">
        <h4>🧪 Android TV测试建议</h4>
        <ul>
            <li><strong>测试环境</strong>: 在实际Android TV设备上测试效果最佳</li>
            <li><strong>观察指标</strong>: 注意秒和毫秒的流畅度，CPU温度变化</li>
            <li><strong>长时间测试</strong>: 运行30分钟以上观察性能稳定性</li>
            <li><strong>内存监控</strong>: 检查是否有内存泄漏</li>
            <li><strong>电池消耗</strong>: 对比两个版本的电池消耗差异</li>
        </ul>
    </div>

    <script>
        let performanceMonitor = null;
        let monitoringActive = false;
        
        function openOriginal() {
            window.open('../index.php', '_blank');
        }
        
        function openOptimized() {
            window.open('../index-tv-optimized.php', '_blank');
        }
        
        function startMonitoring(version) {
            if (monitoringActive) {
                stopMonitoring();
                return;
            }
            
            monitoringActive = true;
            const logElement = document.getElementById('performanceLog');
            logElement.innerHTML = `开始监控 ${version} 版本性能...\n`;
            
            let startTime = performance.now();
            let frameCount = 0;
            let lastTime = startTime;
            
            function monitor() {
                if (!monitoringActive) return;
                
                const currentTime = performance.now();
                frameCount++;
                
                // 每秒更新一次统计
                if (currentTime - lastTime >= 1000) {
                    const fps = Math.round(frameCount * 1000 / (currentTime - lastTime));
                    const memoryInfo = performance.memory ? {
                        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
                    } : null;
                    
                    const timestamp = new Date().toLocaleTimeString();
                    let logEntry = `[${timestamp}] FPS: ${fps}`;
                    
                    if (memoryInfo) {
                        logEntry += ` | 内存: ${memoryInfo.used}MB/${memoryInfo.total}MB (限制:${memoryInfo.limit}MB)`;
                    }
                    
                    logEntry += '\n';
                    logElement.innerHTML += logEntry;
                    logElement.scrollTop = logElement.scrollHeight;
                    
                    frameCount = 0;
                    lastTime = currentTime;
                }
                
                requestAnimationFrame(monitor);
            }
            
            monitor();
            
            // 更新按钮文本
            event.target.textContent = '⏹️ 停止监控';
        }
        
        function stopMonitoring() {
            monitoringActive = false;
            // 重置所有监控按钮
            document.querySelectorAll('.test-button').forEach(btn => {
                if (btn.textContent.includes('停止监控')) {
                    btn.textContent = '📈 性能监控';
                }
            });
        }
        
        function clearLog() {
            document.getElementById('performanceLog').innerHTML = '日志已清空...\n';
        }
        
        function exportLog() {
            const logContent = document.getElementById('performanceLog').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `performance-log-${new Date().toISOString().slice(0,19)}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        // 页面卸载时停止监控
        window.addEventListener('beforeunload', stopMonitoring);
    </script>
</body>
</html>
