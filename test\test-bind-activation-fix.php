<?php
/**
 * 测试激活码绑定修复效果
 */

header('Content-Type: text/html; charset=utf-8');

$dbPath = '../server/user_system.db3';

function createInconsistentTestCode() {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 生成测试激活码
        $testCode = 'TEST1-TEST2-TEST3-TEST4';
        
        // 删除可能存在的测试激活码
        $stmt = $pdo->prepare("DELETE FROM activation_codes WHERE code = ?");
        $stmt->execute([$testCode]);
        
        // 创建一个数据不一致的测试激活码
        $stmt = $pdo->prepare("
            INSERT INTO activation_codes (code, is_used, bound_user_id, status, created_at, expires_at)
            VALUES (?, 1, NULL, 'active', datetime('now'), datetime('now', '+30 days'))
        ");
        $stmt->execute([$testCode]);
        
        return $testCode;
        
    } catch (Exception $e) {
        return false;
    }
}

function testBindingAPI($activationCode, $sessionToken) {
    $url = 'http://promo.xxgogo.ggff.net:8866/api/bind-activation.php';
    
    $data = [
        'activation_code' => $activationCode,
        'session_token' => $sessionToken
    ];
    
    $options = [
        'http' => [
            'header' => "Content-type: application/json\r\n",
            'method' => 'POST',
            'content' => json_encode($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    return json_decode($result, true);
}

function getValidSessionToken() {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->query("
            SELECT session_token FROM user_sessions 
            WHERE is_active = 1 AND expires_at > datetime('now')
            LIMIT 1
        ");
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['session_token'] : null;
        
    } catch (Exception $e) {
        return null;
    }
}

// 执行测试
$testResult = null;
if (isset($_POST['action']) && $_POST['action'] === 'test') {
    $testCode = createInconsistentTestCode();
    $sessionToken = getValidSessionToken();
    
    if ($testCode && $sessionToken) {
        $testResult = testBindingAPI($testCode, $sessionToken);
    } else {
        $testResult = [
            'success' => false,
            'message' => '无法创建测试环境：' . ($testCode ? '无有效会话' : '无法创建测试激活码')
        ];
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>激活码绑定修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
        .warning-box { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; }
        .error-box { background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0; }
        .form { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .form button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .form button:hover { background: #0056b3; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        code { background: #f5f5f5; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🧪 激活码绑定修复测试</h1>
    <p><strong>测试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="section">
        <h2>🔧 修复内容说明</h2>
        
        <h3>修复的问题:</h3>
        <div class="warning-box">
            <p><strong>原问题:</strong> 当激活码存在数据不一致（is_used=1 但 bound_user_id=NULL）时，系统会错误地提示"激活码已被其他用户使用"</p>
        </div>
        
        <h3>修复方案:</h3>
        <div class="highlight">
            <ol>
                <li><strong>数据一致性检查:</strong> 检测 is_used=1 但 bound_user_id=NULL 的情况</li>
                <li><strong>友好错误提示:</strong> 提供更准确的错误信息</li>
                <li><strong>类型安全比较:</strong> 使用严格类型比较避免类型转换问题</li>
                <li><strong>错误日志记录:</strong> 记录数据不一致问题便于排查</li>
            </ol>
        </div>
        
        <h3>修复后的代码逻辑:</h3>
        <pre><code>if ($codeInfo['is_used']) {
    // 检查数据一致性
    if ($codeInfo['bound_user_id'] === null || $codeInfo['bound_user_id'] === '') {
        error_log("激活码数据不一致: code={$activationCode}, is_used=1, bound_user_id=NULL");
        throw new Exception('激活码状态异常，请联系管理员或稍后重试');
    }
    
    // 使用严格比较
    if ((int)$codeInfo['bound_user_id'] === (int)$session['user_id']) {
        throw new Exception('您已经绑定过这个激活码了');
    } else {
        throw new Exception('激活码已被其他用户使用');
    }
}</code></pre>
    </div>
    
    <div class="section">
        <h2>🧪 功能测试</h2>
        
        <div class="form">
            <h3>测试数据不一致场景</h3>
            <p>此测试将创建一个数据不一致的激活码（is_used=1, bound_user_id=NULL），然后测试绑定API的响应。</p>
            <form method="POST">
                <input type="hidden" name="action" value="test">
                <button type="submit">开始测试</button>
            </form>
        </div>
        
        <?php if ($testResult): ?>
            <div class="section">
                <h3>🔍 测试结果</h3>
                
                <div class="<?php echo $testResult['success'] ? 'highlight' : 'error-box'; ?>">
                    <h4><?php echo $testResult['success'] ? '✅ 测试通过' : '❌ 测试结果'; ?></h4>
                    <p><strong>响应消息:</strong> <?php echo htmlspecialchars($testResult['message']); ?></p>
                </div>
                
                <h4>完整响应:</h4>
                <pre><?php echo htmlspecialchars(json_encode($testResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
                
                <div class="info">
                    <h4>期望结果:</h4>
                    <p>修复后，系统应该返回更友好的错误信息："激活码状态异常，请联系管理员或稍后重试"，而不是"激活码已被其他用户使用"。</p>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="section">
        <h2>📋 测试场景说明</h2>
        
        <table style="border-collapse: collapse; width: 100%;">
            <tr style="background: #f2f2f2;">
                <th style="border: 1px solid #ddd; padding: 8px;">场景</th>
                <th style="border: 1px solid #ddd; padding: 8px;">is_used</th>
                <th style="border: 1px solid #ddd; padding: 8px;">bound_user_id</th>
                <th style="border: 1px solid #ddd; padding: 8px;">修复前错误</th>
                <th style="border: 1px solid #ddd; padding: 8px;">修复后错误</th>
            </tr>
            <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">正常未使用</td>
                <td style="border: 1px solid #ddd; padding: 8px;">0</td>
                <td style="border: 1px solid #ddd; padding: 8px;">NULL</td>
                <td style="border: 1px solid #ddd; padding: 8px;">✅ 正常绑定</td>
                <td style="border: 1px solid #ddd; padding: 8px;">✅ 正常绑定</td>
            </tr>
            <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">正常已使用</td>
                <td style="border: 1px solid #ddd; padding: 8px;">1</td>
                <td style="border: 1px solid #ddd; padding: 8px;">用户ID</td>
                <td style="border: 1px solid #ddd; padding: 8px;">❌ 已被其他用户使用</td>
                <td style="border: 1px solid #ddd; padding: 8px;">❌ 已被其他用户使用</td>
            </tr>
            <tr style="background: #fff3cd;">
                <td style="border: 1px solid #ddd; padding: 8px;"><strong>数据不一致</strong></td>
                <td style="border: 1px solid #ddd; padding: 8px;"><strong>1</strong></td>
                <td style="border: 1px solid #ddd; padding: 8px;"><strong>NULL</strong></td>
                <td style="border: 1px solid #ddd; padding: 8px;">❌ 已被其他用户使用</td>
                <td style="border: 1px solid #ddd; padding: 8px;">❌ 激活码状态异常</td>
            </tr>
        </table>
    </div>
    
    <div class="section">
        <h2>🔗 相关工具</h2>
        <ul>
            <li><a href="activation-code-diagnosis.php">激活码状态诊断</a></li>
            <li><a href="fix-activation-code-inconsistency.php">数据不一致修复工具</a></li>
            <li><a href="activation-binding-test.php">激活码绑定测试</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
