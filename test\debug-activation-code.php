<?php
/**
 * 调试激活码数据
 */

header('Content-Type: text/html; charset=utf-8');

$dbPath = '../server/user_system.db3';
$testCode = 'N3WK7-7YJW7-JHXB4-NWWEJ';

try {
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 查询激活码的原始数据
    $stmt = $pdo->prepare("
        SELECT id, code, is_used, bound_user_id, status, created_at, expires_at, used_at
        FROM activation_codes 
        WHERE code = ?
    ");
    $stmt->execute([$testCode]);
    $codeInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $codeInfo = false;
    $error = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>激活码数据调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
        .warning-box { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; }
        .error-box { background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .mono { font-family: monospace; background: #f5f5f5; padding: 2px 4px; }
    </style>
</head>
<body>
    <h1>🔍 激活码数据调试</h1>
    <p><strong>调试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    <p><strong>测试激活码:</strong> <code><?php echo $testCode; ?></code></p>
    
    <?php if (isset($error)): ?>
        <div class="error-box">
            <h3>❌ 数据库错误</h3>
            <p><?php echo htmlspecialchars($error); ?></p>
        </div>
    <?php elseif ($codeInfo): ?>
        <div class="section">
            <h2>📊 原始数据</h2>
            
            <table>
                <tr><th>字段</th><th>原始值</th><th>类型</th><th>转换后值</th><th>检查结果</th></tr>
                <tr>
                    <td>id</td>
                    <td class="mono"><?php echo var_export($codeInfo['id'], true); ?></td>
                    <td><?php echo gettype($codeInfo['id']); ?></td>
                    <td class="mono"><?php echo (int)$codeInfo['id']; ?></td>
                    <td>-</td>
                </tr>
                <tr>
                    <td>code</td>
                    <td class="mono"><?php echo var_export($codeInfo['code'], true); ?></td>
                    <td><?php echo gettype($codeInfo['code']); ?></td>
                    <td class="mono"><?php echo htmlspecialchars($codeInfo['code']); ?></td>
                    <td>-</td>
                </tr>
                <tr>
                    <td>is_used</td>
                    <td class="mono"><?php echo var_export($codeInfo['is_used'], true); ?></td>
                    <td><?php echo gettype($codeInfo['is_used']); ?></td>
                    <td class="mono"><?php echo (int)$codeInfo['is_used']; ?></td>
                    <td><?php echo ((int)$codeInfo['is_used'] === 1) ? '❌ 已使用' : '✅ 未使用'; ?></td>
                </tr>
                <tr>
                    <td>bound_user_id</td>
                    <td class="mono"><?php echo var_export($codeInfo['bound_user_id'], true); ?></td>
                    <td><?php echo gettype($codeInfo['bound_user_id']); ?></td>
                    <td class="mono"><?php echo $codeInfo['bound_user_id'] ?: 'NULL'; ?></td>
                    <td><?php echo ($codeInfo['bound_user_id'] === null) ? '✅ 未绑定' : '❌ 已绑定'; ?></td>
                </tr>
                <tr>
                    <td>status</td>
                    <td class="mono"><?php echo var_export($codeInfo['status'], true); ?></td>
                    <td><?php echo gettype($codeInfo['status']); ?></td>
                    <td class="mono"><?php echo htmlspecialchars($codeInfo['status']); ?></td>
                    <td><?php echo ($codeInfo['status'] === 'active') ? '✅ 活跃' : '❌ 非活跃'; ?></td>
                </tr>
                <tr>
                    <td>used_at</td>
                    <td class="mono"><?php echo var_export($codeInfo['used_at'], true); ?></td>
                    <td><?php echo gettype($codeInfo['used_at']); ?></td>
                    <td class="mono"><?php echo $codeInfo['used_at'] ?: 'NULL'; ?></td>
                    <td><?php echo ($codeInfo['used_at'] === null) ? '✅ 未使用' : '❌ 已使用'; ?></td>
                </tr>
            </table>
        </div>
        
        <div class="section">
            <h2>🔍 一致性分析</h2>
            
            <?php
            $isUsed = (int)$codeInfo['is_used'] === 1;
            $boundUserId = $codeInfo['bound_user_id'] !== null;
            $isActive = $codeInfo['status'] === 'active';
            $hasUsedAt = $codeInfo['used_at'] !== null;
            
            $isConsistent = true;
            $issues = [];
            
            // 检查数据一致性
            if ($isUsed && !$boundUserId) {
                $isConsistent = false;
                $issues[] = "is_used=1 但 bound_user_id=NULL";
            }
            
            if ($isUsed && !$hasUsedAt) {
                $isConsistent = false;
                $issues[] = "is_used=1 但 used_at=NULL";
            }
            
            if ($isUsed && $isActive) {
                $isConsistent = false;
                $issues[] = "is_used=1 但 status='active'";
            }
            
            if (!$isUsed && $boundUserId) {
                $isConsistent = false;
                $issues[] = "is_used=0 但 bound_user_id 不为空";
            }
            ?>
            
            <div class="<?php echo $isConsistent ? 'highlight' : 'error-box'; ?>">
                <h3><?php echo $isConsistent ? '✅ 数据一致' : '❌ 数据不一致'; ?></h3>
                
                <?php if ($isConsistent): ?>
                    <p>激活码数据状态完全一致，可以正常使用。</p>
                <?php else: ?>
                    <p><strong>发现的问题:</strong></p>
                    <ul>
                        <?php foreach ($issues as $issue): ?>
                            <li><?php echo htmlspecialchars($issue); ?></li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="section">
            <h2>🧪 类型检查测试</h2>
            
            <table>
                <tr><th>检查表达式</th><th>结果</th><th>说明</th></tr>
                <tr>
                    <td class="mono">$codeInfo['is_used']</td>
                    <td class="mono"><?php echo var_export((bool)$codeInfo['is_used'], true); ?></td>
                    <td>PHP布尔转换</td>
                </tr>
                <tr>
                    <td class="mono">$codeInfo['is_used'] == 1</td>
                    <td class="mono"><?php echo var_export($codeInfo['is_used'] == 1, true); ?></td>
                    <td>松散比较</td>
                </tr>
                <tr>
                    <td class="mono">(int)$codeInfo['is_used'] === 1</td>
                    <td class="mono"><?php echo var_export((int)$codeInfo['is_used'] === 1, true); ?></td>
                    <td>严格比较</td>
                </tr>
                <tr>
                    <td class="mono">$codeInfo['bound_user_id'] === null</td>
                    <td class="mono"><?php echo var_export($codeInfo['bound_user_id'] === null, true); ?></td>
                    <td>NULL检查</td>
                </tr>
            </table>
        </div>
        
        <div class="section">
            <h2>🔧 修复建议</h2>
            
            <?php if (!$isConsistent): ?>
                <div class="warning-box">
                    <h4>需要修复的问题:</h4>
                    <p>激活码存在数据不一致，建议执行以下SQL命令修复：</p>
                    <pre>UPDATE activation_codes 
SET is_used = 0, status = 'active', used_at = NULL 
WHERE code = '<?php echo $testCode; ?>';</pre>
                </div>
            <?php else: ?>
                <div class="highlight">
                    <h4>✅ 无需修复</h4>
                    <p>激活码数据状态正常，可以正常使用。</p>
                </div>
            <?php endif; ?>
        </div>
        
    <?php else: ?>
        <div class="error-box">
            <h3>❌ 激活码不存在</h3>
            <p>未找到激活码 <code><?php echo $testCode; ?></code></p>
        </div>
    <?php endif; ?>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
