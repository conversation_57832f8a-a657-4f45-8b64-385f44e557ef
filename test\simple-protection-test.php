<?php
/**
 * 简化的访问保护测试
 * 快速测试关键文件的保护状态
 */

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化访问保护测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
    </style>
</head>
<body>
    <h1>🛡️ 简化访问保护测试</h1>
    <p><strong>测试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="highlight">
        <h3>🎯 测试说明</h3>
        <p>这是一个简化版本的访问保护测试，通过直接尝试访问敏感文件来验证.htaccess保护是否生效。</p>
        <p><strong>测试方法:</strong> 在新窗口中打开敏感文件链接，观察是否被正确阻止。</p>
    </div>
    
    <div class="test-section">
        <h2>🔒 vendor目录保护测试</h2>
        <p>点击下面的链接，如果显示403 Forbidden或404 Not Found，说明保护生效：</p>
        
        <table>
            <tr>
                <th>敏感文件</th>
                <th>测试链接</th>
                <th>期望结果</th>
            </tr>
            <tr>
                <td>vendor/autoload.php</td>
                <td><a href="../vendor/autoload.php" target="_blank">测试访问</a></td>
                <td class="error">403 Forbidden</td>
            </tr>
            <tr>
                <td>vendor/composer/autoload_real.php</td>
                <td><a href="../vendor/composer/autoload_real.php" target="_blank">测试访问</a></td>
                <td class="error">403 Forbidden</td>
            </tr>
            <tr>
                <td>vendor/phpmailer/phpmailer/src/PHPMailer.php</td>
                <td><a href="../vendor/phpmailer/phpmailer/src/PHPMailer.php" target="_blank">测试访问</a></td>
                <td class="error">403 Forbidden</td>
            </tr>
            <tr>
                <td>vendor/phpmailer/phpmailer/src/SMTP.php</td>
                <td><a href="../vendor/phpmailer/phpmailer/src/SMTP.php" target="_blank">测试访问</a></td>
                <td class="error">403 Forbidden</td>
            </tr>
        </table>
    </div>
    
    <div class="test-section">
        <h2>🔒 server目录保护测试</h2>
        <table>
            <tr>
                <th>敏感文件</th>
                <th>测试链接</th>
                <th>期望结果</th>
            </tr>
            <tr>
                <td>server/email-config.php</td>
                <td><a href="../server/email-config.php" target="_blank">测试访问</a></td>
                <td class="error">403 Forbidden</td>
            </tr>
            <tr>
                <td>server/working-email-sender.php</td>
                <td><a href="../server/working-email-sender.php" target="_blank">测试访问</a></td>
                <td class="error">403 Forbidden</td>
            </tr>
            <tr>
                <td>server/phpmailer-loader-php73.php</td>
                <td><a href="../server/phpmailer-loader-php73.php" target="_blank">测试访问</a></td>
                <td class="error">403 Forbidden</td>
            </tr>
        </table>
    </div>
    
    <div class="test-section">
        <h2>🔒 docs目录保护测试</h2>
        <table>
            <tr>
                <th>敏感文件</th>
                <th>测试链接</th>
                <th>期望结果</th>
            </tr>
            <tr>
                <td>docs/records.md</td>
                <td><a href="../docs/records.md" target="_blank">测试访问</a></td>
                <td class="error">403 Forbidden</td>
            </tr>
            <tr>
                <td>docs/说明.txt</td>
                <td><a href="../docs/说明.txt" target="_blank">测试访问</a></td>
                <td class="error">403 Forbidden</td>
            </tr>
        </table>
    </div>
    
    <div class="test-section">
        <h2>🔒 配置文件保护测试</h2>
        <table>
            <tr>
                <th>敏感文件</th>
                <th>测试链接</th>
                <th>期望结果</th>
            </tr>
            <tr>
                <td>.htaccess</td>
                <td><a href="../.htaccess" target="_blank">测试访问</a></td>
                <td class="error">403 Forbidden</td>
            </tr>
            <tr>
                <td>vendor/.htaccess</td>
                <td><a href="../vendor/.htaccess" target="_blank">测试访问</a></td>
                <td class="error">403 Forbidden</td>
            </tr>
        </table>
    </div>
    
    <div class="test-section">
        <h2>✅ 允许访问的文件测试</h2>
        <p>这些文件应该可以正常访问：</p>
        <table>
            <tr>
                <th>公共文件</th>
                <th>测试链接</th>
                <th>期望结果</th>
            </tr>
            <tr>
                <td>index.html</td>
                <td><a href="../index.html" target="_blank">测试访问</a></td>
                <td class="success">200 OK</td>
            </tr>
            <tr>
                <td>new-lock.html</td>
                <td><a href="../new-lock.html" target="_blank">测试访问</a></td>
                <td class="success">200 OK</td>
            </tr>
            <tr>
                <td>api/forgot-password.php</td>
                <td><a href="../api/forgot-password.php" target="_blank">测试访问</a></td>
                <td class="success">200 OK (API)</td>
            </tr>
        </table>
    </div>
    
    <div class="test-section">
        <h2>📋 .htaccess保护机制</h2>
        
        <h3>🔒 vendor目录保护 (vendor/.htaccess):</h3>
        <pre>
# 拒绝所有直接访问
Order Deny,Allow
Deny from all

# 防止目录浏览
Options -Indexes

# 禁用PHP执行
php_flag engine off
        </pre>
        
        <h3>🔒 根目录保护 (.htaccess):</h3>
        <pre>
# 完全禁止访问vendor目录
RewriteRule ^vendor/.*$ - [F,L]

# 保护敏感目录
RewriteRule ^(docs|test|server|config)/.*$ - [F,L]

# 保护配置文件
&lt;FilesMatch "\.(ini|conf|config|env|log|bak)$"&gt;
    Order Deny,Allow
    Deny from all
&lt;/FilesMatch&gt;
        </pre>
        
        <h3>🛡️ 安全效果:</h3>
        <ul>
            <li>✅ <strong>vendor目录</strong>: 完全无法访问</li>
            <li>✅ <strong>server目录</strong>: PHP文件受保护</li>
            <li>✅ <strong>docs目录</strong>: 文档文件受保护</li>
            <li>✅ <strong>配置文件</strong>: .htaccess等配置文件受保护</li>
            <li>✅ <strong>公共文件</strong>: HTML和API文件正常访问</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>💡 测试指南</h2>
        <ol>
            <li><strong>点击测试链接</strong>: 在新窗口中打开敏感文件链接</li>
            <li><strong>观察结果</strong>:
                <ul>
                    <li><span class="success">✅ 403 Forbidden</span> - 文件受.htaccess保护</li>
                    <li><span class="success">✅ 404 Not Found</span> - 文件不存在（也算安全）</li>
                    <li><span class="error">❌ 200 OK + 文件内容</span> - 安全风险，需要检查配置</li>
                </ul>
            </li>
            <li><strong>验证公共文件</strong>: 确认允许访问的文件可以正常打开</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关工具</h2>
        <ul>
            <li><a href="security-check.php">安全检查工具</a></li>
            <li><a href="final-verification.php">最终验证</a></li>
            <li><a href="../new-lock.html">返回登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
