<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原版本倒计时</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #ff4444 0%, #cc0000 50%, #990000 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
        }
        
        .title {
            font-size: 24px;
            margin-bottom: 20px;
            text-align: center;
            animation: titlePulse 2s ease-in-out infinite alternate;
        }
        
        @keyframes titlePulse {
            0% { opacity: 0.8; transform: scale(1); }
            100% { opacity: 1; transform: scale(1.05); }
        }
        
        .countdown-container {
            display: flex;
            gap: 15px;
            margin: 20px 0;
        }
        
        .countdown-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            min-width: 80px;
        }
        
        .countdown-number {
            font-size: 36px;
            font-weight: bold;
            color: #ffff00;
            text-shadow: 
                2px 2px 4px rgba(0, 0, 0, 0.7),
                0 0 15px #ffff00,
                0 0 30px #ffff00;
            animation: numberGlow 2s ease-in-out infinite alternate;
        }
        
        @keyframes numberGlow {
            0% { 
                text-shadow: 
                    2px 2px 4px rgba(0, 0, 0, 0.7),
                    0 0 15px #ffff00,
                    0 0 30px #ffff00;
            }
            100% { 
                text-shadow: 
                    2px 2px 4px rgba(0, 0, 0, 0.7),
                    0 0 25px #ffff00,
                    0 0 50px #ffff00,
                    0 0 75px #ffff00;
            }
        }
        
        .countdown-label {
            font-size: 12px;
            color: #ffff00;
            margin-top: 5px;
            font-weight: bold;
        }
        
        .performance-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            color: #ff6666;
        }
    </style>
</head>
<body>
    <div class="performance-info">
        原版本: 10ms更新 (100fps)<br>
        高GPU负载 | 频繁DOM更新
    </div>
    
    <div class="title">原版本倒计时 - 高频更新</div>
    
    <div class="countdown-container">
        <div class="countdown-item">
            <span class="countdown-number" id="days">00</span>
            <span class="countdown-label">天</span>
        </div>
        <div class="countdown-item">
            <span class="countdown-number" id="hours">00</span>
            <span class="countdown-label">小时</span>
        </div>
        <div class="countdown-item">
            <span class="countdown-number" id="minutes">00</span>
            <span class="countdown-label">分钟</span>
        </div>
        <div class="countdown-item">
            <span class="countdown-number" id="seconds">00</span>
            <span class="countdown-label">秒</span>
        </div>
        <div class="countdown-item">
            <span class="countdown-number" id="milliseconds">000</span>
            <span class="countdown-label">毫秒</span>
        </div>
    </div>

    <script>
        // 原版本倒计时 - 高频更新，无优化
        const targetDate = new Date('2025-08-01 00:00:00').getTime();
        
        function updateCountdown() {
            const now = Date.now();
            const distance = targetDate - now;
            
            if (distance < 0) {
                document.getElementById('days').textContent = '00';
                document.getElementById('hours').textContent = '00';
                document.getElementById('minutes').textContent = '00';
                document.getElementById('seconds').textContent = '00';
                document.getElementById('milliseconds').textContent = '000';
                return;
            }
            
            // 每次都重新计算和更新所有元素（无优化）
            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);
            const milliseconds = Math.floor(distance % 1000);
            
            // 每次都执行字符串操作（无缓存）
            document.getElementById('days').textContent = days.toString().padStart(2, '0');
            document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
            document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
            document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
            document.getElementById('milliseconds').textContent = milliseconds.toString().padStart(3, '0');
        }
        
        // 高频更新 - 每10ms更新一次 (100fps)
        setInterval(updateCountdown, 10);
        
        // 初始更新
        updateCountdown();
        
        console.log('🔴 原版本倒计时启动 - 10ms更新间隔');
    </script>
</body>
</html>
