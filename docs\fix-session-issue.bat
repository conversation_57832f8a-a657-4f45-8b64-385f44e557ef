@echo off
echo ========================================
echo PHP 会话问题修复脚本
echo ========================================
echo.

echo 步骤1: 创建目录结构...
if not exist "D:\error_log" mkdir "D:\error_log"
if not exist "D:\error_log\php" mkdir "D:\error_log\php"
if not exist "D:\temp" mkdir "D:\temp"
if not exist "D:\temp\php" mkdir "D:\temp\php"
if not exist "D:\temp\php\sessions" mkdir "D:\temp\php\sessions"
if not exist "D:\temp\php\uploads" mkdir "D:\temp\php\uploads"

echo 步骤2: 设置完全权限 (解决会话问题)...
echo 为 IIS_IUSRS 设置权限...
icacls "D:\error_log" /grant "IIS_IUSRS:(OI)(CI)F" /T /Q
icacls "D:\temp" /grant "IIS_IUSRS:(OI)(CI)F" /T /Q

echo 为 IUSR 设置权限...
icacls "D:\error_log" /grant "IUSR:(OI)(CI)F" /T /Q
icacls "D:\temp" /grant "IUSR:(OI)(CI)F" /T /Q

echo 为 IIS_WPG 设置权限 (Windows 2008)...
icacls "D:\error_log" /grant "IIS_WPG:(OI)(CI)F" /T /Q
icacls "D:\temp" /grant "IIS_WPG:(OI)(CI)F" /T /Q

echo 为 NETWORK SERVICE 设置权限...
icacls "D:\error_log" /grant "NETWORK SERVICE:(OI)(CI)F" /T /Q
icacls "D:\temp" /grant "NETWORK SERVICE:(OI)(CI)F" /T /Q

echo 为 Everyone 设置权限 (临时调试)...
icacls "D:\error_log" /grant "Everyone:(OI)(CI)F" /T /Q
icacls "D:\temp" /grant "Everyone:(OI)(CI)F" /T /Q

echo.
echo 步骤3: 创建测试文件...
echo test > "D:\temp\php\sessions\test.txt"
echo test > "D:\error_log\php\test.txt"

echo.
echo 步骤4: 检查目录权限...
echo 检查 D:\temp\php\sessions 权限:
icacls "D:\temp\php\sessions"
echo.
echo 检查 D:\error_log\php 权限:
icacls "D:\error_log\php"

echo.
echo 步骤5: 清理旧的会话文件...
if exist "D:\temp\php\sessions\*" del /Q "D:\temp\php\sessions\*"

echo.
echo ========================================
echo 修复完成！
echo ========================================
echo.
echo 创建的目录：
echo - D:\error_log\php\          (PHP错误日志)
echo - D:\temp\php\sessions\      (PHP会话文件)
echo - D:\temp\php\uploads\       (PHP上传临时文件)
echo.
echo 设置的权限：
echo - IIS_IUSRS: 完全控制
echo - IUSR: 完全控制  
echo - IIS_WPG: 完全控制 (Windows 2008)
echo - NETWORK SERVICE: 完全控制
echo - Everyone: 完全控制 (临时调试)
echo.
echo 下一步：
echo 1. 确保 php.ini 文件已更新
echo 2. 执行 iisreset 重启 IIS
echo 3. 访问网站测试
echo 4. 如果仍有问题，检查 PHP 错误日志
echo.
pause
