# 禁止通过Web访问server目录
# 保护数据库文件和敏感脚本

# 拒绝所有访问
Deny from all

# 或者使用Apache 2.4+语法
<RequireAll>
    Require all denied
</RequireAll>

# 禁止访问数据库文件
<Files "*.db">
    Deny from all
</Files>

<Files "*.db3">
    Deny from all
</Files>

# 禁止访问Python脚本
<Files "*.py">
    Deny from all
</Files>

# 禁止访问日志文件
<Files "*.log">
    Deny from all
</Files>

# 禁止访问SQL文件
<Files "*.sql">
    Deny from all
</Files>

# 禁止目录浏览
Options -Indexes

# 自定义错误页面
ErrorDocument 403 "Access Denied - This directory is protected"
ErrorDocument 404 "File Not Found"
