# 用户登录系统

一个基于PHP和SQLite的完整用户登录系统，支持用户注册、登录、密码找回、激活码管理等功能。

## 🚀 功能特性

### 👤 用户管理
- **用户注册**: 支持用户名、密码、邮箱注册
- **用户登录**: 安全的用户认证，支持单设备登录
- **密码找回**: 通过用户名+邮箱组合重置密码
- **会话管理**: 自动会话验证和超时处理

### 🔐 安全特性
- **验证码保护**: 所有表单都有图形验证码
- **密码加密**: 使用SHA256加密存储密码
- **会话令牌**: 安全的会话管理机制
- **单设备登录**: 新登录自动挤掉旧会话
- **网络检测**: 防止断网绕过登录验证

### 📧 邮件系统
- **密码重置**: 通过邮件发送重置链接
- **多服务商**: 支持QQ、Gmail、Outlook、163等邮箱
- **HTML模板**: 美观的邮件模板设计
- **安全连接**: TLS/SSL加密邮件传输

### 💳 激活码系统
- **激活码生成**: Python工具生成激活码
- **激活码绑定**: 用户可绑定激活码获得权限
- **有效期管理**: 支持30天有效期设置
- **重新绑定**: 支持激活码重新绑定功能

## 📁 项目结构

```
用户登录系统/
├── api/                       # PHP API接口
│   ├── login.php              # 用户登录
│   ├── register.php           # 用户注册
│   ├── forgot-password.php    # 密码找回
│   ├── reset-password.php     # 密码重置
│   ├── verify-session.php     # 会话验证
│   ├── captcha.php            # 验证码生成
│   ├── bind-activation.php    # 激活码绑定
│   ├── validate-activation.php # 激活码验证
│   └── stats.php              # 统计信息
├── server/                    # 数据库和工具(Web不可访问)
│   ├── user_system.db3        # SQLite数据库
│   ├── generate_activation_code.py # 激活码生成工具
│   ├── new_database_schema.sql # 数据库结构
│   └── .htaccess              # 目录保护
├── js/                        # 前端JavaScript
│   ├── new-auth-system.js     # 认证系统
│   └── new-session-check.js   # 会话检查
├── css/                       # 样式文件
│   ├── lock-style.css         # 登录页面样式
│   └── style.css              # 通用样式
├── images/                    # 图片资源
├── test/                      # 测试文件
├── docs/                      # 项目文档
├── new-lock.html              # 主登录页面
├── reset-password.html        # 密码重置页面
├── index.php                  # 入口页面
└── README.md                  # 项目说明
```

## 🛠️ 环境要求

- **PHP 7.0+** (推荐 PHP 7.4+)
- **Web服务器** (Apache/Nginx/IIS)
- **SQLite3扩展** (PHP内置)
- **PDO扩展** (PHP内置)
- **Python 3.6+** (仅激活码生成工具需要)

## 📦 快速部署

### 1. 上传文件
将所有文件上传到Web服务器目录

### 2. 设置权限
```bash
# 设置目录权限
chmod 755 api/
chmod 755 server/
chmod 666 server/user_system.db3

# 确保server目录Web不可访问(已有.htaccess)
```

### 3. 初始化数据库
数据库已预配置，包含完整的表结构和索引

### 4. 配置邮件发送
```bash
# 编辑邮件配置文件
nano server/email-config.php

# 配置您的邮箱信息（QQ邮箱示例）
$emailConfig['provider'] = 'qq';
$emailConfig['qq']['username'] = '<EMAIL>';
$emailConfig['qq']['password'] = 'your-auth-code';
```

### 5. 生成激活码
```bash
# 进入server目录
cd server/

# 运行Python生成工具
python generate_activation_code.py
```

### 6. 测试邮件配置
访问 `http://your-domain.com/test/test-email-config.html` 测试邮件发送

### 7. 访问系统
- 主页面: `http://your-domain.com/`
- 登录页面: `http://your-domain.com/new-lock.html`
- 邮件配置: `http://your-domain.com/test/test-email-config.html`
- 测试页面: `http://your-domain.com/test/`

## 🔧 配置说明

### 数据库配置
系统使用SQLite数据库，位于 `server/user_system.db3`，包含以下表：
- `users` - 用户信息
- `user_sessions` - 用户会话
- `activation_codes` - 激活码
- `password_resets` - 密码重置令牌
- `captcha_codes` - 验证码
- `login_logs` - 登录日志
- `payment_records` - 支付记录

### 安全配置
- server目录通过.htaccess禁止Web访问
- 数据库文件禁止直接下载
- 所有敏感操作都有验证码保护
- 会话令牌定期更新

## 🧪 测试工具

项目包含多个测试页面，位于 `test/` 目录：
- `test-new-system.html` - 系统功能测试
- `test-form-alignment.html` - 表单对齐测试
- `test-password-reset.html` - 密码找回测试
- `test-network-detection.html` - 网络检测测试

## 📚 文档

详细文档位于 `docs/` 目录：
- `PHP部署说明.md` - 详细部署指南
- `python-generator-guide.md` - 激活码生成工具说明
- `records.md` - 开发记录和更新日志
- `security-assessment.md` - 系统安全评估报告
- `security-improvements-summary.md` - 安全改进实施总结
- `user-experience-report.md` - 用户体验测试报告

## 🔒 安全特性

### 防护机制
- **验证码保护**: 防止自动化攻击
- **会话管理**: 防止会话劫持，HttpOnly+Secure+SameSite Cookie
- **单设备登录**: 防止账号共享
- **网络检测**: 防止断网绕过
- **目录保护**: 敏感文件Web不可访问
- **速率限制**: 多层级防暴力破解攻击
- **CSRF保护**: 防跨站请求伪造攻击

### 数据保护
- **密码安全**: Argon2ID哈希算法，内置盐值和成本参数
- **会话令牌**: 64字符强随机生成
- **重置令牌**: 有时效性和一次性使用
- **验证码**: 防暴力破解，会话绑定
- **向后兼容**: 旧密码格式自动升级

### 安全评级
- **整体安全**: ⭐⭐⭐⭐☆ (4.5/5)
- **密码安全**: ⭐⭐⭐⭐⭐ (5/5)
- **会话管理**: ⭐⭐⭐⭐⭐ (5/5)
- **攻击防护**: ⭐⭐⭐⭐☆ (4/5)

## 📞 技术支持

如有问题，请查看：
1. `docs/records.md` - 开发记录
2. `test/` 目录下的测试页面
3. PHP错误日志
4. 浏览器控制台

## 📄 许可证

本项目仅供学习和研究使用。

---

**最后更新**: 2025-01-23  
**版本**: v2.0  
**作者**: Augment Agent
