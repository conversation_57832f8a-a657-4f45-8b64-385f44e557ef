<?php
/**
 * 简单的连接测试API
 * 用于测试PHP后端是否正常工作
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // 基本信息
    $response = [
        'success' => true,
        'message' => 'PHP后端连接正常',
        'data' => [
            'php_version' => PHP_VERSION,
            'server_time' => date('Y-m-d H:i:s'),
            'request_method' => $_SERVER['REQUEST_METHOD'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
        ]
    ];
    
    // 测试数据库连接
    $dbPath = '../server/user_system.db3';
    if (file_exists($dbPath)) {
        try {
            $pdo = new PDO("sqlite:$dbPath");
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // 检查表是否存在
            $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $response['data']['database'] = [
                'status' => 'connected',
                'tables' => $tables,
                'path' => $dbPath
            ];
            
            // 统计激活码数量
            if (in_array('activation_codes', $tables)) {
                $stmt = $pdo->query("SELECT COUNT(*) FROM activation_codes");
                $codeCount = $stmt->fetchColumn();
                $response['data']['database']['activation_codes_count'] = $codeCount;
            }
            
        } catch (PDOException $e) {
            $response['data']['database'] = [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    } else {
        $response['data']['database'] = [
            'status' => 'not_found',
            'path' => $dbPath
        ];
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'PHP后端错误: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
