<?php
/**
 * 测试vendor修复结果
 * 验证PHPMailer是否可以正常加载
 */

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试vendor修复结果</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
    </style>
</head>
<body>
    <h1>🧪 测试vendor修复结果</h1>
    <p><strong>测试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="test-section">
        <h2>📊 vendor目录结构检查</h2>
        <?php
        $vendorBase = '../vendor';
        $requiredFiles = [
            'autoload.php',
            'composer/autoload_real.php',
            'composer/autoload_static.php',
            'composer/ClassLoader.php',
            'composer/platform_check.php',
            'composer/autoload_psr4.php',
            'phpmailer/phpmailer/src/PHPMailer.php',
            'phpmailer/phpmailer/src/SMTP.php',
            'phpmailer/phpmailer/src/Exception.php'
        ];
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>必需文件</th><th>状态</th><th>大小</th></tr>";
        
        $allExists = true;
        foreach ($requiredFiles as $file) {
            $path = $vendorBase . '/' . $file;
            $exists = file_exists($path);
            $size = $exists ? filesize($path) : 0;
            
            echo "<tr>";
            echo "<td>$file</td>";
            echo "<td>" . ($exists ? '<span class="success">✅ 存在</span>' : '<span class="error">❌ 缺失</span>') . "</td>";
            echo "<td>" . ($exists ? number_format($size) . ' 字节' : '-') . "</td>";
            echo "</tr>";
            
            if (!$exists) {
                $allExists = false;
            }
        }
        echo "</table>";
        
        if ($allExists) {
            echo "<p class='success'>✅ 所有必需文件都存在</p>";
        } else {
            echo "<p class='error'>❌ 部分文件缺失</p>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>🔧 PHPMailer加载测试</h2>
        <?php
        try {
            echo "<p>尝试加载PHPMailer...</p>";
            
            // 尝试加载autoload.php
            $autoloadPath = $vendorBase . '/autoload.php';
            if (!file_exists($autoloadPath)) {
                throw new Exception("autoload.php不存在: $autoloadPath");
            }
            
            require_once $autoloadPath;
            echo "<p class='success'>✅ autoload.php加载成功</p>";
            
            // 检查PHPMailer类是否可用
            if (!class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
                throw new Exception("PHPMailer类不可用");
            }
            echo "<p class='success'>✅ PHPMailer类可用</p>";
            
            // 尝试创建PHPMailer实例
            $mail = new PHPMailer\PHPMailer\PHPMailer(true);
            echo "<p class='success'>✅ PHPMailer实例创建成功</p>";
            
            // 显示版本信息
            echo "<p class='info'>📋 PHPMailer版本: " . $mail::VERSION . "</p>";
            
            // 测试基本配置
            $mail->isSMTP();
            $mail->Host = 'smtp.163.com';
            $mail->SMTPAuth = true;
            $mail->Username = '<EMAIL>';
            $mail->Password = 'password';
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
            $mail->Port = 465;
            $mail->CharSet = 'UTF-8';
            
            echo "<p class='success'>✅ SMTP配置设置成功</p>";
            
            echo "<div class='highlight'>";
            echo "<h3>🎉 PHPMailer修复成功！</h3>";
            echo "<p>vendor目录结构正确，PHPMailer可以正常加载和使用。</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ PHPMailer加载失败: " . htmlspecialchars($e->getMessage()) . "</p>";
            
            echo "<h3>调试信息:</h3>";
            echo "<pre>";
            echo "错误: " . $e->getMessage() . "\n";
            echo "文件: " . $e->getFile() . "\n";
            echo "行号: " . $e->getLine() . "\n";
            echo "</pre>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>🔗 下一步测试</h2>
        <?php if ($allExists): ?>
            <p>vendor结构修复成功！现在可以测试完整功能：</p>
            <ol>
                <li><strong>测试邮件发送器:</strong> <a href="test-working-email-sender.php">test-working-email-sender.php</a></li>
                <li><strong>测试密码找回:</strong> <a href="../new-lock.html">登录页面 → 忘记密码</a></li>
                <li><strong>最终验证:</strong> <a href="final-verification.php">final-verification.php</a></li>
            </ol>
        <?php else: ?>
            <p class="warning">⚠️ 需要先完成vendor目录修复</p>
            <ol>
                <li>运行 <a href="fix-vendor-structure.php">fix-vendor-structure.php</a></li>
                <li>或手动复制缺失的文件</li>
            </ol>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>📋 修复总结</h2>
        
        <h3>解决的问题:</h3>
        <ul>
            <li>✅ <strong>路径斜杠问题:</strong> Windows路径混合正反斜杠</li>
            <li>✅ <strong>缺失文件:</strong> composer目录结构不完整</li>
            <li>✅ <strong>PHPMailer依赖:</strong> 完整复制PHPMailer库</li>
        </ul>
        
        <h3>复制的关键文件:</h3>
        <ul>
            <li><strong>composer/autoload_real.php</strong> - Composer自动加载核心</li>
            <li><strong>composer/autoload_static.php</strong> - 静态自动加载映射</li>
            <li><strong>composer/ClassLoader.php</strong> - 类加载器</li>
            <li><strong>composer/platform_check.php</strong> - 平台检查</li>
            <li><strong>phpmailer/</strong> - 完整的PHPMailer库</li>
        </ul>
        
        <h3>Windows路径修复:</h3>
        <p>原错误: <code>D:\www\test_daojishi\vendor/composer/autoload_real.php</code></p>
        <p>现在使用正确的相对路径引用，避免了路径混合问题。</p>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
