<?php
/**
 * 测试本地PHPMailer
 * 验证复制到本项目的PHPMailer是否正常工作
 */

header('Content-Type: text/html; charset=utf-8');

$testResult = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_local_phpmailer'])) {
    $testResult = testLocalPHPMailer();
}

function testLocalPHPMailer() {
    $result = [
        'start_time' => microtime(true),
        'success' => false,
        'error' => null,
        'steps' => []
    ];
    
    try {
        // 步骤1: 检查vendor目录
        $result['steps'][] = ['step' => '检查vendor目录', 'status' => 'start', 'time' => microtime(true)];
        $vendorPath = '../vendor';
        if (!is_dir($vendorPath)) {
            throw new Exception("vendor目录不存在: $vendorPath");
        }
        $result['steps'][] = ['step' => '检查vendor目录', 'status' => 'success', 'time' => microtime(true)];
        
        // 步骤2: 检查autoload.php
        $result['steps'][] = ['step' => '检查autoload.php', 'status' => 'start', 'time' => microtime(true)];
        $autoloadPath = $vendorPath . '/autoload.php';
        if (!file_exists($autoloadPath)) {
            throw new Exception("autoload.php不存在: $autoloadPath");
        }
        $result['steps'][] = ['step' => '检查autoload.php', 'status' => 'success', 'time' => microtime(true)];
        
        // 步骤3: 加载PHPMailer
        $result['steps'][] = ['step' => '加载PHPMailer', 'status' => 'start', 'time' => microtime(true)];
        require_once $autoloadPath;
        $result['steps'][] = ['step' => '加载PHPMailer', 'status' => 'success', 'time' => microtime(true)];
        
        // 步骤4: 检查PHPMailer类
        $result['steps'][] = ['step' => '检查PHPMailer类', 'status' => 'start', 'time' => microtime(true)];
        if (!class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
            throw new Exception("PHPMailer类不存在");
        }
        $result['steps'][] = ['step' => '检查PHPMailer类', 'status' => 'success', 'time' => microtime(true)];
        
        // 步骤5: 创建PHPMailer实例
        $result['steps'][] = ['step' => '创建PHPMailer实例', 'status' => 'start', 'time' => microtime(true)];
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        $result['steps'][] = ['step' => '创建PHPMailer实例', 'status' => 'success', 'time' => microtime(true), 'data' => [
            'version' => $mail::VERSION,
            'class' => get_class($mail)
        ]];
        
        // 步骤6: 测试SMTP配置
        $result['steps'][] = ['step' => '测试SMTP配置', 'status' => 'start', 'time' => microtime(true)];
        $mail->isSMTP();
        $mail->Host = 'smtp.163.com';
        $mail->SMTPAuth = true;
        $mail->Username = '<EMAIL>';
        $mail->Password = 'BJXfKJw32HgDSMSg';
        $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
        $mail->Port = 465;
        $mail->CharSet = 'UTF-8';
        $result['steps'][] = ['step' => '测试SMTP配置', 'status' => 'success', 'time' => microtime(true)];
        
        // 步骤7: 测试邮件构建
        $result['steps'][] = ['step' => '测试邮件构建', 'status' => 'start', 'time' => microtime(true)];
        $mail->setFrom('<EMAIL>', '倒计时系统');
        $mail->addAddress('<EMAIL>');
        $mail->isHTML(true);
        $mail->Subject = '本地PHPMailer测试';
        $mail->Body = '<h2>测试邮件</h2><p>这是使用本地PHPMailer发送的测试邮件。</p><p>发送时间：' . date('Y-m-d H:i:s') . '</p>';
        $result['steps'][] = ['step' => '测试邮件构建', 'status' => 'success', 'time' => microtime(true)];
        
        $result['success'] = true;
        
    } catch (Exception $e) {
        $result['error'] = $e->getMessage();
        $result['steps'][] = ['step' => '异常', 'status' => 'error', 'time' => microtime(true), 'error' => $e->getMessage()];
    }
    
    $result['end_time'] = microtime(true);
    $result['total_time'] = $result['end_time'] - $result['start_time'];
    
    return $result;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试本地PHPMailer</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
        button { padding: 8px 16px; margin: 5px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
        .step { margin: 10px 0; padding: 10px; border-left: 4px solid #ddd; }
        .step.success { border-left-color: #4CAF50; background: #f0f8f0; }
        .step.error { border-left-color: #f44336; background: #fdf0f0; }
        .step.start { border-left-color: #2196F3; background: #f0f7ff; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
    </style>
</head>
<body>
    <h1>🧪 测试本地PHPMailer</h1>
    <p><strong>测试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="highlight">
        <h3>🎯 测试目的</h3>
        <p><strong>验证内容:</strong> 确认复制到本项目的PHPMailer文件是否完整且可正常使用</p>
        <p><strong>测试范围:</strong></p>
        <ul>
            <li>vendor目录结构</li>
            <li>autoload.php加载</li>
            <li>PHPMailer类可用性</li>
            <li>SMTP配置功能</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>📊 本地vendor目录状态</h2>
        <?php
        $vendorDir = '../vendor';
        $autoloadFile = $vendorDir . '/autoload.php';
        $phpmailerDir = $vendorDir . '/phpmailer';
        $composerDir = $vendorDir . '/composer';
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>项目</th><th>路径</th><th>状态</th></tr>";
        
        $checks = [
            'vendor目录' => $vendorDir,
            'autoload.php' => $autoloadFile,
            'phpmailer目录' => $phpmailerDir,
            'composer目录' => $composerDir
        ];
        
        foreach ($checks as $name => $path) {
            $exists = file_exists($path);
            echo "<tr>";
            echo "<td><strong>$name</strong></td>";
            echo "<td>$path</td>";
            echo "<td>" . ($exists ? '<span class="success">✅ 存在</span>' : '<span class="error">❌ 不存在</span>') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        ?>
    </div>
    
    <div class="test-section">
        <h2>🧪 PHPMailer功能测试</h2>
        <form method="POST">
            <p>点击下面的按钮测试本地PHPMailer的功能:</p>
            <button type="submit" name="test_local_phpmailer">测试本地PHPMailer</button>
        </form>
        
        <?php if ($testResult): ?>
            <h3>测试结果:</h3>
            
            <div class="step <?php echo $testResult['success'] ? 'success' : 'error'; ?>">
                <h4><?php echo $testResult['success'] ? '✅ 本地PHPMailer测试成功' : '❌ 本地PHPMailer测试失败'; ?></h4>
                <p><strong>总耗时:</strong> <?php echo number_format($testResult['total_time'], 3); ?> 秒</p>
                <?php if ($testResult['error']): ?>
                    <p><strong>错误信息:</strong> <?php echo htmlspecialchars($testResult['error']); ?></p>
                <?php endif; ?>
            </div>
            
            <h4>详细步骤:</h4>
            <?php foreach ($testResult['steps'] as $step): ?>
                <div class="step <?php echo $step['status']; ?>">
                    <h5><?php echo $step['step']; ?> - <?php echo ucfirst($step['status']); ?></h5>
                    <?php if (isset($step['data'])): ?>
                        <pre><?php echo htmlspecialchars(json_encode($step['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
                    <?php endif; ?>
                    <?php if (isset($step['error'])): ?>
                        <p class="error">错误: <?php echo htmlspecialchars($step['error']); ?></p>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
            
            <?php if ($testResult['success']): ?>
                <div class="highlight">
                    <h4>🎉 测试成功！</h4>
                    <p>本地PHPMailer配置正确，现在可以:</p>
                    <ol>
                        <li>更新working-email-sender.php使用本地路径</li>
                        <li>测试完整的邮件发送功能</li>
                        <li>测试密码找回功能</li>
                    </ol>
                    
                    <p>
                        <a href="update-phpmailer-paths.php"><button>更新路径引用</button></a>
                        <a href="test-working-email-sender.php"><button>测试邮件发送</button></a>
                    </p>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>📋 部署优势</h2>
        <h3>使用本地PHPMailer的优势:</h3>
        <ul>
            <li>✅ <strong>独立部署:</strong> 不依赖外部路径</li>
            <li>✅ <strong>版本控制:</strong> PHPMailer版本固定，避免兼容性问题</li>
            <li>✅ <strong>服务器兼容:</strong> 可以直接复制到任何服务器</li>
            <li>✅ <strong>路径简化:</strong> 使用相对路径，更容易维护</li>
        </ul>
        
        <h3>文件结构:</h3>
        <pre>
项目根目录/
├── vendor/                 # PHPMailer依赖
│   ├── autoload.php       # 自动加载文件
│   ├── composer/          # Composer文件
│   └── phpmailer/         # PHPMailer库
├── server/                # 服务器文件
│   └── working-email-sender.php
└── api/                   # API文件
    └── forgot-password.php
        </pre>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关链接</h2>
        <ul>
            <li><a href="copy-phpmailer.php">复制PHPMailer文件</a></li>
            <li><a href="update-phpmailer-paths.php">更新路径引用</a></li>
            <li><a href="test-working-email-sender.php">测试工作的邮件发送器</a></li>
            <li><a href="../new-lock.html">返回登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
