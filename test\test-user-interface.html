<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户界面测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .info {
            color: #0c5460;
            background: #d1ecf1;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .test-section {
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin: 20px 0;
        }
        
        .demo-area {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            min-height: 200px;
            position: relative;
        }
        
        .demo-content {
            text-align: center;
            padding: 50px 20px;
            color: #666;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <h1>🎨 用户界面优化测试</h1>
    
    <div class="container">
        <h2>界面优化内容</h2>
        <div class="info">
            <strong>✅ 已完成的优化:</strong><br>
            1. 会话检测频率从5分钟改为30秒<br>
            2. 新设备登录时实时检测并提示<br>
            3. 用户信息改为右上角矢量图标<br>
            4. 悬停显示二级菜单（用户名、激活码、到期时间、退出按钮）<br>
            5. 3秒后图标透明度降低50%<br>
            6. 删除原有的绿色背景和剩余天数显示
        </div>
    </div>
    
    <div class="container">
        <h2>🧪 功能测试</h2>
        
        <div class="test-section">
            <h3>1. 模拟用户界面</h3>
            <p>下面的区域模拟登录后的主页面效果：</p>
            <div class="demo-area" id="demoArea">
                <div class="demo-content">
                    这里是主页面内容区域<br>
                    右上角应该显示用户图标
                </div>
            </div>
            <button onclick="simulateUserLogin()">模拟用户登录</button>
            <button onclick="clearDemo()">清除演示</button>
        </div>
        
        <div class="test-section">
            <h3>2. 会话检测测试</h3>
            <p>测试新设备登录时的实时检测功能：</p>
            <button onclick="simulateSessionExpired()">模拟会话失效</button>
            <button onclick="testPeriodicCheck()">测试定期检查</button>
            <div id="sessionTestResult"></div>
        </div>
    </div>
    
    <div class="container">
        <h2>🎯 设计说明</h2>
        <div class="info">
            <strong>用户图标设计:</strong><br>
            • 位置：页面右上角固定位置<br>
            • 样式：40px圆形白色背景，内含SVG用户图标<br>
            • 交互：鼠标悬停显示下拉菜单<br>
            • 透明度：3秒后自动降低到50%<br><br>
            
            <strong>下拉菜单内容:</strong><br>
            • 用户名显示<br>
            • 激活码（等宽字体）<br>
            • 到期时间<br>
            • 退出登录按钮<br><br>
            
            <strong>会话检测优化:</strong><br>
            • 检测频率：30秒一次<br>
            • 失效提示：弹窗显示"会话已失效"<br>
            • 自动跳转：跳转到登录页面
        </div>
    </div>
    
    <div class="container">
        <h2>🔗 快速链接</h2>
        <button onclick="window.location.href='../new-lock.html'">登录页面</button>
        <button onclick="window.location.href='../index.php'">主页面</button>
        <button onclick="window.open('../index.php', '_blank')">新窗口打开主页面</button>
    </div>

    <script>
        function simulateUserLogin() {
            const demoArea = document.getElementById('demoArea');
            
            // 清除现有内容
            clearDemo();
            
            // 创建模拟的用户图标
            const userContainer = document.createElement('div');
            userContainer.style.cssText = `
                position: absolute;
                top: 20px;
                right: 20px;
                z-index: 1000;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
            `;
            
            const userIcon = document.createElement('div');
            userIcon.style.cssText = `
                width: 40px;
                height: 40px;
                background: rgba(255, 255, 255, 0.9);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                border: 2px solid rgba(0,0,0,0.1);
            `;
            
            userIcon.innerHTML = `
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                </svg>
            `;
            
            const dropdownMenu = document.createElement('div');
            dropdownMenu.style.cssText = `
                position: absolute;
                top: 50px;
                right: 0;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                padding: 15px;
                min-width: 200px;
                opacity: 0;
                visibility: hidden;
                transform: translateY(-10px);
                transition: all 0.3s ease;
                border: 1px solid rgba(0,0,0,0.1);
            `;
            
            dropdownMenu.innerHTML = `
                <div style="padding-bottom: 10px; border-bottom: 1px solid #eee; margin-bottom: 10px;">
                    <div style="font-weight: bold; color: #333; margin-bottom: 5px;">👤 testuser</div>
                    <div style="font-size: 12px; color: #666;">已登录</div>
                </div>
                <div style="margin-bottom: 10px;">
                    <div style="font-size: 12px; color: #666; margin-bottom: 3px;">激活码</div>
                    <div style="font-size: 13px; color: #333; font-family: monospace;">6T92U-RFTEY-546GZ-BFXMM</div>
                </div>
                <div style="margin-bottom: 15px;">
                    <div style="font-size: 12px; color: #666; margin-bottom: 3px;">到期时间</div>
                    <div style="font-size: 13px; color: #333;">2025-08-22</div>
                </div>
                <button style="
                    width: 100%;
                    background: #f44336;
                    color: white;
                    border: none;
                    padding: 8px 12px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 13px;
                    transition: background 0.3s ease;
                ">🚪 退出登录</button>
            `;
            
            userContainer.appendChild(userIcon);
            userContainer.appendChild(dropdownMenu);
            demoArea.appendChild(userContainer);
            
            // 添加交互效果
            let hideTimeout = null;
            
            userIcon.addEventListener('mouseenter', () => {
                clearTimeout(hideTimeout);
                dropdownMenu.style.opacity = '1';
                dropdownMenu.style.visibility = 'visible';
                dropdownMenu.style.transform = 'translateY(0)';
                userIcon.style.background = 'rgba(76, 175, 80, 0.1)';
                userIcon.style.borderColor = 'rgba(76, 175, 80, 0.3)';
            });
            
            userIcon.addEventListener('mouseleave', () => {
                hideTimeout = setTimeout(() => {
                    dropdownMenu.style.opacity = '0';
                    dropdownMenu.style.visibility = 'hidden';
                    dropdownMenu.style.transform = 'translateY(-10px)';
                    userIcon.style.background = 'rgba(255, 255, 255, 0.9)';
                    userIcon.style.borderColor = 'rgba(0,0,0,0.1)';
                }, 100);
            });
            
            dropdownMenu.addEventListener('mouseenter', () => {
                clearTimeout(hideTimeout);
            });
            
            dropdownMenu.addEventListener('mouseleave', () => {
                hideTimeout = setTimeout(() => {
                    dropdownMenu.style.opacity = '0';
                    dropdownMenu.style.visibility = 'hidden';
                    dropdownMenu.style.transform = 'translateY(-10px)';
                    userIcon.style.background = 'rgba(255, 255, 255, 0.9)';
                    userIcon.style.borderColor = 'rgba(0,0,0,0.1)';
                }, 100);
            });
            
            // 3秒后降低透明度
            setTimeout(() => {
                userIcon.style.opacity = '0.5';
            }, 3000);
            
            console.log('✅ 模拟用户登录界面已创建');
        }
        
        function clearDemo() {
            const demoArea = document.getElementById('demoArea');
            demoArea.innerHTML = `
                <div class="demo-content">
                    这里是主页面内容区域<br>
                    右上角应该显示用户图标
                </div>
            `;
        }
        
        function simulateSessionExpired() {
            const resultDiv = document.getElementById('sessionTestResult');
            
            // 创建会话失效提示
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
            `;
            
            overlay.innerHTML = `
                <div style="
                    background: white;
                    padding: 40px;
                    border-radius: 10px;
                    text-align: center;
                    max-width: 400px;
                ">
                    <h2 style="color: #f44336; margin-bottom: 20px;">🔒 会话已失效</h2>
                    <p style="margin-bottom: 20px;">您的账号在其他设备登录，当前会话已失效</p>
                    <div style="color: #666; font-size: 14px;">即将跳转到登录页面...</div>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        margin-top: 15px;
                        background: #007bff;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 5px;
                        cursor: pointer;
                    ">关闭演示</button>
                </div>
            `;
            
            document.body.appendChild(overlay);
            
            resultDiv.innerHTML = '<div class="success">✅ 会话失效提示已显示</div>';
        }
        
        function testPeriodicCheck() {
            const resultDiv = document.getElementById('sessionTestResult');
            resultDiv.innerHTML = `
                <div class="info">
                    📊 会话检测配置:<br>
                    • 检测频率: 30秒一次<br>
                    • 检测内容: 会话令牌有效性<br>
                    • 失效处理: 显示提示并跳转登录<br>
                    • 实时性: 新设备登录后30秒内检测到
                </div>
            `;
        }
    </script>
</body>
</html>
