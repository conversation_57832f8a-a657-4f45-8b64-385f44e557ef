<?php
/**
 * PHP后端功能测试脚本
 * 测试数据库连接、激活码生成、验证等功能
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>PHP后端功能测试</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { background: #f8f9fa; margin: 20px 0; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        h1, h2 { color: #333; }
        code { background: #e9ecef; padding: 2px 4px; border-radius: 3px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>";

echo "<h1>🔐 PHP后端功能测试</h1>";

// 测试1: PHP环境检查
echo "<div class='test-section'>";
echo "<h2>📋 PHP环境检查</h2>";
echo "<p><strong>PHP版本:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>操作系统:</strong> " . PHP_OS . "</p>";

// 检查必要扩展
$requiredExtensions = ['pdo', 'pdo_sqlite', 'json'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p>✅ <strong>$ext</strong> 扩展已加载</p>";
    } else {
        echo "<p>❌ <strong>$ext</strong> 扩展未加载</p>";
        $missingExtensions[] = $ext;
    }
}

if (empty($missingExtensions)) {
    echo "<p class='success'>✅ PHP环境检查通过</p>";
} else {
    echo "<p class='error'>❌ 缺少必要扩展: " . implode(', ', $missingExtensions) . "</p>";
}
echo "</div>";

// 测试2: 数据库连接测试
echo "<div class='test-section'>";
echo "<h2>🗄️ 数据库连接测试</h2>";

$dbPath = '../server/user_system.db3';

try {
    // 确保目录存在
    $serverDir = dirname($dbPath);
    if (!is_dir($serverDir)) {
        mkdir($serverDir, 0755, true);
        echo "<p>✅ 创建目录: $serverDir</p>";
    }
    
    // 测试数据库连接
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ SQLite数据库连接成功</p>";
    echo "<p><strong>数据库路径:</strong> <code>$dbPath</code></p>";
    
    // 检查表是否存在
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<p class='warning'>⚠️ 数据库为空，需要初始化</p>";
        echo "<p>请运行: <code>php admin/init-database.php</code></p>";
    } else {
        echo "<p>✅ 数据库表: " . implode(', ', $tables) . "</p>";
        
        // 统计数据
        if (in_array('activation_codes', $tables)) {
            $stmt = $pdo->query("SELECT COUNT(*) FROM activation_codes");
            $codeCount = $stmt->fetchColumn();
            echo "<p>📊 激活码数量: $codeCount</p>";
        }
        
        if (in_array('user_records', $tables)) {
            $stmt = $pdo->query("SELECT COUNT(*) FROM user_records");
            $userCount = $stmt->fetchColumn();
            echo "<p>📊 用户记录: $userCount</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p class='error'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 测试3: 激活码生成测试
echo "<div class='test-section'>";
echo "<h2>🔑 激活码生成测试</h2>";

function generateTestCode() {
    $charset = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
    $segments = [];
    
    for ($i = 0; $i < 4; $i++) {
        $segment = '';
        for ($j = 0; $j < 5; $j++) {
            $segment .= $charset[random_int(0, strlen($charset) - 1)];
        }
        $segments[] = $segment;
    }
    
    return implode('-', $segments);
}

try {
    $testCode = generateTestCode();
    echo "<p>✅ 激活码生成成功</p>";
    echo "<p><strong>示例激活码:</strong> <code>$testCode</code></p>";
    
    // 验证格式
    if (preg_match('/^[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}$/', $testCode)) {
        echo "<p>✅ 激活码格式验证通过</p>";
    } else {
        echo "<p class='error'>❌ 激活码格式验证失败</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 激活码生成失败: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 测试4: JSON处理测试
echo "<div class='test-section'>";
echo "<h2>📡 JSON处理测试</h2>";

$testData = [
    'activationCode' => 'TEST1-TEST2-TEST3-TEST4',
    'deviceFingerprint' => 'test123456789',
    'deviceInfo' => 'Test Device Info',
    'timestamp' => time()
];

try {
    $jsonString = json_encode($testData, JSON_UNESCAPED_UNICODE);
    echo "<p>✅ JSON编码成功</p>";
    echo "<pre>" . htmlspecialchars($jsonString) . "</pre>";
    
    $decodedData = json_decode($jsonString, true);
    if ($decodedData && $decodedData['activationCode'] === $testData['activationCode']) {
        echo "<p>✅ JSON解码验证通过</p>";
    } else {
        echo "<p class='error'>❌ JSON解码验证失败</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ JSON处理失败: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 测试5: 文件权限测试
echo "<div class='test-section'>";
echo "<h2>📁 文件权限测试</h2>";

$testPaths = [
    '../server/' => '数据库目录',
    '../api/' => 'API目录',
    '../admin/' => '管理工具目录'
];

foreach ($testPaths as $path => $description) {
    if (is_dir($path)) {
        if (is_writable($path)) {
            echo "<p>✅ $description 可写</p>";
        } else {
            echo "<p class='warning'>⚠️ $description 不可写</p>";
        }
    } else {
        echo "<p class='error'>❌ $description 不存在</p>";
    }
}
echo "</div>";

// 测试6: API接口测试
echo "<div class='test-section'>";
echo "<h2>🌐 API接口测试</h2>";

$apiTests = [
    '../api/validate-activation.php' => '激活码验证API',
    '../api/stats.php' => '统计信息API',
    '../admin/init-database.php' => '数据库初始化',
    '../admin/generate-codes.php' => '激活码生成器'
];

foreach ($apiTests as $file => $description) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "<p>✅ $description 文件可访问</p>";
        } else {
            echo "<p class='error'>❌ $description 文件不可读</p>";
        }
    } else {
        echo "<p class='error'>❌ $description 文件不存在</p>";
    }
}
echo "</div>";

// 总结
echo "<div class='test-section'>";
echo "<h2>📋 测试总结</h2>";
echo "<p>🎯 <strong>下一步操作:</strong></p>";
echo "<ol>";
echo "<li>如果数据库未初始化，运行: <code>php admin/init-database.php</code></li>";
echo "<li>生成激活码: <code>php admin/generate-codes.php</code></li>";
echo "<li>配置Web服务器，确保可以访问API接口</li>";
echo "<li>访问锁屏页面进行完整测试</li>";
echo "</ol>";

echo "<p>🔗 <strong>相关链接:</strong></p>";
echo "<ul>";
echo "<li><a href='../lock.html'>锁屏页面</a></li>";
echo "<li><a href='../test/test-unlock-system.html'>系统测试页面</a></li>";
echo "<li><a href='../api/stats.php'>统计信息API</a></li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
