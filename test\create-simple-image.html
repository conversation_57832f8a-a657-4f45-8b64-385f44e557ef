<!DOCTYPE html>
<html>
<head>
    <title>创建简单测试图片</title>
</head>
<body>
    <h3>点击按钮创建测试图片</h3>
    <button onclick="createImages()">创建测试图片</button>
    <div id="status"></div>
    
    <script>
        function createImages() {
            // 创建Logo图片
            const logoCanvas = document.createElement('canvas');
            logoCanvas.width = 200;
            logoCanvas.height = 100;
            const logoCtx = logoCanvas.getContext('2d');
            
            // 绘制Logo
            logoCtx.fillStyle = '#ff6b6b';
            logoCtx.fillRect(0, 0, 200, 100);
            logoCtx.fillStyle = '#ffffff';
            logoCtx.font = '20px Arial';
            logoCtx.textAlign = 'center';
            logoCtx.fillText('TEST LOGO', 100, 55);
            
            // 创建汽车图片
            const carCanvas = document.createElement('canvas');
            carCanvas.width = 300;
            carCanvas.height = 200;
            const carCtx = carCanvas.getContext('2d');
            
            // 绘制汽车图片
            carCtx.fillStyle = '#6495ed';
            carCtx.fillRect(0, 0, 300, 200);
            carCtx.fillStyle = '#ffffff';
            carCtx.font = '24px Arial';
            carCtx.textAlign = 'center';
            carCtx.fillText('TEST CAR', 150, 110);
            
            // 下载Logo
            logoCanvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'logo.png';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 'image/png');
            
            // 下载汽车图片
            setTimeout(() => {
                carCanvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'car.png';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }, 'image/png');
            }, 1000);
            
            document.getElementById('status').innerHTML = '图片已创建并下载！请将下载的图片放入uploads文件夹中。';
        }
    </script>
</body>
</html>
