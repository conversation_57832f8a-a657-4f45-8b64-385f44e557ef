<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>激活码调试工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .success {
            border-left: 4px solid #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left: 4px solid #dc3545;
            background: #f8d7da;
        }
        
        .warning {
            border-left: 4px solid #ffc107;
            background: #fff3cd;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .generate-btn {
            background: #28a745;
        }
        
        .generate-btn:hover {
            background: #218838;
        }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .code-display {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            cursor: pointer;
        }
        
        .code-display:hover {
            background: #dee2e6;
        }
        
        input {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>🔧 激活码调试工具</h1>
    
    <!-- 快速生成激活码 -->
    <div class="section">
        <h2>⚡ 快速生成激活码</h2>
        <button class="generate-btn" onclick="generateQuickCodes()">生成3个测试激活码</button>
        <div id="quickGenResult"></div>
    </div>
    
    <!-- 检查数据库状态 -->
    <div class="section">
        <h2>🔍 数据库状态检查</h2>
        <button onclick="checkDatabase()">检查数据库</button>
        <div id="dbResult"></div>
    </div>
    
    <!-- 测试特定激活码 -->
    <div class="section">
        <h2>🧪 测试激活码验证</h2>
        <input type="text" id="testCodeInput" placeholder="输入激活码进行测试" value="V4RP9-VPUMG-JRRW6-UP229">
        <button onclick="testSpecificCode()">测试验证</button>
        <div id="testResult"></div>
    </div>
    
    <!-- 可用激活码列表 -->
    <div class="section">
        <h2>📋 可用激活码</h2>
        <button onclick="listActiveCodes()">刷新列表</button>
        <div id="activeCodesList"></div>
    </div>

    <script>
        async function generateQuickCodes() {
            const resultDiv = document.getElementById('quickGenResult');
            resultDiv.innerHTML = '<div class="warning">生成中...</div>';
            
            try {
                const response = await fetch('admin/quick-add-code.php');
                const data = await response.json();
                
                if (data.success) {
                    let html = '<div class="success">✅ ' + data.message + '</div>';
                    
                    if (data.new_codes && data.new_codes.length > 0) {
                        html += '<h4>新生成的激活码:</h4>';
                        data.new_codes.forEach(code => {
                            html += `<div class="code-display" onclick="copyToClipboard('${code.code}')" title="点击复制">${code.code}</div>`;
                        });
                    }
                    
                    if (data.all_active_codes && data.all_active_codes.length > 0) {
                        html += '<h4>所有可用激活码:</h4>';
                        data.all_active_codes.forEach(code => {
                            html += `<div class="code-display" onclick="copyToClipboard('${code.code}')" title="点击复制">${code.code}</div>`;
                        });
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 生成失败: ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }
        
        async function checkDatabase() {
            const resultDiv = document.getElementById('dbResult');
            resultDiv.innerHTML = '<div class="warning">检查中...</div>';
            
            try {
                const response = await fetch('admin/debug-codes.php');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ 数据库连接正常</div>
                        <pre>${JSON.stringify(data.data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 数据库错误: ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }
        
        async function testSpecificCode() {
            const resultDiv = document.getElementById('testResult');
            const code = document.getElementById('testCodeInput').value.trim();
            
            if (!code) {
                resultDiv.innerHTML = '<div class="error">❌ 请输入激活码</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="warning">测试中...</div>';
            
            try {
                const response = await fetch('api/validate-activation.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        activationCode: code,
                        deviceFingerprint: 'debug-test-' + Date.now(),
                        deviceInfo: 'Debug Test Device',
                        userAgent: navigator.userAgent,
                        screenResolution: `${screen.width}x${screen.height}`,
                        timestamp: Date.now()
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="success">✅ 激活码验证成功</div>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="warning">⚠️ 激活码验证失败</div>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        `;
                    }
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ HTTP错误 ${response.status}</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }
        
        async function listActiveCodes() {
            const resultDiv = document.getElementById('activeCodesList');
            resultDiv.innerHTML = '<div class="warning">加载中...</div>';
            
            try {
                const response = await fetch('admin/quick-add-code.php');
                const data = await response.json();
                
                if (data.success && data.all_active_codes) {
                    if (data.all_active_codes.length > 0) {
                        let html = '<div class="success">✅ 找到 ' + data.all_active_codes.length + ' 个可用激活码</div>';
                        data.all_active_codes.forEach(code => {
                            html += `
                                <div class="code-display" onclick="copyToClipboard('${code.code}')" title="点击复制">
                                    ${code.code}
                                    <small style="display: block; font-size: 12px; color: #666;">
                                        创建: ${code.created_at} | 过期: ${code.expires_at}
                                    </small>
                                </div>
                            `;
                        });
                        resultDiv.innerHTML = html;
                    } else {
                        resultDiv.innerHTML = '<div class="warning">⚠️ 没有可用的激活码，请先生成一些</div>';
                    }
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 获取失败: ${data.error || '未知错误'}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }
        
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('激活码已复制: ' + text);
                // 自动填入测试框
                document.getElementById('testCodeInput').value = text;
            }, function(err) {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('激活码已复制: ' + text);
                document.getElementById('testCodeInput').value = text;
            });
        }
        
        // 页面加载时自动检查
        window.addEventListener('load', () => {
            console.log('🔧 调试工具加载完成');
            listActiveCodes();
        });
    </script>
</body>
</html>
