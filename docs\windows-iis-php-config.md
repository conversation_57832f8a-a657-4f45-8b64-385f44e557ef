# Windows 2008 + IIS + PHP-CGI 配置指南

## 📊 PHP.ini 配置修改说明

### 🔧 已修改的关键配置

#### 1. 错误显示配置 (生产环境)
```ini
# 原配置 (开发环境)
display_errors = On
display_startup_errors = On

# 新配置 (生产环境) ✅
display_errors = Off
display_startup_errors = Off
```

#### 2. 错误日志配置
```ini
# 启用错误日志
log_errors = On
log_errors_max_len = 4096

# Windows路径格式的错误日志文件 ✅
error_log = "D:\error_log\php\php_errors.log"
```

#### 3. Windows IIS 特定配置
```ini
# 内存和执行时间
memory_limit = 256M
max_execution_time = 60

# 文件上传
upload_max_filesize = 10M
post_max_size = 12M

# Windows路径配置
session.save_path = "D:\temp\php\sessions"
upload_tmp_dir = "D:\temp\php\uploads"

# 时区设置
date.timezone = "Asia/Shanghai"

# CGI配置
cgi.fix_pathinfo = 1
fastcgi.impersonate = 1

# 安全配置
disable_functions = exec,passthru,shell_exec,system,proc_open,popen
expose_php = Off
```

## 📁 需要创建的目录结构

### 1. 错误日志目录
```
D:\error_log\
└── php\
    └── php_errors.log  (PHP会自动创建此文件)
```

### 2. 临时文件目录
```
D:\temp\
├── php\
│   ├── sessions\    (会话文件目录)
│   └── uploads\     (上传临时文件目录)
```

## 🛠️ 部署步骤

### 步骤1: 创建必要目录
```batch
# 在Windows命令行中执行
mkdir "D:\error_log\php"
mkdir "D:\temp\php\sessions"
mkdir "D:\temp\php\uploads"
```

### 步骤2: 设置目录权限
为以下目录设置IIS应用程序池用户的完全控制权限：
- `D:\error_log\php\`
- `D:\temp\php\sessions\`
- `D:\temp\php\uploads\`

**设置方法**:
1. 右键点击目录 → 属性 → 安全
2. 添加 `IIS_IUSRS` 用户组
3. 给予"完全控制"权限

### 步骤3: 替换php.ini文件
1. 备份当前的php.ini文件
2. 将修改后的php.ini文件复制到PHP安装目录
3. 重启IIS服务

### 步骤4: 重启IIS
```batch
# 在管理员命令行中执行
iisreset
```

## 🔍 验证配置

### 1. 检查PHP配置
创建一个测试文件 `phpinfo.php`:
```php
<?php
phpinfo();
?>
```

访问此文件，检查以下配置项：
- `display_errors` = Off
- `log_errors` = On
- `error_log` = D:\error_log\php\php_errors.log

### 2. 测试错误日志
创建测试文件 `test_error.php`:
```php
<?php
// 测试错误日志
error_log("Test error message from PHP");

// 触发一个警告
$undefined_variable = $non_existent_var;

echo json_encode(['status' => 'test completed']);
?>
```

访问此文件后，检查 `D:\error_log\php\php_errors.log` 是否有错误记录。

### 3. 测试JSON响应
确保访问 `test_error.php` 返回纯净的JSON，没有PHP错误信息混入。

## 🚨 故障排除

### 问题1: 错误日志文件未创建
**原因**: 目录权限不足
**解决**: 确保IIS_IUSRS有写入权限

### 问题2: PHP配置未生效
**原因**: php.ini文件路径不正确或未重启IIS
**解决**: 
1. 检查phpinfo()中的"Loaded Configuration File"路径
2. 确保修改的是正确的php.ini文件
3. 重启IIS服务

### 问题3: 仍然显示错误信息
**原因**: 可能存在多个php.ini文件或配置被覆盖
**解决**:
1. 检查phpinfo()中的所有配置文件路径
2. 在应用程序中使用 `ini_set('display_errors', 0)`

## 📋 密码找回功能修复

### 应用层临时修复
在 `api/forgot-password.php` 开头添加：
```php
<?php
// 确保错误不显示到浏览器
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// 设置正确的内容类型
header('Content-Type: application/json; charset=utf-8');

// 开始输出缓冲，防止意外输出
ob_start();

try {
    // 原有代码逻辑
    
} catch (Exception $e) {
    // 清理输出缓冲
    ob_clean();
    
    // 记录错误到日志
    error_log("Forgot password error: " . $e->getMessage());
    
    // 返回标准JSON错误响应
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '系统错误，请稍后重试'
    ]);
    exit;
}

// 确保输出缓冲结束
ob_end_flush();
?>
```

## 📊 配置对比

### 修改前 (开发环境)
```ini
display_errors = On          # ❌ 错误显示到浏览器
display_startup_errors = On  # ❌ 启动错误显示
;error_log = php_errors.log  # ❌ 错误日志路径未设置
```

### 修改后 (生产环境)
```ini
display_errors = Off                              # ✅ 错误不显示
display_startup_errors = Off                     # ✅ 启动错误不显示
error_log = "D:\error_log\php\php_errors.log"   # ✅ 明确的日志路径
```

## 🎯 预期效果

1. **密码找回功能修复**: JSON响应不再被PHP错误污染
2. **错误监控**: 所有PHP错误记录到指定日志文件
3. **安全性提升**: 错误信息不暴露给用户
4. **调试便利**: 开发人员可以通过日志文件查看详细错误

## 📅 配置信息
**配置日期**: 2025-07-25  
**环境**: Windows 2008 + IIS + PHP-CGI  
**配置文件**: docs/php.ini  
**错误日志**: D:\error_log\php\php_errors.log  
**状态**: 已配置，待部署
