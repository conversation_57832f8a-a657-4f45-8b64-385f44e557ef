<?php
/**
 * 密码找回API
 * 发送密码重置邮件
 */

session_start();
header('Content-Type: application/json; charset=utf-8');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit();
}

// 数据库配置
$dbPath = '../server/user_system.db3';

/**
 * 验证验证码
 */
function verifyCaptcha($inputCode, $sessionId) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $codeHash = hash('sha256', strtoupper($inputCode));
        
        $stmt = $pdo->prepare("
            SELECT id FROM captcha_codes 
            WHERE code_hash = ? AND session_id = ? 
            AND expires_at > datetime('now') AND is_used = 0
        ");
        $stmt->execute([$codeHash, $sessionId]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            // 标记验证码为已使用
            $stmt = $pdo->prepare("UPDATE captcha_codes SET is_used = 1 WHERE id = ?");
            $stmt->execute([$result['id']]);
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        error_log("验证码验证失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 检查用户名和邮箱组合是否存在
 */
function checkUserEmailExists($username, $email) {
    global $dbPath;

    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $stmt = $pdo->prepare("
            SELECT id, username, email FROM users
            WHERE username = ? AND email = ? AND status = 'active'
        ");
        $stmt->execute([$username, $email]);

        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        return $user ? $user : false;
    } catch (Exception $e) {
        error_log("检查用户邮箱组合失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 生成重置令牌
 */
function generateResetToken() {
    return bin2hex(random_bytes(32));
}

/**
 * 保存重置令牌
 */
function saveResetToken($userId, $username, $email, $token) {
    global $dbPath;

    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // 清除该用户的旧重置令牌
        $stmt = $pdo->prepare("DELETE FROM password_resets WHERE email = ?");
        $stmt->execute([$email]);

        // 保存新重置令牌（30分钟有效期）
        $expiresAt = date('Y-m-d H:i:s', time() + 1800); // 30分钟

        $stmt = $pdo->prepare("
            INSERT INTO password_resets (email, reset_token, expires_at, username, user_id)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$email, $token, $expiresAt, $username, $userId]);

        return true;
    } catch (Exception $e) {
        error_log("保存重置令牌失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 发送重置邮件
 */
function sendResetEmail($email, $token, $username = '') {
    // 使用PHP 7.3兼容版本
    require_once '../server/phpmailer-loader-php73.php';

    try {
        // 构建重置链接
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $resetLink = $protocol . "://" . $_SERVER['HTTP_HOST'] . "/reset-password.html?token=" . $token;

        $subject = "密码重置 - 倒计时系统";

        // HTML邮件模板
        $body = "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #4CAF50; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .button { display: inline-block; padding: 12px 24px; background: #4CAF50; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
                .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 15px 0; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>🔑 密码重置</h1>
                </div>
                <div class='content'>
                    <h2>您好！</h2>
                    <p>您请求重置倒计时系统的密码。请点击下面的按钮来重置您的密码：</p>

                    <div style='text-align: center;'>
                        <a href='{$resetLink}' class='button'>重置密码</a>
                    </div>

                    <p>或者复制以下链接到浏览器地址栏：</p>
                    <p style='word-break: break-all; background: #f0f0f0; padding: 10px; border-radius: 3px;'>{$resetLink}</p>

                    <div class='warning'>
                        <strong>⚠️ 重要提醒：</strong>
                        <ul>
                            <li>此链接将在 <strong>30分钟</strong> 后过期</li>
                            <li>为了您的账户安全，请尽快完成密码重置</li>
                            <li>如果您没有请求重置密码，请忽略此邮件</li>
                        </ul>
                    </div>
                </div>
                <div class='footer'>
                    <p>此邮件由倒计时系统自动发送，请勿回复。</p>
                    <p>发送时间：" . date('Y-m-d H:i:s') . "</p>
                </div>
            </div>
        </body>
        </html>
        ";

        // 发送邮件 (使用PHP 7.3兼容版本)
        $emailSender = new PHP73EmailSender();
        $result = $emailSender->sendPasswordResetEmail($email, $username, $token);

        // 记录日志
        $logMessage = date('Y-m-d H:i:s') . " - 密码重置邮件发送到: $email\n";
        $logMessage .= "用户名: $username\n";
        $logMessage .= "发送结果: " . ($result['success'] ? '成功' : '失败') . "\n";
        $logMessage .= "令牌: $token\n";
        if (!$result['success']) {
            $logMessage .= "错误信息: " . $result['message'] . "\n";
        }
        $logMessage .= "\n";

        file_put_contents('../server/password_reset.log', $logMessage, FILE_APPEND);

        return $result['success'];

    } catch (Exception $e) {
        // 记录错误日志
        $errorMessage = date('Y-m-d H:i:s') . " - 邮件发送异常: " . $e->getMessage() . "\n";
        $errorMessage .= "收件人: $email\n";
        $errorMessage .= "令牌: $token\n\n";

        file_put_contents('../server/password_reset.log', $errorMessage, FILE_APPEND);
        error_log("密码重置邮件发送失败: " . $e->getMessage());

        return false;
    }
}

/**
 * 记录重置请求日志
 */
function logResetRequest($email, $success, $reason = null) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("
            INSERT INTO login_logs (username, ip_address, user_agent, status, failure_reason)
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            "password_reset:$email",
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            $success ? 'password_reset_sent' : 'password_reset_failed',
            $reason
        ]);
    } catch (Exception $e) {
        error_log("记录重置日志失败: " . $e->getMessage());
    }
}

try {
    // 获取请求数据 - 支持JSON和表单数据
    $data = null;

    // 首先尝试JSON数据
    $input = file_get_contents('php://input');
    if (!empty($input)) {
        $data = json_decode($input, true);
    }

    // 如果JSON解析失败，尝试表单数据
    if (!$data && !empty($_POST)) {
        $data = $_POST;
    }

    if (!$data) {
        throw new Exception('无效的请求数据');
    }
    
    // 验证必需字段
    $requiredFields = ['username', 'email', 'captcha'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            throw new Exception("缺少必需字段: $field");
        }
    }

    $username = trim($data['username']);
    $email = trim($data['email']);
    $captcha = trim($data['captcha']);

    // 验证用户名格式
    if (strlen($username) < 3 || strlen($username) > 20) {
        throw new Exception('用户名长度应为3-20位');
    }

    if (!preg_match('/^[a-zA-Z0-9]+$/', $username)) {
        throw new Exception('用户名只能包含字母和数字');
    }
    
    // 验证邮箱格式
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('邮箱格式不正确');
    }
    
    // 验证验证码
    $sessionId = session_id();
    if (!verifyCaptcha($captcha, $sessionId)) {
        throw new Exception('验证码错误或已过期');
    }
    
    // 检查用户名和邮箱组合是否存在
    $user = checkUserEmailExists($username, $email);
    if (!$user) {
        // 为了安全，不透露用户信息是否存在，统一返回成功消息
        logResetRequest($email, false, '用户名和邮箱组合不存在');
        echo json_encode([
            'success' => true,
            'message' => '如果该邮箱已注册，您将收到密码重置邮件'
        ]);
        exit();
    }
    
    // 生成重置令牌
    $resetToken = generateResetToken();
    
    // 保存重置令牌
    if (!saveResetToken($user['id'], $user['username'], $user['email'], $resetToken)) {
        throw new Exception('保存重置令牌失败');
    }
    
    // 发送重置邮件
    if (!sendResetEmail($email, $resetToken, $user['username'])) {
        throw new Exception('发送重置邮件失败');
    }
    
    // 记录成功日志
    logResetRequest($email, true);
    
    echo json_encode([
        'success' => true,
        'message' => '密码重置邮件已发送，请检查您的邮箱',
        'debug_info' => [
            'email' => $email,
            'token' => $resetToken,
            'reset_link' => "reset-password.html?token=" . $resetToken
        ]
    ]);
    
} catch (Exception $e) {
    // 记录失败日志
    if (isset($email)) {
        logResetRequest($email, false, $e->getMessage());
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
