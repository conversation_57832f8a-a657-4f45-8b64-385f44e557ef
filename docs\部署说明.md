# 付费解锁系统 - 部署说明

## 📋 系统概述

本系统实现了湖南省省补倒计时页面的付费解锁机制，包含以下功能：

- 🔐 **锁屏页面**: 显示付款二维码和激活码输入
- 💰 **付费机制**: 支付宝收款19.9元
- 🔑 **激活码系统**: 10天有效期，一码一设备
- 📱 **设备绑定**: 基于设备指纹的唯一性验证
- 📊 **用户记录**: 完整的用户访问和付费记录

## 🚀 快速部署

### 1. 环境要求

- **Python 3.6+** (用于后端服务)
- **现代浏览器** (支持ES6+)
- **SQLite3** (Python内置)

### 2. 安装依赖

```bash
# 安装Python依赖
pip install flask flask-cors

# 或使用国内源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple flask flask-cors
```

### 3. 初始化数据库

```bash
# 运行数据库初始化脚本
python server/init_database.py
```

### 4. 生成激活码

```bash
# 运行激活码生成器
python server/generate_activation_code.py

# 选择选项1生成单个激活码，或选项2批量生成
```

### 5. 启动服务

```bash
# 启动后端服务
python server/app.py

# 服务将在 http://localhost:8080 启动
```

### 6. 访问系统

- **主页面**: http://localhost:8080/ (会自动跳转到锁屏页面)
- **锁屏页面**: http://localhost:8080/lock.html
- **统计API**: http://localhost:8080/api/stats

## 📁 文件结构

```
湖南省省补倒计时/
├── server/                     # 后端服务
│   ├── app.py                 # Flask主服务
│   ├── database_schema.sql    # 数据库结构
│   ├── init_database.py       # 数据库初始化
│   ├── generate_activation_code.py  # 激活码生成器
│   └── unlock_system.db       # SQLite数据库文件
├── js/                        # 前端JavaScript
│   ├── device-fingerprint.js  # 设备指纹识别
│   ├── lock-script.js         # 锁屏页面逻辑
│   └── unlock-check.js        # 主页面解锁检查
├── css/                       # 样式文件
│   ├── style.css             # 主页面样式
│   └── lock-style.css        # 锁屏页面样式
├── images/                    # 图片资源
│   ├── logo.png              # Logo图片
│   ├── alipay-qr.png         # 支付宝收款码
│   └── qq-qr.png             # QQ联系二维码
├── lock.html                  # 锁屏页面
├── index.php                  # 主页面
└── setup.py                   # 一键部署脚本
```

## 🔧 配置说明

### 1. 替换二维码图片

将以下占位图片替换为真实二维码：

- `images/alipay-qr.png` - 支付宝收款码（19.9元）
- `images/qq-qr.png` - QQ客服联系二维码

### 2. 修改收款金额

如需修改收款金额，请编辑以下文件：

- `lock.html` - 修改页面显示的价格
- `server/database_schema.sql` - 修改数据库默认金额
- `docs/部署说明.md` - 更新文档说明

### 3. 调整激活码有效期

在 `server/generate_activation_code.py` 中修改默认有效期：

```python
# 默认10天，可修改为其他天数
def create_activation_code(self, valid_days=10):
```

## 🎯 使用流程

### 用户端流程

1. **访问主页面** → 自动跳转到锁屏页面
2. **扫描支付宝二维码** → 付款19.9元
3. **扫描QQ二维码** → 添加客服QQ
4. **发送付款截图** → 联系客服获取激活码
5. **输入激活码** → 解锁访问主页面
6. **10天内有效** → 同一设备可重复访问

### 管理员流程

1. **生成激活码**:
   ```bash
   python server/generate_activation_code.py
   ```

2. **查看统计信息**:
   ```bash
   curl http://localhost:8080/api/stats
   ```

3. **查看数据库**:
   ```bash
   sqlite3 server/unlock_system.db
   .tables
   SELECT * FROM activation_codes;
   SELECT * FROM user_records;
   ```

## 🔒 安全特性

### 设备绑定机制

- **设备指纹**: 基于屏幕分辨率、浏览器信息、时区等生成唯一标识
- **一码一设备**: 激活码只能在首次使用的设备上有效
- **防止共享**: 无法在多个设备间共享激活码

### 数据安全

- **本地验证**: 支持离线验证已激活的设备
- **服务器验证**: 在线验证激活码有效性和设备绑定
- **过期检查**: 自动检查和清理过期激活码

## 📱 安卓电视适配

### 浏览器兼容性

- ✅ **Chrome for Android TV**
- ✅ **Android WebView**
- ✅ **Firefox for Android TV**
- ✅ **其他基于Chromium的浏览器**

### 界面适配

- **响应式设计**: 自适应不同屏幕尺寸
- **大字体**: 适合电视屏幕观看
- **简化操作**: 遥控器友好的界面设计

## 🛠️ 故障排除

### 常见问题

1. **数据库初始化失败**
   ```bash
   # 删除数据库文件重新初始化
   rm server/unlock_system.db
   python server/init_database.py
   ```

2. **激活码验证失败**
   - 检查网络连接
   - 确认激活码格式正确
   - 验证设备指纹是否匹配

3. **页面无法访问**
   - 确认服务器已启动
   - 检查端口8080是否被占用
   - 查看防火墙设置

### 日志查看

服务器运行时会显示详细日志：

```bash
python server/app.py
# 查看激活码验证、用户访问等日志信息
```

## 📞 技术支持

如遇到技术问题，请检查：

1. **系统日志**: 服务器控制台输出
2. **浏览器控制台**: F12开发者工具
3. **数据库状态**: SQLite数据库内容
4. **网络连接**: API请求是否正常

---

**🎉 部署完成后，系统即可正常使用！**
