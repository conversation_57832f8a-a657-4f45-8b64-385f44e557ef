<?php
/**
 * 修复后的密码找回功能测试
 * 验证邮件发送器修复是否成功
 */

session_start();
header('Content-Type: text/html; charset=utf-8');

// 获取当前会话ID和验证码
$sessionId = session_id();

function getCurrentCaptcha($sessionId) {
    $dbPath = '../server/user_system.db3';
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("
            SELECT code_value FROM captcha_codes 
            WHERE session_id = ? AND expires_at > datetime('now') AND is_used = 0
            ORDER BY created_at DESC LIMIT 1
        ");
        $stmt->execute([$sessionId]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['code_value'] : null;
    } catch (Exception $e) {
        return null;
    }
}

$currentCaptcha = getCurrentCaptcha($sessionId);

// 处理测试请求
$testResult = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_forgot_password'])) {
    $username = $_POST['username'] ?? '';
    $email = $_POST['email'] ?? '';
    $captcha = $_POST['captcha'] ?? '';
    
    // 构建POST数据
    $postData = json_encode([
        'username' => $username,
        'email' => $email,
        'captcha' => $captcha
    ]);
    
    // 发送API请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://promo.xxgogo.ggff.net:8866/api/forgot-password.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Cookie: PHPSESSID=' . $sessionId
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $testResult = [
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error,
        'post_data' => $postData
    ];
}

// 检查邮件配置
function checkEmailConfig() {
    try {
        require_once '../server/email-config.php';
        return validateEmailConfig();
    } catch (Exception $e) {
        return ['valid' => false, 'error' => $e->getMessage()];
    }
}

$emailConfigStatus = checkEmailConfig();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复后的密码找回功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
        input, button { padding: 8px; margin: 5px; }
        button { background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
        .fix-info { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
    </style>
</head>
<body>
    <h1>🔧 修复后的密码找回功能测试</h1>
    <p><strong>测试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="fix-info">
        <h3>🎯 修复内容</h3>
        <p><strong>问题:</strong> PHPMailer依赖文件缺失，已移植到本项目vendor目录</p>
        <p><strong>解决方案:</strong> 将邮件发送器从 <code>phpmailer-email-sender.php</code> 改为使用现有的 <code>email-sender.php</code></p>
        <p><strong>修改文件:</strong> <code>api/forgot-password.php</code> 第120行和第180行</p>
    </div>
    
    <div class="test-section">
        <h2>📊 邮件配置状态</h2>
        <?php if ($emailConfigStatus['valid']): ?>
            <p class="success">✅ 邮件配置有效</p>
            <p><strong>服务商:</strong> <?php echo $emailConfigStatus['provider']; ?></p>
            <p><strong>SMTP主机:</strong> <?php echo $emailConfigStatus['smtp_host']; ?></p>
            <p><strong>发件人:</strong> <?php echo $emailConfigStatus['from_email']; ?></p>
        <?php else: ?>
            <p class="error">❌ 邮件配置无效</p>
            <p><strong>错误:</strong> <?php echo $emailConfigStatus['error']; ?></p>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>📊 会话信息</h2>
        <p><strong>当前会话ID:</strong> <?php echo $sessionId; ?></p>
        <p><strong>当前有效验证码:</strong> 
            <?php if ($currentCaptcha): ?>
                <span class="success"><?php echo $currentCaptcha; ?></span>
            <?php else: ?>
                <span class="error">无有效验证码</span>
                <a href="../api/captcha.php" target="_blank">生成验证码</a>
            <?php endif; ?>
        </p>
    </div>
    
    <div class="test-section">
        <h2>🧪 密码找回API测试</h2>
        <form method="POST">
            <div>
                <label>用户名:</label>
                <input type="text" name="username" value="<?php echo $_POST['username'] ?? 'testuser'; ?>" required>
            </div>
            <div>
                <label>邮箱:</label>
                <input type="email" name="email" value="<?php echo $_POST['email'] ?? '<EMAIL>'; ?>" required>
            </div>
            <div>
                <label>验证码:</label>
                <input type="text" name="captcha" value="<?php echo $currentCaptcha ?? ''; ?>" required>
                <small>(当前有效验证码已自动填入)</small>
            </div>
            <div>
                <button type="submit" name="test_forgot_password">测试密码找回功能</button>
            </div>
        </form>
    </div>
    
    <?php if ($testResult): ?>
    <div class="test-section">
        <h2>📋 测试结果</h2>
        
        <h3>HTTP状态码</h3>
        <p class="<?php echo $testResult['http_code'] == 200 ? 'success' : ($testResult['http_code'] == 400 ? 'warning' : 'error'); ?>">
            <?php echo $testResult['http_code']; ?>
            <?php 
            switch($testResult['http_code']) {
                case 200: echo ' - 请求成功'; break;
                case 400: echo ' - 请求错误 (可能是验证码或用户信息问题)'; break;
                case 500: echo ' - 服务器内部错误'; break;
                default: echo ' - 其他错误'; break;
            }
            ?>
        </p>
        
        <h3>发送的数据</h3>
        <pre><?php echo htmlspecialchars($testResult['post_data']); ?></pre>
        
        <h3>API响应</h3>
        <pre><?php echo htmlspecialchars($testResult['response']); ?></pre>
        
        <?php if ($testResult['error']): ?>
        <h3>cURL错误</h3>
        <p class="error"><?php echo htmlspecialchars($testResult['error']); ?></p>
        <?php endif; ?>
        
        <?php
        // 尝试解析JSON响应
        $jsonResponse = json_decode($testResult['response'], true);
        if ($jsonResponse):
        ?>
        <h3>解析后的响应</h3>
        <pre><?php echo htmlspecialchars(json_encode($jsonResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
        
        <?php if (isset($jsonResponse['success'])): ?>
        <h3>结果分析</h3>
        <?php if ($jsonResponse['success']): ?>
            <p class="success">✅ 密码找回请求成功！</p>
            <p><?php echo htmlspecialchars($jsonResponse['message']); ?></p>
            <?php if (isset($jsonResponse['debug_info'])): ?>
                <h4>调试信息:</h4>
                <pre><?php echo htmlspecialchars(json_encode($jsonResponse['debug_info'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
            <?php endif; ?>
        <?php else: ?>
            <p class="error">❌ 密码找回请求失败</p>
            <p><strong>错误信息:</strong> <?php echo htmlspecialchars($jsonResponse['message']); ?></p>
        <?php endif; ?>
        <?php endif; ?>
        
        <?php endif; ?>
    </div>
    <?php endif; ?>
    
    <div class="test-section">
        <h2>📋 错误日志检查</h2>
        <?php
        // 检查PHP错误日志
        $phpErrorLog = 'D:/error_log/php/php_errors.log';
        if (file_exists($phpErrorLog)) {
            echo "<h3>PHP错误日志 (最后10行):</h3>";
            $lines = file($phpErrorLog);
            if ($lines) {
                $lastLines = array_slice($lines, -10);
                echo "<pre>";
                foreach ($lastLines as $line) {
                    if (strpos($line, 'phpmailer-email-sender') !== false) {
                        echo '<span class="error">' . htmlspecialchars($line) . '</span>';
                    } else {
                        echo htmlspecialchars($line);
                    }
                }
                echo "</pre>";
            }
        } else {
            echo "<p class='warning'>PHP错误日志文件不存在</p>";
        }
        
        // 检查密码重置日志
        $resetLog = '../server/password_reset.log';
        if (file_exists($resetLog)) {
            echo "<h3>密码重置日志 (最后5条):</h3>";
            $lines = file($resetLog);
            if ($lines) {
                $lastLines = array_slice($lines, -15); // 每条记录约3行
                echo "<pre>";
                foreach ($lastLines as $line) {
                    echo htmlspecialchars($line);
                }
                echo "</pre>";
            }
        } else {
            echo "<p class='info'>密码重置日志文件不存在 (正常，首次使用时会创建)</p>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关链接</h2>
        <ul>
            <li><a href="error-log-test.php">PHP错误日志测试</a></li>
            <li><a href="debug-session-captcha.php">会话和验证码调试</a></li>
            <li><a href="../new-lock.html">返回登录页面</a></li>
            <li><a href="../api/captcha.php" target="_blank">生成验证码</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
