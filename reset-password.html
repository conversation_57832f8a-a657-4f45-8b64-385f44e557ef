<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重置密码 - 倒计时系统</title>
    <link rel="stylesheet" href="css/lock-style.css">
    <style>
        .reset-container {
            max-width: 400px;
            margin: 50px auto;
            padding: 30px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 15px;
            margin: 8px 0;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
            font-size: 16px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
        }
        
        .action-button {
            width: 100%;
            padding: 15px;
            margin: 15px 0;
            border: none;
            border-radius: 8px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .action-button:hover {
            background: linear-gradient(135deg, #45a049, #4CAF50);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }
        
        .action-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .status-message {
            margin: 15px 0;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }
        
        .status-success {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
            border: 1px solid rgba(76, 175, 80, 0.5);
        }
        
        .status-error {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 1px solid rgba(244, 67, 54, 0.5);
        }
        
        .spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .back-link {
            display: inline-block;
            margin-top: 20px;
            color: #4CAF50;
            text-decoration: none;
            font-size: 14px;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="lock-container">
        <!-- 背景动画 -->
        <div class="animated-bg"></div>
        
        <div class="reset-container">
            <h1>🔑 重置密码</h1>
            
            <div id="resetForm">
                <p>请输入新密码</p>
                
                <input type="password" id="newPassword" class="form-input" placeholder="新密码 (至少6位)" maxlength="50">
                <input type="password" id="confirmPassword" class="form-input" placeholder="确认新密码" maxlength="50">
                
                <button id="resetBtn" class="action-button">
                    <span class="button-text">重置密码</span>
                    <span class="button-loading" style="display: none;">
                        <span class="spinner"></span>
                        重置中...
                    </span>
                </button>
                
                <div id="statusMessage" class="status-message" style="display: none;"></div>
                
                <a href="new-lock.html" class="back-link">返回登录页面</a>
            </div>
        </div>
    </div>

    <script>
        class PasswordReset {
            constructor() {
                this.token = this.getTokenFromUrl();
                this.init();
            }
            
            init() {
                if (!this.token) {
                    this.showMessage('无效的重置链接', 'error');
                    document.getElementById('resetForm').style.display = 'none';
                    return;
                }
                
                this.bindEvents();
            }
            
            getTokenFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('token');
            }
            
            bindEvents() {
                const resetBtn = document.getElementById('resetBtn');
                resetBtn.addEventListener('click', () => this.handleReset());
                
                // 回车键支持
                document.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.handleReset();
                    }
                });
            }
            
            async handleReset() {
                const newPassword = document.getElementById('newPassword').value.trim();
                const confirmPassword = document.getElementById('confirmPassword').value.trim();
                
                if (!newPassword || !confirmPassword) {
                    this.showMessage('请填写完整信息', 'error');
                    return;
                }
                
                if (newPassword.length < 6) {
                    this.showMessage('密码长度至少6位', 'error');
                    return;
                }
                
                if (newPassword !== confirmPassword) {
                    this.showMessage('两次输入的密码不一致', 'error');
                    return;
                }
                
                this.setButtonLoading(true);
                
                try {
                    const response = await fetch('api/reset-password.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            token: this.token,
                            new_password: newPassword
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        this.showMessage('密码重置成功！3秒后跳转到登录页面', 'success');
                        setTimeout(() => {
                            window.location.href = 'new-lock.html';
                        }, 3000);
                    } else {
                        this.showMessage(data.message, 'error');
                    }
                } catch (error) {
                    console.error('❌ 重置失败:', error);
                    this.showMessage('网络错误，请重试', 'error');
                } finally {
                    this.setButtonLoading(false);
                }
            }
            
            setButtonLoading(loading) {
                const button = document.getElementById('resetBtn');
                const textSpan = button.querySelector('.button-text');
                const loadingSpan = button.querySelector('.button-loading');
                
                if (loading) {
                    button.disabled = true;
                    textSpan.style.display = 'none';
                    loadingSpan.style.display = 'inline';
                } else {
                    button.disabled = false;
                    textSpan.style.display = 'inline';
                    loadingSpan.style.display = 'none';
                }
            }
            
            showMessage(message, type = 'info') {
                const messageDiv = document.getElementById('statusMessage');
                messageDiv.textContent = message;
                messageDiv.className = `status-message status-${type}`;
                messageDiv.style.display = 'block';
                
                // 自动隐藏成功消息
                if (type === 'success') {
                    setTimeout(() => this.clearMessage(), 5000);
                }
            }
            
            clearMessage() {
                const messageDiv = document.getElementById('statusMessage');
                messageDiv.style.display = 'none';
                messageDiv.textContent = '';
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            new PasswordReset();
        });
    </script>
</body>
</html>
