@echo off
echo ========================================
echo Windows IIS PHP 目录设置脚本
echo ========================================
echo.

echo 正在创建PHP错误日志目录...
if not exist "D:\error_log" mkdir "D:\error_log"
if not exist "D:\error_log\php" mkdir "D:\error_log\php"
echo 已创建: D:\error_log\php\

echo.
echo 正在创建PHP临时文件目录...
if not exist "D:\temp" mkdir "D:\temp"
if not exist "D:\temp\php" mkdir "D:\temp\php"
if not exist "D:\temp\php\sessions" mkdir "D:\temp\php\sessions"
if not exist "D:\temp\php\uploads" mkdir "D:\temp\php\uploads"
echo 已创建: D:\temp\php\sessions\
echo 已创建: D:\temp\php\uploads\

echo.
echo 正在设置目录权限...
echo 为 IIS_IUSRS 用户组设置权限...

icacls "D:\error_log\php" /grant "IIS_IUSRS:(OI)(CI)F" /T
icacls "D:\temp\php" /grant "IIS_IUSRS:(OI)(CI)F" /T

echo.
echo 为 IUSR 用户设置权限...
icacls "D:\error_log\php" /grant "IUSR:(OI)(CI)F" /T
icacls "D:\temp\php" /grant "IUSR:(OI)(CI)F" /T

echo.
echo 为 Everyone 用户组设置权限 (临时调试用)...
icacls "D:\error_log\php" /grant "Everyone:(OI)(CI)F" /T
icacls "D:\temp\php" /grant "Everyone:(OI)(CI)F" /T

echo.
echo ========================================
echo 目录创建和权限设置完成！
echo ========================================
echo.
echo 创建的目录：
echo - D:\error_log\php\          (PHP错误日志)
echo - D:\temp\php\sessions\      (PHP会话文件)
echo - D:\temp\php\uploads\       (PHP上传临时文件)
echo.
echo 下一步：
echo 1. 将修改后的 php.ini 文件复制到 PHP 安装目录
echo 2. 执行 iisreset 重启 IIS 服务
echo 3. 访问 phpinfo.php 验证配置
echo.
pause
