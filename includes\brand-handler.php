<?php
/**
 * 品牌处理核心文件
 * 处理品牌路由、图片路径和页面配置
 */

// 引入品牌配置
require_once __DIR__ . '/../config/brands.php';

/**
 * 品牌处理类
 */
class BrandHandler {
    private $currentBrand = null;
    private $brandPath = null;
    
    public function __construct() {
        $this->initializeBrand();
    }
    
    /**
     * 初始化品牌信息
     */
    private function initializeBrand() {
        // 优先从环境变量获取品牌路径（由路由器设置）
        if (isset($_SERVER['BRAND_PATH'])) {
            $this->brandPath = $_SERVER['BRAND_PATH'];
        } else {
            // 从URL解析品牌
            $requestUri = $_SERVER['REQUEST_URI'] ?? '';
            $this->brandPath = parseBrandFromUrl($requestUri);
        }

        // 如果没有找到有效品牌，使用默认品牌
        if (!$this->brandPath || !isValidBrand($this->brandPath)) {
            $this->brandPath = 'wuling';
        }

        // 获取品牌信息
        $this->currentBrand = getBrandInfo($this->brandPath);

        // 如果品牌信息无效，使用默认品牌
        if (!$this->currentBrand) {
            $this->brandPath = 'wuling';
            $this->currentBrand = getDefaultBrand();
        }
    }
    
    /**
     * 获取当前品牌信息
     * @return array 品牌信息
     */
    public function getCurrentBrand() {
        return $this->currentBrand;
    }
    
    /**
     * 获取当前品牌路径
     * @return string 品牌路径
     */
    public function getBrandPath() {
        return $this->brandPath;
    }
    
    /**
     * 获取品牌名称
     * @return string 品牌名称
     */
    public function getBrandName() {
        return $this->currentBrand['name'];
    }
    
    /**
     * 获取品牌英文名称
     * @return string 品牌英文名称
     */
    public function getBrandEnglishName() {
        return $this->currentBrand['english_name'];
    }
    
    /**
     * 获取Logo路径
     * @return string Logo图片路径
     */
    public function getLogoPath() {
        $logoPath = $this->currentBrand['logo'];
        
        // 检查文件是否存在
        if (file_exists($logoPath)) {
            return $logoPath;
        }
        
        // 如果品牌logo不存在，使用默认logo
        if (file_exists('images/logo.png')) {
            return 'images/logo.png';
        }
        
        // 最后的备用路径
        return 'uploads/logo.png';
    }
    
    /**
     * 获取汽车图片路径
     * @return string 汽车图片路径
     */
    public function getCarImagePath() {
        $carPath = $this->currentBrand['car_image'];
        
        // 检查文件是否存在
        if (file_exists($carPath)) {
            return $carPath;
        }
        
        // 如果品牌汽车图片不存在，使用默认图片
        if (file_exists('images/upload1.png')) {
            return 'images/upload1.png';
        }
        
        // 最后的备用路径
        return 'uploads/car.png';
    }
    
    /**
     * 获取品牌主题色
     * @return string 主题色代码
     */
    public function getThemeColor() {
        return $this->currentBrand['color_theme'];
    }
    
    /**
     * 获取品牌描述
     * @return string 品牌描述
     */
    public function getBrandDescription() {
        return $this->currentBrand['description'];
    }
    
    /**
     * 生成页面标题（固定为湖南省省补）
     * @param string $suffix 标题后缀
     * @return string 完整页面标题
     */
    public function getPageTitle($suffix = '') {
        $title = '湖南省省补倒计时';
        if ($suffix) {
            $title .= ' - ' . $suffix;
        }
        return $title;
    }
    
    /**
     * 生成品牌相关的CSS变量
     * @return string CSS变量定义
     */
    public function getBrandCssVariables() {
        return "
        :root {
            --brand-color: {$this->getThemeColor()};
            --brand-name: '{$this->getBrandName()}';
        }
        ";
    }
    
    /**
     * 检查是否为TV优化版
     * @return bool 是否为TV版
     */
    public function isTvVersion() {
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        return strpos($requestUri, 'index-tv-optimized.php') !== false;
    }
    
    /**
     * 生成切换版本的URL
     * @return string 切换版本的URL
     */
    public function getToggleVersionUrl() {
        if ($this->isTvVersion()) {
            return "/index.php?brand={$this->brandPath}";
        } else {
            return "/index-tv-optimized.php?brand={$this->brandPath}";
        }
    }
    
    /**
     * 获取切换版本的文本
     * @return string 切换版本的文本
     */
    public function getToggleVersionText() {
        if ($this->isTvVersion()) {
            return '💻 切换到标准版';
        } else {
            return '📺 切换到TV优化版';
        }
    }
    
    /**
     * 记录品牌访问日志
     */
    public function logBrandAccess() {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'brand' => $this->brandPath,
            'brand_name' => $this->getBrandName(),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
        ];
        
        // 这里可以添加日志记录逻辑
        // 例如写入文件或数据库
        error_log('Brand Access: ' . json_encode($logData));
    }
}

/**
 * 全局品牌处理器实例
 */
$brandHandler = new BrandHandler();

// 记录访问日志
$brandHandler->logBrandAccess();
?>
