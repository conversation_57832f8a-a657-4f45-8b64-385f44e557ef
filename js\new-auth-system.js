/**
 * 新的用户认证系统
 * 支持用户注册、登录、激活码绑定
 */

class NewAuthSystem {
    constructor() {
        this.currentUser = null;
        this.sessionToken = null;
        this.init();
    }

    init() {
        console.log('🚀 新认证系统初始化...');
        
        // 绑定事件
        this.bindEvents();
        
        // 检查现有会话
        this.checkExistingSession();
    }

    bindEvents() {
        // 登录按钮
        const loginBtn = document.getElementById('loginBtn');
        if (loginBtn) {
            loginBtn.addEventListener('click', () => this.handleLogin());
        }

        // 注册按钮
        const registerBtn = document.getElementById('registerBtn');
        if (registerBtn) {
            registerBtn.addEventListener('click', () => this.handleRegister());
        }

        // 绑定激活码按钮
        const bindBtn = document.getElementById('bindBtn');
        if (bindBtn) {
            bindBtn.addEventListener('click', () => this.handleBindActivation());
        }

        // 发送重置邮件按钮
        const sendResetBtn = document.getElementById('sendResetBtn');
        if (sendResetBtn) {
            sendResetBtn.addEventListener('click', () => this.handleForgotPassword());
        }

        // 回车键支持
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const activeForm = this.getActiveForm();
                if (activeForm === 'login') {
                    this.handleLogin();
                } else if (activeForm === 'register') {
                    this.handleRegister();
                } else if (activeForm === 'activation') {
                    this.handleBindActivation();
                } else if (activeForm === 'forgot') {
                    this.handleForgotPassword();
                }
            }
        });
    }

    getActiveForm() {
        if (document.getElementById('loginForm').style.display !== 'none') {
            return 'login';
        } else if (document.getElementById('registerForm').style.display !== 'none') {
            return 'register';
        } else if (document.getElementById('activationForm').style.display !== 'none') {
            return 'activation';
        } else if (document.getElementById('forgotPasswordForm').style.display !== 'none') {
            return 'forgot';
        }
        return null;
    }

    async checkExistingSession() {
        const sessionToken = this.getStoredSessionToken();
        if (!sessionToken) {
            return;
        }

        try {
            const response = await fetch('api/verify-session.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ session_token: sessionToken })
            });

            const data = await response.json();

            if (data.success) {
                // 会话有效且有激活码，直接跳转
                console.log('✅ 检测到有效会话，跳转到主页面');
                this.redirectToMainPage();
            } else if (data.code === 'NEED_ACTIVATION') {
                // 会话有效但需要激活码
                console.log('⚠️ 需要绑定激活码');
                this.currentUser = data.data;
                this.sessionToken = sessionToken;
                this.showActivationForm();
            } else {
                // 会话无效，清除存储
                this.clearStoredSession();
            }
        } catch (error) {
            console.error('❌ 检查会话失败:', error);
            this.clearStoredSession();
        }
    }

    async handleLogin() {
        const username = document.getElementById('loginUsername').value.trim();
        const password = document.getElementById('loginPassword').value.trim();

        if (!username || !password) {
            this.showMessage('请填写用户名和密码', 'error');
            return;
        }

        this.setButtonLoading('loginBtn', true);

        try {
            // 获取CSRF令牌
            const csrfToken = await this.getCSRFToken();

            const response = await fetch('api/login.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password,
                    device_info: this.getDeviceInfo(),
                    csrf_token: csrfToken
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showMessage('登录成功！', 'success');
                this.currentUser = data.data;
                this.sessionToken = data.data.session_token;
                this.storeSessionToken(this.sessionToken);
                
                // 检查是否需要绑定激活码
                setTimeout(() => this.checkActivationStatus(), 1000);
            } else {
                this.showMessage(data.message, 'error');
            }
        } catch (error) {
            console.error('❌ 登录失败:', error);
            this.showMessage('网络错误，请重试', 'error');
        } finally {
            this.setButtonLoading('loginBtn', false);
        }
    }

    async handleRegister() {
        const username = document.getElementById('regUsername').value.trim();
        const password = document.getElementById('regPassword').value.trim();
        const email = document.getElementById('regEmail').value.trim();
        const captcha = document.getElementById('regCaptcha').value.trim();

        if (!username || !password || !email || !captcha) {
            this.showMessage('请填写完整信息', 'error');
            return;
        }

        this.setButtonLoading('registerBtn', true);

        try {
            const response = await fetch('api/register.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password,
                    email: email,
                    captcha: captcha
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showMessage('注册成功！请登录', 'success');
                setTimeout(() => this.showLoginForm(), 2000);
            } else {
                this.showMessage(data.message, 'error');
                this.refreshCaptcha('register');
            }
        } catch (error) {
            console.error('❌ 注册失败:', error);
            this.showMessage('网络错误，请重试', 'error');
        } finally {
            this.setButtonLoading('registerBtn', false);
        }
    }

    async handleForgotPassword() {
        const username = document.getElementById('forgotUsername').value.trim();
        const email = document.getElementById('forgotEmail').value.trim();
        const captcha = document.getElementById('forgotCaptcha').value.trim();

        if (!username || !email || !captcha) {
            this.showMessage('请填写完整信息（用户名、邮箱、验证码）', 'error');
            return;
        }

        if (username.length < 3 || username.length > 20) {
            this.showMessage('用户名长度应为3-20位', 'error');
            return;
        }

        if (!username.match(/^[a-zA-Z0-9]+$/)) {
            this.showMessage('用户名只能包含字母和数字', 'error');
            return;
        }

        if (!email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
            this.showMessage('邮箱格式不正确', 'error');
            return;
        }

        this.setButtonLoading('sendResetBtn', true);

        try {
            const response = await fetch('api/forgot-password.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    email: email,
                    captcha: captcha
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showMessage(data.message, 'success');
                if (data.debug_info) {
                    // 开发环境显示调试信息
                    console.log('🔧 调试信息:', data.debug_info);
                    this.showMessage(
                        data.message + '\n\n调试信息：重置链接已生成，请查看控制台',
                        'success'
                    );
                }
                this.refreshCaptcha('forgot');
            } else {
                this.showMessage(data.message, 'error');
                this.refreshCaptcha('forgot');
            }
        } catch (error) {
            console.error('❌ 发送重置邮件失败:', error);
            this.showMessage('网络错误，请重试', 'error');
        } finally {
            this.setButtonLoading('sendResetBtn', false);
        }
    }

    async handleBindActivation() {
        const activationCode = document.getElementById('activationCode').value.trim();

        if (!activationCode) {
            this.showMessage('请输入激活码', 'error');
            return;
        }

        if (!activationCode.match(/^[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}$/)) {
            this.showMessage('激活码格式不正确', 'error');
            return;
        }

        this.setButtonLoading('bindBtn', true);

        try {
            const response = await fetch('api/bind-activation.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    activation_code: activationCode,
                    session_token: this.sessionToken
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showMessage('激活码绑定成功！正在跳转...', 'success');
                setTimeout(() => this.redirectToMainPage(), 2000);
            } else {
                this.showMessage(data.message, 'error');
            }
        } catch (error) {
            console.error('❌ 绑定激活码失败:', error);
            this.showMessage('网络错误，请重试', 'error');
        } finally {
            this.setButtonLoading('bindBtn', false);
        }
    }

    async checkActivationStatus() {
        try {
            const response = await fetch('api/verify-session.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ session_token: this.sessionToken })
            });

            const data = await response.json();

            if (data.success) {
                // 有有效激活码，跳转主页面
                this.redirectToMainPage();
            } else if (data.code === 'NEED_ACTIVATION') {
                // 需要绑定激活码
                this.showActivationForm();
            } else {
                this.showMessage('会话验证失败，请重新登录', 'error');
                this.logout();
            }
        } catch (error) {
            console.error('❌ 检查激活状态失败:', error);
            this.showActivationForm(); // 默认显示激活表单
        }
    }

    showLoginForm() {
        document.getElementById('loginForm').style.display = 'block';
        document.getElementById('registerForm').style.display = 'none';
        document.getElementById('activationForm').style.display = 'none';
        document.getElementById('forgotPasswordForm').style.display = 'none';
        this.clearMessage();
    }

    showRegisterForm() {
        document.getElementById('loginForm').style.display = 'none';
        document.getElementById('registerForm').style.display = 'block';
        document.getElementById('activationForm').style.display = 'none';
        document.getElementById('forgotPasswordForm').style.display = 'none';
        this.clearMessage();
        this.refreshCaptcha('register');
    }

    showForgotPasswordForm() {
        document.getElementById('loginForm').style.display = 'none';
        document.getElementById('registerForm').style.display = 'none';
        document.getElementById('activationForm').style.display = 'none';
        document.getElementById('forgotPasswordForm').style.display = 'block';
        this.clearMessage();
        this.refreshCaptcha('forgot');
    }

    showActivationForm() {
        document.getElementById('loginForm').style.display = 'none';
        document.getElementById('registerForm').style.display = 'none';
        document.getElementById('activationForm').style.display = 'block';
        document.getElementById('forgotPasswordForm').style.display = 'none';

        if (this.currentUser) {
            document.getElementById('currentUsername').textContent = this.currentUser.username;
        }

        this.clearMessage();
    }

    refreshCaptcha(type) {
        const img = document.getElementById(type + 'CaptchaImg');
        if (img) {
            img.src = 'api/captcha.php?' + Date.now();
        }
    }

    setButtonLoading(buttonId, loading) {
        const button = document.getElementById(buttonId);
        if (!button) return;

        const textSpan = button.querySelector('.button-text');
        const loadingSpan = button.querySelector('.button-loading');

        if (loading) {
            button.disabled = true;
            if (textSpan) textSpan.style.display = 'none';
            if (loadingSpan) loadingSpan.style.display = 'inline';
        } else {
            button.disabled = false;
            if (textSpan) textSpan.style.display = 'inline';
            if (loadingSpan) loadingSpan.style.display = 'none';
        }
    }

    showMessage(message, type = 'info') {
        const messageDiv = document.getElementById('statusMessage');
        if (!messageDiv) return;

        messageDiv.textContent = message;
        messageDiv.className = `status-message status-${type}`;
        messageDiv.style.display = 'block';

        // 自动隐藏成功消息
        if (type === 'success') {
            setTimeout(() => this.clearMessage(), 3000);
        }
    }

    clearMessage() {
        const messageDiv = document.getElementById('statusMessage');
        if (messageDiv) {
            messageDiv.style.display = 'none';
            messageDiv.textContent = '';
        }
    }

    getDeviceInfo() {
        return `${navigator.platform} | ${navigator.userAgent.split(' ')[0]}`;
    }

    async getCSRFToken() {
        try {
            const response = await fetch('api/get-csrf-token.php', {
                method: 'GET',
                credentials: 'same-origin'
            });

            const data = await response.json();

            if (data.success) {
                return data.csrf_token;
            } else {
                throw new Error('获取CSRF令牌失败');
            }
        } catch (error) {
            console.error('❌ 获取CSRF令牌失败:', error);
            throw error;
        }
    }

    getStoredSessionToken() {
        return localStorage.getItem('session_token') || 
               sessionStorage.getItem('session_token') ||
               this.getCookie('session_token');
    }

    storeSessionToken(token) {
        localStorage.setItem('session_token', token);
        sessionStorage.setItem('session_token', token);
    }

    clearStoredSession() {
        localStorage.removeItem('session_token');
        sessionStorage.removeItem('session_token');
        this.deleteCookie('session_token');
        this.currentUser = null;
        this.sessionToken = null;
    }

    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    }

    deleteCookie(name) {
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    }

    logout() {
        this.clearStoredSession();
        this.showLoginForm();
        this.showMessage('已退出登录', 'info');
    }

    redirectToMainPage() {
        console.log('🎉 登录成功，跳转到品牌选择页面');
        // 设置登录状态标识
        sessionStorage.setItem('user_logged_in', 'true');
        window.location.href = 'brand-selector.html';
    }
}

// 全局函数供HTML调用
function showLoginForm() {
    window.authSystem.showLoginForm();
}

function showRegisterForm() {
    window.authSystem.showRegisterForm();
}

function showForgotPasswordForm() {
    window.authSystem.showForgotPasswordForm();
}

function refreshCaptcha(type) {
    window.authSystem.refreshCaptcha(type);
}

function logout() {
    window.authSystem.logout();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.authSystem = new NewAuthSystem();
});
