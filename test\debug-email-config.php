<?php
/**
 * 邮件配置调试脚本
 * 检查邮件配置的实际状态
 */

header('Content-Type: text/html; charset=utf-8');

echo "<h1>📧 邮件配置调试</h1>";
echo "<p><strong>调试时间:</strong> " . date('Y-m-d H:i:s') . "</p>";

echo "<h2>🔍 步骤1: 直接包含配置文件</h2>";
try {
    require_once '../server/email-config.php';
    echo "<p style='color: green;'>✅ 配置文件加载成功</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 配置文件加载失败: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>🔍 步骤2: 检查全局变量</h2>";
echo "<p><strong>\$emailConfig 是否存在:</strong> " . (isset($emailConfig) ? '是' : '否') . "</p>";

if (isset($emailConfig)) {
    echo "<p><strong>配置数组内容:</strong></p>";
    echo "<pre>" . htmlspecialchars(print_r($emailConfig, true)) . "</pre>";
    
    echo "<p><strong>Provider值:</strong> ";
    if (isset($emailConfig['provider'])) {
        echo "'" . htmlspecialchars($emailConfig['provider']) . "'";
        echo " (长度: " . strlen($emailConfig['provider']) . ")";
    } else {
        echo "未设置";
    }
    echo "</p>";
} else {
    echo "<p style='color: red;'>❌ \$emailConfig 变量不存在</p>";
}

echo "<h2>🔍 步骤3: 测试 getEmailConfig() 函数</h2>";
try {
    $config = getEmailConfig();
    echo "<p style='color: green;'>✅ getEmailConfig() 调用成功</p>";
    echo "<p><strong>返回的配置:</strong></p>";
    echo "<pre>" . htmlspecialchars(print_r($config, true)) . "</pre>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ getEmailConfig() 调用失败</p>";
    echo "<p><strong>错误信息:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>错误文件:</strong> " . $e->getFile() . ":" . $e->getLine() . "</p>";
}

echo "<h2>🔍 步骤4: 测试 EmailSender 类</h2>";
try {
    require_once '../server/email-sender.php';
    echo "<p style='color: green;'>✅ EmailSender 类文件加载成功</p>";
    
    $emailSender = new EmailSender();
    echo "<p style='color: green;'>✅ EmailSender 对象创建成功</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ EmailSender 测试失败</p>";
    echo "<p><strong>错误信息:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>错误文件:</strong> " . $e->getFile() . ":" . $e->getLine() . "</p>";
}

echo "<h2>🔍 步骤5: 检查163邮箱配置</h2>";
if (isset($emailConfig) && isset($emailConfig['163'])) {
    $config163 = $emailConfig['163'];
    echo "<p><strong>163邮箱配置:</strong></p>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>字段</th><th>值</th><th>状态</th></tr>";
    
    $fields = ['smtp_host', 'smtp_port', 'smtp_secure', 'username', 'password', 'from_email', 'from_name'];
    foreach ($fields as $field) {
        echo "<tr>";
        echo "<td><strong>$field</strong></td>";
        if (isset($config163[$field])) {
            $value = $config163[$field];
            if ($field === 'password') {
                $displayValue = str_repeat('*', strlen($value));
            } else {
                $displayValue = htmlspecialchars($value);
            }
            echo "<td>$displayValue</td>";
            echo "<td style='color: " . (empty($value) ? 'red' : 'green') . ";'>";
            echo empty($value) ? '❌ 空值' : '✅ 已设置';
            echo "</td>";
        } else {
            echo "<td>未设置</td>";
            echo "<td style='color: red;'>❌ 缺失</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ 163邮箱配置不存在</p>";
}

echo "<h2>🔧 修复建议</h2>";

// 检查可能的问题
$issues = [];

if (!isset($emailConfig)) {
    $issues[] = "全局变量 \$emailConfig 不存在";
} else {
    if (!isset($emailConfig['provider']) || empty($emailConfig['provider'])) {
        $issues[] = "provider 字段未设置或为空";
    }
    
    if (isset($emailConfig['provider'])) {
        $provider = $emailConfig['provider'];
        if (!isset($emailConfig[$provider])) {
            $issues[] = "指定的邮件服务商配置不存在: $provider";
        } else {
            $config = $emailConfig[$provider];
            $required = ['smtp_host', 'smtp_port', 'username', 'password', 'from_email'];
            foreach ($required as $field) {
                if (empty($config[$field])) {
                    $issues[] = "163邮箱配置缺少字段: $field";
                }
            }
        }
    }
}

if (empty($issues)) {
    echo "<p style='color: green;'>✅ 邮件配置看起来正常</p>";
} else {
    echo "<p style='color: red;'>❌ 发现以下问题:</p>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li style='color: red;'>$issue</li>";
    }
    echo "</ul>";
    
    echo "<h3>修复步骤:</h3>";
    echo "<ol>";
    echo "<li>检查 server/email-config.php 文件是否完整</li>";
    echo "<li>确保 provider 字段设置为 '163'</li>";
    echo "<li>确保163邮箱配置中的所有必需字段都已填写</li>";
    echo "<li>检查文件权限和路径</li>";
    echo "</ol>";
}

echo "<h2>🔗 相关链接</h2>";
echo "<ul>";
echo "<li><a href='test-forgot-password-fixed.php'>测试修复后的密码找回功能</a></li>";
echo "<li><a href='error-log-test.php'>PHP错误日志测试</a></li>";
echo "<li><a href='../server/email-config.php'>查看邮件配置文件</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p><small>📅 生成时间: " . date('Y-m-d H:i:s') . "</small></p>";
?>
