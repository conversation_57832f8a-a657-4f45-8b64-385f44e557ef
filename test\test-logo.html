<!DOCTYPE html>
<html>
<head>
    <title>创建测试Logo</title>
</head>
<body>
    <canvas id="canvas" width="200" height="100"></canvas>
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // 绘制一个简单的Logo
        ctx.fillStyle = '#ff6b6b';
        ctx.fillRect(0, 0, 200, 100);
        
        ctx.fillStyle = '#ffffff';
        ctx.font = '20px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('TEST LOGO', 100, 55);
        
        // 转换为PNG并下载
        canvas.toBlob(function(blob) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'test-logo.png';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }, 'image/png');
    </script>
</body>
</html>
