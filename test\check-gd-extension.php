<?php
/**
 * GD扩展检查工具
 * 检查服务器是否支持GD扩展和图像处理功能
 */

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GD扩展检查工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .check-item {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ddd;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
        }
        .test-image {
            margin: 20px 0;
            text-align: center;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 GD扩展检查工具</h1>
        
        <?php
        echo "<h2>📋 基本信息</h2>";
        echo "<div class='info check-item'>";
        echo "<strong>PHP版本:</strong> " . PHP_VERSION . "<br>";
        echo "<strong>服务器:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
        echo "<strong>操作系统:</strong> " . PHP_OS . "<br>";
        echo "<strong>检查时间:</strong> " . date('Y-m-d H:i:s');
        echo "</div>";
        
        echo "<h2>🔧 GD扩展检查</h2>";
        
        // 检查GD扩展是否加载
        if (extension_loaded('gd')) {
            echo "<div class='success check-item'>";
            echo "✅ <strong>GD扩展已加载</strong>";
            echo "</div>";
            
            // 获取GD信息
            $gdInfo = gd_info();
            echo "<div class='info check-item'>";
            echo "<strong>GD版本:</strong> " . $gdInfo['GD Version'] . "<br>";
            echo "<strong>FreeType支持:</strong> " . ($gdInfo['FreeType Support'] ? '是' : '否') . "<br>";
            echo "<strong>JPEG支持:</strong> " . ($gdInfo['JPEG Support'] ? '是' : '否') . "<br>";
            echo "<strong>PNG支持:</strong> " . ($gdInfo['PNG Support'] ? '是' : '否') . "<br>";
            echo "<strong>GIF支持:</strong> " . ($gdInfo['GIF Read Support'] ? '是' : '否') . "<br>";
            echo "</div>";
            
        } else {
            echo "<div class='error check-item'>";
            echo "❌ <strong>GD扩展未加载</strong><br>";
            echo "这是验证码无法显示的主要原因！";
            echo "</div>";
        }
        
        echo "<h2>🧪 图像函数检查</h2>";
        
        // 检查关键函数
        $functions = [
            'imagecreate' => '创建画布',
            'imagecolorallocate' => '分配颜色',
            'imagefill' => '填充背景',
            'imagestring' => '绘制文字',
            'imageline' => '绘制线条',
            'imagesetpixel' => '绘制像素',
            'imagepng' => '输出PNG图片',
            'imagedestroy' => '销毁图像资源'
        ];
        
        foreach ($functions as $func => $desc) {
            if (function_exists($func)) {
                echo "<div class='success check-item'>";
                echo "✅ <strong>$func()</strong> - $desc";
                echo "</div>";
            } else {
                echo "<div class='error check-item'>";
                echo "❌ <strong>$func()</strong> - $desc (函数不存在)";
                echo "</div>";
            }
        }
        
        echo "<h2>🎨 测试图像生成</h2>";
        
        if (extension_loaded('gd') && function_exists('imagecreate')) {
            try {
                // 尝试创建测试图像
                $testImage = imagecreate(200, 50);
                $bgColor = imagecolorallocate($testImage, 240, 240, 240);
                $textColor = imagecolorallocate($testImage, 50, 50, 50);
                imagefill($testImage, 0, 0, $bgColor);
                imagestring($testImage, 5, 50, 15, 'TEST', $textColor);
                
                // 保存测试图片
                $testFile = 'test-gd-image.png';
                if (imagepng($testImage, $testFile)) {
                    echo "<div class='success check-item'>";
                    echo "✅ <strong>图像生成测试成功</strong><br>";
                    echo "测试图片已保存为: $testFile";
                    echo "</div>";
                    
                    echo "<div class='test-image'>";
                    echo "<img src='$testFile?t=" . time() . "' alt='测试图片' style='border: 1px solid #ccc;'>";
                    echo "</div>";
                } else {
                    echo "<div class='error check-item'>";
                    echo "❌ <strong>图像保存失败</strong><br>";
                    echo "可能是文件权限问题";
                    echo "</div>";
                }
                
                imagedestroy($testImage);
                
            } catch (Exception $e) {
                echo "<div class='error check-item'>";
                echo "❌ <strong>图像生成测试失败</strong><br>";
                echo "错误信息: " . $e->getMessage();
                echo "</div>";
            }
        } else {
            echo "<div class='error check-item'>";
            echo "❌ <strong>无法进行图像生成测试</strong><br>";
            echo "GD扩展或相关函数不可用";
            echo "</div>";
        }
        
        echo "<h2>🔧 解决方案</h2>";
        
        if (!extension_loaded('gd')) {
            echo "<div class='warning check-item'>";
            echo "<strong>如果GD扩展未加载，请尝试以下解决方案：</strong><br><br>";
            echo "<strong>1. 检查php.ini配置:</strong><br>";
            echo "<div class='code'>";
            echo "extension=gd<br>";
            echo "; 或者<br>";
            echo "extension=php_gd2.dll  ; Windows<br>";
            echo "extension=gd.so       ; Linux";
            echo "</div>";
            
            echo "<strong>2. 重启Web服务器</strong><br>";
            echo "修改php.ini后需要重启Apache/Nginx<br><br>";
            
            echo "<strong>3. 安装GD扩展 (Linux):</strong><br>";
            echo "<div class='code'>";
            echo "# Ubuntu/Debian<br>";
            echo "sudo apt-get install php-gd<br><br>";
            echo "# CentOS/RHEL<br>";
            echo "sudo yum install php-gd<br><br>";
            echo "# 或者<br>";
            echo "sudo dnf install php-gd";
            echo "</div>";
            
            echo "<strong>4. 检查PHP模块:</strong><br>";
            echo "<div class='code'>";
            echo "php -m | grep -i gd";
            echo "</div>";
            echo "</div>";
        }
        ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="javascript:location.reload()" class="btn">🔄 重新检查</a>
            <a href="../api/captcha.php" class="btn" target="_blank">🖼️ 测试验证码</a>
        </div>
        
        <div class="info check-item" style="margin-top: 20px;">
            <strong>💡 提示:</strong><br>
            • 如果本地能显示验证码但服务器不能，99%是GD扩展问题<br>
            • 修改php.ini后一定要重启Web服务器<br>
            • 可以通过 <code>php -m</code> 命令检查已加载的模块<br>
            • 如果是共享主机，请联系主机商启用GD扩展
        </div>
    </div>
</body>
</html>
