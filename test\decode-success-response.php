<?php
$response = '{"success":true,"message":"\u5bc6\u7801\u91cd\u7f6e\u90ae\u4ef6\u5df2\u53d1\u9001\uff0c\u8bf7\u68c0\u67e5\u60a8\u7684\u90ae\u7bb1","debug_info":{"email":"<EMAIL>","token":"dc0abd7c68644d7a6caee2a75dd5c4e9d0f6c5cd8c5be0c82bf3b5d06ae32e09","reset_link":"reset-password.html?token=dc0abd7c68644d7a6caee2a75dd5c4e9d0f6c5cd8c5be0c82bf3b5d06ae32e09"}}';

$decoded = json_decode($response, true);
echo "🎉 API响应解码结果:\n";
echo "==================\n";
echo "成功: " . ($decoded['success'] ? '✅ 是' : '❌ 否') . "\n";
echo "消息: " . $decoded['message'] . "\n";
echo "邮箱: " . $decoded['debug_info']['email'] . "\n";
echo "令牌: " . $decoded['debug_info']['token'] . "\n";
echo "重置链接: " . $decoded['debug_info']['reset_link'] . "\n";
?>
