<?php
/**
 * 用户注册API
 */

session_start();
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit();
}

// 数据库配置
$dbPath = '../server/user_system.db3';

/**
 * 验证验证码
 */
function verifyCaptcha($inputCode, $sessionId) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $codeHash = hash('sha256', strtoupper($inputCode));
        
        $stmt = $pdo->prepare("
            SELECT id FROM captcha_codes 
            WHERE code_hash = ? AND session_id = ? 
            AND expires_at > datetime('now') AND is_used = 0
        ");
        $stmt->execute([$codeHash, $sessionId]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            // 标记验证码为已使用
            $stmt = $pdo->prepare("UPDATE captcha_codes SET is_used = 1 WHERE id = ?");
            $stmt->execute([$result['id']]);
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        error_log("验证码验证失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 检查用户名是否已存在
 */
function checkUsernameExists($username) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
        $stmt->execute([$username]);
        
        return $stmt->fetchColumn() > 0;
    } catch (Exception $e) {
        error_log("检查用户名失败: " . $e->getMessage());
        return true; // 出错时假设已存在，防止重复注册
    }
}

/**
 * 创建用户 (使用安全的密码哈希算法)
 */
function createUser($username, $password, $email) {
    global $dbPath;

    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // 使用安全的密码哈希算法
        $passwordHash = password_hash($password, PASSWORD_ARGON2ID);

        $stmt = $pdo->prepare("
            INSERT INTO users (username, password_hash, email, status)
            VALUES (?, ?, ?, 'active')
        ");

        $stmt->execute([$username, $passwordHash, $email]);

        return $pdo->lastInsertId();
    } catch (Exception $e) {
        error_log("创建用户失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 记录注册日志
 */
function logRegistration($userId, $username, $success, $reason = null) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("
            INSERT INTO login_logs (user_id, username, ip_address, user_agent, status, failure_reason)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $userId,
            $username,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            $success ? 'registered' : 'register_failed',
            $reason
        ]);
    } catch (Exception $e) {
        error_log("记录注册日志失败: " . $e->getMessage());
    }
}

try {
    // 获取请求数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('无效的JSON数据');
    }
    
    // 验证必需字段
    $requiredFields = ['username', 'password', 'email', 'captcha'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            throw new Exception("缺少必需字段: $field");
        }
    }
    
    $username = trim($data['username']);
    $password = trim($data['password']);
    $captcha = trim($data['captcha']);
    $email = trim($data['email']);
    
    // 验证用户名格式
    if (!preg_match('/^[a-zA-Z0-9_]{3,20}$/', $username)) {
        throw new Exception('用户名格式不正确，只能包含字母、数字、下划线，长度3-20位');
    }
    
    // 验证密码强度
    if (strlen($password) < 6) {
        throw new Exception('密码长度至少6位');
    }
    
    // 验证邮箱格式
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('邮箱格式不正确');
    }
    
    // 验证验证码
    $sessionId = session_id();
    if (!verifyCaptcha($captcha, $sessionId)) {
        throw new Exception('验证码错误或已过期');
    }
    
    // 检查用户名是否已存在
    if (checkUsernameExists($username)) {
        throw new Exception('用户名已存在');
    }
    
    // 创建用户
    $userId = createUser($username, $password, $email);
    
    if (!$userId) {
        throw new Exception('注册失败，请稍后重试');
    }
    
    // 记录成功日志
    logRegistration($userId, $username, true);
    
    echo json_encode([
        'success' => true,
        'message' => '注册成功！请登录后购买激活码',
        'data' => [
            'user_id' => $userId,
            'username' => $username
        ]
    ]);
    
} catch (Exception $e) {
    // 记录失败日志
    if (isset($username)) {
        logRegistration(null, $username, false, $e->getMessage());
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
