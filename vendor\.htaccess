# =============================================================================
# vendor目录安全配置
# 防止直接访问vendor目录中的敏感文件
# =============================================================================

# 拒绝所有直接访问
Order Deny,Allow
Deny from all

# 可选：允许特定文件类型的访问（如果需要）
# <Files "*.css">
#     Allow from all
# </Files>
# <Files "*.js">
#     Allow from all
# </Files>

# 防止目录浏览
Options -Indexes

# 隐藏敏感文件
<FilesMatch "\.(json|lock|md|txt|log|ini|conf|config)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# 保护Composer文件
<FilesMatch "^(composer\.|autoload|platform_check)">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# 保护PHP源码文件
<FilesMatch "\.php$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# 防止访问隐藏文件
<FilesMatch "^\.">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# 安全头部
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# 禁用PHP执行（双重保护）
<IfModule mod_php7.c>
    php_flag engine off
</IfModule>
<IfModule mod_php8.c>
    php_flag engine off
</IfModule>

# 错误页面重定向
ErrorDocument 403 /403.html
ErrorDocument 404 /404.html
