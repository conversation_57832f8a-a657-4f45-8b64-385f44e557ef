<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 倒计时系统</title>
    <link rel="stylesheet" href="css/lock-style.css">
    <style>
        /* 新增样式 */
        .user-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* 套餐选择样式 */
        .pricing-plans {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            justify-content: center;
            flex-wrap: wrap;
        }

        .plan-item {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            min-width: 200px;
            text-align: center;
            border: 2px solid #e0e0e0;
            transition: all 0.3s ease;
            position: relative;
            backdrop-filter: blur(10px);
        }

        .plan-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            border-color: #4CAF50;
        }

        .plan-item.recommended {
            border-color: #FF6B35;
            background: rgba(255, 255, 255, 0.95);
        }

        .plan-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .plan-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .plan-badge {
            background: #4CAF50;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .recommended-badge {
            background: #FF6B35;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .plan-price {
            margin: 15px 0;
            text-align: center;
        }

        .plan-price .price {
            font-size: 32px;
            font-weight: bold;
            color: #FF6B35;
            display: inline;
        }

        .plan-price .period {
            font-size: 16px;
            color: #666;
            display: inline;
            margin-left: -40px;
        }

        /* 响应式调整：防止小屏幕下文字重叠 */
        @media (max-width: 768px) {
            .plan-price .period {
                margin-left: -25px;
            }
        }

        @media (max-width: 480px) {
            .plan-price .period {
                margin-left: -15px;
            }
        }

        .plan-validity {
            color: #444;
            font-size: 14px;
            margin: 10px 0;
            font-weight: 500;
        }

        .plan-savings {
            background: #4CAF50;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-top: 10px;
            display: inline-block;
        }

        /* 二维码区域样式更新 */
        .qr-codes {
            display: flex;
            gap: 30px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .qr-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .qr-note {
            font-size: 12px;
            color: #444;
            margin-top: 5px;
            font-style: italic;
            font-weight: 500;
        }

        /* 确保所有文字有足够的对比度 */
        .qr-label {
            color: #333;
            font-weight: bold;
            font-size: 14px;
        }

        .section-title {
            color: #333;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .form-footer {
            margin-top: 15px;
        }

        .form-footer a {
            color: #4CAF50;
            text-decoration: none;
            font-weight: 500;
        }

        .form-footer a:hover {
            color: #45a049;
            text-decoration: underline;
        }

        .status-message {
            margin-top: 15px;
            padding: 10px;
            border-radius: 8px;
            font-weight: 500;
        }

        .instructions {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            margin-top: 20px;
            border: 1px solid rgba(255, 255, 255, 0.5);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .instructions h4 {
            color: #333;
            margin-bottom: 15px;
        }

        .instructions ol {
            text-align: left;
            color: #444;
            line-height: 1.6;
        }

        .instructions li {
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-container {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 15px;
            margin: 8px 0;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.95);
            font-size: 16px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
            background: white;
        }
        
        .captcha-group {
            display: flex;
            gap: 10px;
            align-items: center;
            margin: 8px 0;
            /* 确保验证码组合框与其他输入框对齐 */
            width: 100%;
            box-sizing: border-box;
        }

        .captcha-input {
            flex: 1;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.95);
            font-size: 16px;
            box-sizing: border-box;
            /* 确保验证码输入框左边缘与其他输入框对齐 */
            margin: 0;
        }

        .captcha-input:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
            background: white;
        }
        
        .captcha-image {
            width: 120px;
            height: 44px; /* 与输入框高度匹配 (12px padding * 2 + 20px line-height) */
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            flex-shrink: 0; /* 防止图片被压缩 */
        }
        
        .captcha-image:hover {
            border-color: #4CAF50;
            transform: scale(1.05);
        }
        
        .action-button {
            width: 100%;
            padding: 15px;
            margin: 15px 0;
            border: none;
            border-radius: 8px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .action-button:hover {
            background: linear-gradient(135deg, #45a049, #4CAF50);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }
        
        .action-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .form-footer {
            text-align: center;
            margin-top: 15px;
        }
        
        .form-footer a {
            color: #4CAF50;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s ease;
        }
        
        .form-footer a:hover {
            color: #45a049;
            text-decoration: underline;
        }
        
        .form-description {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
        }
        
        .spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .status-message {
            margin: 15px 0;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }
        
        .status-success {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
            border: 1px solid rgba(76, 175, 80, 0.5);
        }
        
        .status-error {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 1px solid rgba(244, 67, 54, 0.5);
        }
        
        .status-warning {
            background: rgba(255, 193, 7, 0.2);
            color: #FFC107;
            border: 1px solid rgba(255, 193, 7, 0.5);
        }
    </style>
</head>
<body>
    <div class="lock-container">
        <!-- 背景动画 -->
        <div class="animated-bg"></div>
        
        <!-- 主要内容 -->
        <div class="lock-content">
            <!-- Logo -->
            <div class="logo-section">
                <img src="images/logo.png" alt="Logo" class="logo">
            </div>
            
            <!-- 标题 -->
            <div class="title-section">
                <h1 class="main-title">🔐 倒计时系统</h1>
                <p class="subtitle">用户登录 & 激活管理</p>
            </div>
            
            <!-- 付费信息 -->
            <div class="payment-section">
                <!-- 套餐选择 -->
                <div class="pricing-plans">
                    <div class="plan-item monthly-plan">
                        <div class="plan-header">
                            <span class="plan-name">月度套餐</span>
                            <span class="plan-badge">灵活选择</span>
                        </div>
                        <div class="plan-price">
                            <span class="price">¥19.9</span>
                            <span class="period">/月</span>
                        </div>
                        <div class="plan-validity">激活码有效期30天</div>
                    </div>

                    <div class="plan-item yearly-plan recommended">
                        <div class="plan-header">
                            <span class="plan-name">年度套餐</span>
                            <span class="plan-badge recommended-badge">推荐</span>
                        </div>
                        <div class="plan-price">
                            <span class="price">¥168</span>
                            <span class="period">/年</span>
                        </div>
                        <div class="plan-validity">激活码有效期365天</div>
                        <div class="plan-savings">相比月付节省¥70.8</div>
                    </div>
                </div>

                <!-- 二维码区域 -->
                <div class="qr-codes">
                    <div class="qr-item">
                        <img src="images/alipay-qr.png" alt="支付宝付款码" class="qr-code">
                        <p class="qr-label">支付宝付款</p>
                        <p class="qr-note">月度¥19.9 | 年度¥168</p>
                    </div>

                    <div class="qr-item">
                        <img src="images/qq-qr.png" alt="QQ联系二维码" class="qr-code">
                        <p class="qr-label">付款后加QQ获取激活码</p>
                        <p class="qr-note">QQ号：416787728</p>
                        <p class="qr-note">请备注选择的套餐类型</p>
                    </div>
                </div>
            </div>
            
            <!-- 用户操作区域 -->
            <div class="user-section">
                <!-- 登录表单 -->
                <div id="loginForm" class="form-container">
                    <h2 class="section-title">🔑 用户登录</h2>
                    
                    <div class="input-group">
                        <input type="text" id="loginUsername" class="form-input" placeholder="用户名" maxlength="20">
                        <input type="password" id="loginPassword" class="form-input" placeholder="密码" maxlength="50">

                        <button id="loginBtn" class="action-button">
                            <span class="button-text">登录</span>
                            <span class="button-loading" style="display: none;">
                                <span class="spinner"></span>
                                登录中...
                            </span>
                        </button>
                    </div>
                    
                    <div class="form-footer">
                        <a href="#" onclick="showRegisterForm()">还没有账号？立即注册</a>
                        <br>
                        <a href="#" onclick="showForgotPasswordForm()">忘记密码？</a>
                    </div>
                </div>

                <!-- 注册表单 -->
                <div id="registerForm" class="form-container" style="display: none;">
                    <h2 class="section-title">📝 用户注册</h2>
                    
                    <div class="input-group">
                        <input type="text" id="regUsername" class="form-input" placeholder="用户名 (3-20位字母数字)" maxlength="20">
                        <input type="password" id="regPassword" class="form-input" placeholder="密码 (至少6位)" maxlength="50">
                        <input type="email" id="regEmail" class="form-input" placeholder="邮箱 (必填，用于密码找回)" maxlength="100" required>
                        
                        <div class="captcha-group">
                            <input type="text" id="regCaptcha" class="captcha-input" placeholder="验证码" maxlength="4">
                            <img id="regCaptchaImg" class="captcha-image" src="api/captcha.php" alt="验证码" onclick="refreshCaptcha('register')">
                        </div>
                        
                        <button id="registerBtn" class="action-button">
                            <span class="button-text">注册</span>
                            <span class="button-loading" style="display: none;">
                                <span class="spinner"></span>
                                注册中...
                            </span>
                        </button>
                    </div>
                    
                    <div class="form-footer">
                        <a href="#" onclick="showLoginForm()">已有账号？立即登录</a>
                    </div>
                </div>

                <!-- 密码找回表单 -->
                <div id="forgotPasswordForm" class="form-container" style="display: none;">
                    <h2 class="section-title">🔑 密码找回</h2>

                    <div class="form-description">
                        <p style="font-size: 14px; color: rgba(255, 255, 255, 0.7); margin-bottom: 15px;">
                            请输入用户名和注册邮箱，系统将发送重置密码链接到您的邮箱
                        </p>
                    </div>

                    <div class="input-group">
                        <input type="text" id="forgotUsername" class="form-input" placeholder="请输入用户名" maxlength="20" required>
                        <input type="email" id="forgotEmail" class="form-input" placeholder="请输入注册邮箱" maxlength="100" required>

                        <div class="captcha-group">
                            <input type="text" id="forgotCaptcha" class="captcha-input" placeholder="验证码" maxlength="4">
                            <img id="forgotCaptchaImg" class="captcha-image" src="api/captcha.php" alt="验证码" onclick="refreshCaptcha('forgot')">
                        </div>

                        <button id="sendResetBtn" class="action-button">
                            <span class="button-text">发送重置邮件</span>
                            <span class="button-loading" style="display: none;">
                                <span class="spinner"></span>
                                发送中...
                            </span>
                        </button>
                    </div>

                    <div class="form-footer">
                        <a href="#" onclick="showLoginForm()">返回登录</a>
                    </div>
                </div>

                <!-- 激活码绑定表单 -->
                <div id="activationForm" class="form-container" style="display: none;">
                    <h2 class="section-title">🎯 绑定激活码</h2>
                    <p class="form-description">欢迎 <span id="currentUsername"></span>！请绑定您的激活码</p>
                    
                    <div class="input-group">
                        <input type="text" id="activationCode" class="form-input" placeholder="请输入激活码 (XXXXX-XXXXX-XXXXX-XXXXX)" maxlength="23">
                        
                        <button id="bindBtn" class="action-button">
                            <span class="button-text">绑定激活码</span>
                            <span class="button-loading" style="display: none;">
                                <span class="spinner"></span>
                                绑定中...
                            </span>
                        </button>
                    </div>
                    
                    <div class="form-footer">
                        <a href="#" onclick="logout()">退出登录</a>
                    </div>
                </div>
                
                <!-- 状态显示区域 -->
                <div id="statusMessage" class="status-message"></div>
            </div>
            
            <!-- 使用说明 -->
            <div class="instructions">
                <h4>📋 使用说明</h4>
                <ol>
                    <li>注册账号并登录系统</li>
                    <li>选择套餐：月度¥19.9（30天）或年度¥168（365天）</li>
                    <li>扫描支付宝二维码付款对应金额</li>
                    <li>扫描QQ二维码添加客服</li>
                    <li>发送付款截图给客服，并备注选择的套餐类型</li>
                    <li>客服确认后提供对应有效期的激活码</li>
                    <li>在系统中绑定激活码即可使用</li>
                    <li>年度套餐更优惠，相比月付节省¥70.8</li>
                    <li>所有套餐均为单设备登录</li>
                </ol>
            </div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="js/new-auth-system.js"></script>
</body>
</html>
