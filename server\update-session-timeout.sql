-- 更新会话超时时间配置
-- 将会话超时时间从24小时延长到90天，适合商家广告长期展示

-- 更新系统配置表中的会话超时时间
UPDATE system_config 
SET config_value = '7776000', 
    description = '会话超时时间(秒) - 90天，适合商家广告长期展示'
WHERE config_key = 'session_timeout';

-- 如果配置不存在，则插入新配置
INSERT OR IGNORE INTO system_config (config_key, config_value, description) VALUES
('session_timeout', '7776000', '会话超时时间(秒) - 90天，适合商家广告长期展示');

-- 显示更新后的配置
SELECT config_key, config_value, description 
FROM system_config 
WHERE config_key = 'session_timeout';
