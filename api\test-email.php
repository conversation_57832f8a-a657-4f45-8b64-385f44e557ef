<?php
/**
 * 邮件测试API
 * 用于测试邮件配置和发送功能
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $action = $_GET['action'] ?? 'check';
    
    switch ($action) {
        case 'check':
            // 检查邮件配置
            checkEmailConfig();
            break;
            
        case 'test':
            // 发送测试邮件
            sendTestEmail();
            break;
            
        default:
            throw new Exception('无效的操作');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * 检查邮件配置
 */
function checkEmailConfig() {
    require_once '../server/email-sender.php';
    
    $result = validateEmailConfig();
    
    echo json_encode([
        'success' => $result['valid'],
        'message' => $result['valid'] ? '邮件配置验证通过' : $result['error'],
        'data' => $result
    ]);
}

/**
 * 发送测试邮件
 */
function sendTestEmail() {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只允许POST请求');
    }
    
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data || !isset($data['email'])) {
        throw new Exception('请提供测试邮箱地址');
    }
    
    $testEmail = trim($data['email']);
    
    if (!filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('邮箱格式不正确');
    }
    
    require_once '../server/email-sender.php';
    
    // 检查配置
    $configResult = validateEmailConfig();
    if (!$configResult['valid']) {
        throw new Exception('邮件配置无效: ' . $configResult['error']);
    }
    
    // 发送测试邮件
    $subject = '邮件配置测试 - 倒计时系统';
    $body = createTestEmailBody();
    
    $emailSender = new EmailSender();
    $result = $emailSender->sendEmail($testEmail, $subject, $body, true);
    
    if ($result) {
        // 记录测试日志
        $logMessage = date('Y-m-d H:i:s') . " - 测试邮件发送成功\n";
        $logMessage .= "收件人: $testEmail\n";
        $logMessage .= "邮件服务商: " . $configResult['provider'] . "\n";
        $logMessage .= "SMTP服务器: " . $configResult['smtp_host'] . "\n\n";
        
        file_put_contents('../server/email_test.log', $logMessage, FILE_APPEND);
        
        echo json_encode([
            'success' => true,
            'message' => '测试邮件发送成功，请检查收件箱',
            'data' => [
                'email' => $testEmail,
                'provider' => $configResult['provider'],
                'smtp_host' => $configResult['smtp_host']
            ]
        ]);
    } else {
        $errorInfo = $emailSender->getLastResponse();
        
        // 记录失败日志
        $logMessage = date('Y-m-d H:i:s') . " - 测试邮件发送失败\n";
        $logMessage .= "收件人: $testEmail\n";
        $logMessage .= "错误信息: $errorInfo\n\n";
        
        file_put_contents('../server/email_test.log', $logMessage, FILE_APPEND);
        
        throw new Exception('测试邮件发送失败: ' . $errorInfo);
    }
}

/**
 * 创建测试邮件内容
 */
function createTestEmailBody() {
    $currentTime = date('Y-m-d H:i:s');
    
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #007bff; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
            .content { padding: 20px; background: #f9f9f9; }
            .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; background: #e9ecef; border-radius: 0 0 5px 5px; }
            .success { background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 15px 0; border-radius: 5px; color: #155724; }
            .info-box { background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; margin: 15px 0; border-radius: 5px; color: #0c5460; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>📧 邮件配置测试</h1>
            </div>
            <div class='content'>
                <div class='success'>
                    <h2>✅ 测试成功！</h2>
                    <p>恭喜！您的邮件配置工作正常，系统可以成功发送邮件。</p>
                </div>
                
                <h3>📋 测试信息</h3>
                <div class='info-box'>
                    <p><strong>测试时间：</strong> $currentTime</p>
                    <p><strong>系统名称：</strong> 倒计时系统</p>
                    <p><strong>测试类型：</strong> SMTP邮件发送测试</p>
                    <p><strong>邮件格式：</strong> HTML格式</p>
                </div>
                
                <h3>🔧 功能说明</h3>
                <p>此测试邮件表明以下功能正常工作：</p>
                <ul>
                    <li>✅ SMTP服务器连接</li>
                    <li>✅ 邮箱认证</li>
                    <li>✅ HTML邮件发送</li>
                    <li>✅ 中文字符支持</li>
                    <li>✅ 邮件模板渲染</li>
                </ul>
                
                <h3>📝 下一步</h3>
                <p>现在您可以：</p>
                <ol>
                    <li>使用密码找回功能测试实际应用</li>
                    <li>检查邮件是否进入垃圾邮件文件夹</li>
                    <li>确认邮件链接可以正常点击</li>
                </ol>
            </div>
            <div class='footer'>
                <p>此邮件由倒计时系统邮件测试功能自动发送</p>
                <p>如有问题，请检查邮件配置或联系系统管理员</p>
            </div>
        </div>
    </body>
    </html>
    ";
}
?>
