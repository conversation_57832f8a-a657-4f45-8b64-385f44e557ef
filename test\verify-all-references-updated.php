<?php
/**
 * 验证所有PHPMailer引用已更新
 * 检查项目中是否还有test_email_system的引用
 */

header('Content-Type: text/html; charset=utf-8');

function scanForOldReferences() {
    $result = [
        'old_references' => [],
        'new_references' => [],
        'total_files_scanned' => 0
    ];
    
    try {
        $projectRoot = dirname(__DIR__);
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($projectRoot, RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && preg_match('/\.(php|html|js|css|json|md)$/i', $file->getFilename())) {
                $filePath = $file->getPathname();
                $relativePath = str_replace($projectRoot . DIRECTORY_SEPARATOR, '', $filePath);
                
                // 跳过vendor目录和一些不需要检查的目录
                if (strpos($relativePath, 'vendor' . DIRECTORY_SEPARATOR) === 0 ||
                    strpos($relativePath, '.git' . DIRECTORY_SEPARATOR) === 0 ||
                    strpos($relativePath, 'node_modules' . DIRECTORY_SEPARATOR) === 0) {
                    continue;
                }
                
                $result['total_files_scanned']++;
                $content = file_get_contents($filePath);
                
                // 检查旧引用
                $oldPatterns = [
                    'test_email_system/vendor/autoload.php',
                    '../../test_email_system/vendor/autoload.php',
                    '../test_email_system/vendor/autoload.php',
                    'test_email_system\\vendor\\autoload.php',
                    '..\\..\\test_email_system\\vendor\\autoload.php',
                    '..\\test_email_system\\vendor\\autoload.php'
                ];
                
                foreach ($oldPatterns as $pattern) {
                    if (strpos($content, $pattern) !== false) {
                        $result['old_references'][] = [
                            'file' => $relativePath,
                            'pattern' => $pattern
                        ];
                    }
                }
                
                // 检查新引用
                $newPatterns = [
                    '../vendor/autoload.php',
                    'vendor/autoload.php',
                    '../../vendor/autoload.php'
                ];
                
                foreach ($newPatterns as $pattern) {
                    if (strpos($content, $pattern) !== false) {
                        $result['new_references'][] = [
                            'file' => $relativePath,
                            'pattern' => $pattern
                        ];
                    }
                }
            }
        }
        
    } catch (Exception $e) {
        $result['error'] = $e->getMessage();
    }
    
    return $result;
}

$scanResult = scanForOldReferences();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证PHPMailer引用更新</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
        .warning-box { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; }
        .pattern { font-family: monospace; background: #e8e8e8; padding: 2px 4px; }
    </style>
</head>
<body>
    <h1>✅ 验证PHPMailer引用更新</h1>
    <p><strong>验证时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="test-section">
        <h2>📊 扫描统计</h2>
        <p><strong>扫描的文件数:</strong> <?php echo $scanResult['total_files_scanned']; ?> 个</p>
        <p><strong>发现的旧引用:</strong> <?php echo count($scanResult['old_references']); ?> 处</p>
        <p><strong>发现的新引用:</strong> <?php echo count($scanResult['new_references']); ?> 处</p>
    </div>
    
    <?php if (empty($scanResult['old_references'])): ?>
        <div class="highlight">
            <h3>🎉 更新完成！</h3>
            <p class="success">✅ 没有发现任何test_email_system的旧引用</p>
            <p>所有PHPMailer路径引用已成功更新为本项目的vendor目录</p>
        </div>
    <?php else: ?>
        <div class="warning-box">
            <h3>⚠️ 发现旧引用</h3>
            <p class="warning">还有 <?php echo count($scanResult['old_references']); ?> 处旧引用需要更新</p>
        </div>
        
        <div class="test-section">
            <h2>🔍 需要更新的旧引用</h2>
            <table>
                <tr>
                    <th>文件路径</th>
                    <th>旧引用模式</th>
                </tr>
                <?php foreach ($scanResult['old_references'] as $ref): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($ref['file']); ?></td>
                        <td><span class="pattern"><?php echo htmlspecialchars($ref['pattern']); ?></span></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($scanResult['new_references'])): ?>
        <div class="test-section">
            <h2>✅ 已更新的新引用</h2>
            <table>
                <tr>
                    <th>文件路径</th>
                    <th>新引用模式</th>
                </tr>
                <?php foreach ($scanResult['new_references'] as $ref): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($ref['file']); ?></td>
                        <td><span class="pattern"><?php echo htmlspecialchars($ref['pattern']); ?></span></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
    <?php endif; ?>
    
    <div class="test-section">
        <h2>📋 更新总结</h2>
        
        <h3>已手动更新的文件:</h3>
        <ul>
            <li>✅ <strong>server/working-email-sender.php</strong> - 主要邮件发送器</li>
            <li>✅ <strong>test/verify-phpmailer-path.php</strong> - 路径验证工具</li>
            <li>✅ <strong>test/test-working-email-sender.php</strong> - 邮件发送器测试</li>
            <li>✅ <strong>test/check-paths.php</strong> - 路径检查工具</li>
        </ul>
        
        <h3>路径变更:</h3>
        <ul>
            <li><strong>旧路径:</strong> <span class="pattern">../../test_email_system/vendor/autoload.php</span></li>
            <li><strong>新路径:</strong> <span class="pattern">../vendor/autoload.php</span></li>
        </ul>
        
        <h3>部署优势:</h3>
        <ul>
            <li>✅ <strong>完全独立:</strong> 不依赖外部项目</li>
            <li>✅ <strong>可直接部署:</strong> 复制整个项目即可</li>
            <li>✅ <strong>路径简化:</strong> 使用项目内相对路径</li>
            <li>✅ <strong>版本固定:</strong> PHPMailer版本锁定</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🧪 下一步测试</h2>
        <p>现在可以测试更新后的功能:</p>
        <ol>
            <li><strong>测试本地PHPMailer:</strong> <a href="test-local-phpmailer.php">test-local-phpmailer.php</a></li>
            <li><strong>测试邮件发送器:</strong> <a href="test-working-email-sender.php">test-working-email-sender.php</a></li>
            <li><strong>测试密码找回:</strong> <a href="../new-lock.html">登录页面 → 忘记密码</a></li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关工具</h2>
        <ul>
            <li><a href="copy-phpmailer.php">复制PHPMailer文件</a></li>
            <li><a href="update-all-phpmailer-references.php">全项目引用更新工具</a></li>
            <li><a href="test-local-phpmailer.php">测试本地PHPMailer</a></li>
            <li><a href="test-working-email-sender.php">测试工作的邮件发送器</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
