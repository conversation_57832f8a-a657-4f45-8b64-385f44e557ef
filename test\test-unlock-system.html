<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>付费解锁系统测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .test-item {
            margin: 15px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
            width: 300px;
        }
        
        .fingerprint {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 5px;
            border-radius: 3px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <h1>🔐 付费解锁系统测试页面</h1>
    
    <!-- 设备指纹测试 -->
    <div class="test-section">
        <h2 class="test-title">📱 设备指纹测试</h2>
        
        <div class="test-item">
            <strong>设备指纹:</strong>
            <div id="deviceFingerprint" class="fingerprint">生成中...</div>
        </div>
        
        <div class="test-item">
            <strong>设备信息:</strong>
            <div id="deviceInfo">收集中...</div>
        </div>
        
        <button onclick="regenerateFingerprint()">重新生成指纹</button>
    </div>
    
    <!-- 激活码验证测试 -->
    <div class="test-section">
        <h2 class="test-title">🔑 激活码验证测试</h2>
        
        <div class="test-item">
            <label>测试激活码:</label>
            <input type="text" id="testCode" placeholder="XXXXX-XXXXX-XXXXX-XXXXX" maxlength="23">
            <button onclick="testActivationCode()">验证激活码</button>
        </div>
        
        <div id="validationResult" class="test-item" style="display: none;"></div>
    </div>
    
    <!-- API连接测试 -->
    <div class="test-section">
        <h2 class="test-title">🌐 API连接测试</h2>
        
        <div class="test-item">
            <button onclick="testAPIConnection()">测试API连接</button>
            <button onclick="getSystemStats()">获取系统统计</button>
        </div>
        
        <div id="apiResult" class="test-item" style="display: none;"></div>
    </div>
    
    <!-- 本地存储测试 -->
    <div class="test-section">
        <h2 class="test-title">💾 本地存储测试</h2>
        
        <div class="test-item">
            <button onclick="testLocalStorage()">测试本地存储</button>
            <button onclick="clearLocalStorage()">清除本地数据</button>
        </div>
        
        <div id="storageResult" class="test-item" style="display: none;"></div>
    </div>
    
    <!-- 页面跳转测试 -->
    <div class="test-section">
        <h2 class="test-title">🔗 页面跳转测试</h2>
        
        <div class="test-item">
            <button onclick="goToLockScreen()">跳转到锁屏页面</button>
            <button onclick="goToMainPage()">跳转到主页面</button>
        </div>
    </div>
    
    <!-- 系统信息 -->
    <div class="test-section">
        <h2 class="test-title">ℹ️ 系统信息</h2>
        
        <div class="test-item">
            <strong>浏览器:</strong> <span id="browserInfo"></span>
        </div>
        
        <div class="test-item">
            <strong>屏幕分辨率:</strong> <span id="screenInfo"></span>
        </div>
        
        <div class="test-item">
            <strong>时区:</strong> <span id="timezoneInfo"></span>
        </div>
        
        <div class="test-item">
            <strong>当前时间:</strong> <span id="currentTime"></span>
        </div>
    </div>

    <script src="../js/device-fingerprint.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('🧪 测试页面加载完成');
            
            // 生成设备指纹
            await generateDeviceFingerprint();
            
            // 显示系统信息
            showSystemInfo();
            
            // 更新时间
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
        });

        async function generateDeviceFingerprint() {
            try {
                const fingerprint = await window.deviceFingerprint.generateFingerprint();
                document.getElementById('deviceFingerprint').textContent = fingerprint;
                
                const deviceInfo = window.deviceFingerprint.getDeviceInfoString();
                document.getElementById('deviceInfo').textContent = deviceInfo;
                
                console.log('✅ 设备指纹生成成功');
            } catch (error) {
                console.error('❌ 设备指纹生成失败:', error);
                document.getElementById('deviceFingerprint').textContent = '生成失败: ' + error.message;
            }
        }

        async function regenerateFingerprint() {
            document.getElementById('deviceFingerprint').textContent = '重新生成中...';
            await generateDeviceFingerprint();
        }

        async function testActivationCode() {
            const code = document.getElementById('testCode').value.trim().toUpperCase();
            const resultDiv = document.getElementById('validationResult');
            
            if (!code) {
                showResult(resultDiv, '请输入激活码', 'error');
                return;
            }

            try {
                showResult(resultDiv, '验证中...', 'warning');
                
                const response = await fetch('../api/validate-activation.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        activationCode: code,
                        deviceFingerprint: window.deviceFingerprint.getFingerprint(),
                        deviceInfo: window.deviceFingerprint.getDeviceInfoString(),
                        userAgent: navigator.userAgent,
                        screenResolution: `${screen.width}x${screen.height}`,
                        timestamp: Date.now()
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult(resultDiv, `✅ 验证成功: ${result.message}`, 'success');
                } else {
                    showResult(resultDiv, `❌ 验证失败: ${result.message}`, 'error');
                }
                
            } catch (error) {
                showResult(resultDiv, `❌ 网络错误: ${error.message}`, 'error');
            }
        }

        async function testAPIConnection() {
            const resultDiv = document.getElementById('apiResult');
            
            try {
                showResult(resultDiv, '测试连接中...', 'warning');
                
                const response = await fetch('../api/stats.php');
                
                if (response.ok) {
                    showResult(resultDiv, '✅ API连接正常', 'success');
                } else {
                    showResult(resultDiv, `❌ API连接失败: HTTP ${response.status}`, 'error');
                }
                
            } catch (error) {
                showResult(resultDiv, `❌ 连接错误: ${error.message}`, 'error');
            }
        }

        async function getSystemStats() {
            const resultDiv = document.getElementById('apiResult');
            
            try {
                showResult(resultDiv, '获取统计信息中...', 'warning');
                
                const response = await fetch('../api/stats.php');
                const stats = await response.json();
                
                if (stats.success) {
                    const info = `
                        总激活码: ${stats.data.totalCodes} | 
                        已使用: ${stats.data.usedCodes} | 
                        有效: ${stats.data.activeCodes} | 
                        用户数: ${stats.data.totalUsers}
                    `;
                    showResult(resultDiv, `📊 系统统计: ${info}`, 'success');
                } else {
                    showResult(resultDiv, `❌ 获取失败: ${stats.message}`, 'error');
                }
                
            } catch (error) {
                showResult(resultDiv, `❌ 获取错误: ${error.message}`, 'error');
            }
        }

        function testLocalStorage() {
            const resultDiv = document.getElementById('storageResult');
            
            try {
                // 测试localStorage
                const testData = {
                    test: true,
                    timestamp: Date.now(),
                    deviceFingerprint: window.deviceFingerprint.getFingerprint()
                };
                
                localStorage.setItem('unlockSystemTest', JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem('unlockSystemTest'));
                
                if (retrieved && retrieved.test) {
                    showResult(resultDiv, '✅ 本地存储功能正常', 'success');
                } else {
                    showResult(resultDiv, '❌ 本地存储读取失败', 'error');
                }
                
            } catch (error) {
                showResult(resultDiv, `❌ 本地存储错误: ${error.message}`, 'error');
            }
        }

        function clearLocalStorage() {
            const resultDiv = document.getElementById('storageResult');
            
            try {
                localStorage.removeItem('unlockStatus');
                localStorage.removeItem('unlockSystemTest');
                showResult(resultDiv, '🗑️ 本地数据已清除', 'success');
            } catch (error) {
                showResult(resultDiv, `❌ 清除失败: ${error.message}`, 'error');
            }
        }

        function goToLockScreen() {
            window.location.href = '../lock.html';
        }

        function goToMainPage() {
            window.location.href = '../index.php';
        }

        function showSystemInfo() {
            document.getElementById('browserInfo').textContent = navigator.userAgent;
            document.getElementById('screenInfo').textContent = `${screen.width}x${screen.height}`;
            
            try {
                document.getElementById('timezoneInfo').textContent = Intl.DateTimeFormat().resolvedOptions().timeZone;
            } catch (e) {
                document.getElementById('timezoneInfo').textContent = '获取失败';
            }
        }

        function updateCurrentTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString('zh-CN');
        }

        function showResult(element, message, type) {
            element.textContent = message;
            element.className = `test-item ${type}`;
            element.style.display = 'block';
        }
    </script>
</body>
</html>
