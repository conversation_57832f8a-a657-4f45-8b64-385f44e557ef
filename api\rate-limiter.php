<?php
/**
 * 速率限制工具
 */

/**
 * 检查速率限制
 */
function checkRateLimit($action, $identifier, $maxAttempts = 5, $timeWindow = 300) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 创建速率限制表（如果不存在）
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS rate_limits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action VARCHAR(50) NOT NULL,
                identifier VARCHAR(100) NOT NULL,
                attempts INTEGER DEFAULT 1,
                first_attempt DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_attempt DATETIME DEFAULT CURRENT_TIMESTAMP,
                blocked_until DATETIME NULL,
                UNIQUE(action, identifier)
            )
        ");
        
        $now = date('Y-m-d H:i:s');
        $windowStart = date('Y-m-d H:i:s', time() - $timeWindow);
        
        // 检查是否被阻止
        $stmt = $pdo->prepare("
            SELECT blocked_until, attempts 
            FROM rate_limits 
            WHERE action = ? AND identifier = ? AND blocked_until > ?
        ");
        $stmt->execute([$action, $identifier, $now]);
        $blocked = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($blocked) {
            throw new Exception("操作被限制，请在 " . $blocked['blocked_until'] . " 后重试");
        }
        
        // 清理过期记录
        $pdo->prepare("
            DELETE FROM rate_limits 
            WHERE action = ? AND identifier = ? AND first_attempt < ?
        ")->execute([$action, $identifier, $windowStart]);
        
        // 获取当前尝试次数
        $stmt = $pdo->prepare("
            SELECT attempts, first_attempt 
            FROM rate_limits 
            WHERE action = ? AND identifier = ?
        ");
        $stmt->execute([$action, $identifier]);
        $current = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($current) {
            $attempts = $current['attempts'] + 1;
            
            // 更新尝试次数
            $stmt = $pdo->prepare("
                UPDATE rate_limits 
                SET attempts = ?, last_attempt = ? 
                WHERE action = ? AND identifier = ?
            ");
            $stmt->execute([$attempts, $now, $action, $identifier]);
            
            // 检查是否超过限制
            if ($attempts > $maxAttempts) {
                // 计算阻止时间（递增延迟）
                $blockDuration = min(3600, 60 * pow(2, $attempts - $maxAttempts)); // 最多1小时
                $blockedUntil = date('Y-m-d H:i:s', time() + $blockDuration);
                
                $stmt = $pdo->prepare("
                    UPDATE rate_limits 
                    SET blocked_until = ? 
                    WHERE action = ? AND identifier = ?
                ");
                $stmt->execute([$blockedUntil, $action, $identifier]);
                
                throw new Exception("尝试次数过多，已被限制 " . round($blockDuration/60) . " 分钟");
            }
        } else {
            // 首次尝试
            $stmt = $pdo->prepare("
                INSERT INTO rate_limits (action, identifier, attempts, first_attempt, last_attempt)
                VALUES (?, ?, 1, ?, ?)
            ");
            $stmt->execute([$action, $identifier, $now, $now]);
        }
        
        return true;
        
    } catch (Exception $e) {
        if (strpos($e->getMessage(), '操作被限制') !== false || 
            strpos($e->getMessage(), '尝试次数过多') !== false) {
            throw $e;
        }
        error_log("速率限制检查失败: " . $e->getMessage());
        return true; // 出错时允许继续，避免影响正常用户
    }
}

/**
 * 记录成功操作（重置计数器）
 */
function recordSuccessfulAction($action, $identifier) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 删除速率限制记录
        $stmt = $pdo->prepare("
            DELETE FROM rate_limits 
            WHERE action = ? AND identifier = ?
        ");
        $stmt->execute([$action, $identifier]);
        
    } catch (Exception $e) {
        error_log("记录成功操作失败: " . $e->getMessage());
    }
}

/**
 * 获取客户端标识符
 */
function getClientIdentifier() {
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    
    // 使用IP和User-Agent的组合作为标识符
    return hash('sha256', $ip . '|' . $userAgent);
}

/**
 * 检查登录速率限制
 */
function checkLoginRateLimit($username = null) {
    $identifier = getClientIdentifier();
    
    // IP级别限制：每5分钟最多10次尝试
    checkRateLimit('login_ip', $identifier, 10, 300);
    
    // 用户级别限制：每5分钟最多5次尝试
    if ($username) {
        checkRateLimit('login_user', $username, 5, 300);
    }
}

/**
 * 检查注册速率限制
 */
function checkRegisterRateLimit() {
    $identifier = getClientIdentifier();
    
    // IP级别限制：每小时最多3次注册
    checkRateLimit('register', $identifier, 3, 3600);
}

/**
 * 检查验证码生成速率限制
 */
function checkCaptchaRateLimit() {
    $identifier = getClientIdentifier();
    
    // IP级别限制：每分钟最多10次验证码生成
    checkRateLimit('captcha', $identifier, 10, 60);
}

/**
 * 检查密码重置速率限制
 */
function checkPasswordResetRateLimit($email = null) {
    $identifier = getClientIdentifier();
    
    // IP级别限制：每小时最多5次密码重置
    checkRateLimit('password_reset_ip', $identifier, 5, 3600);
    
    // 邮箱级别限制：每小时最多2次密码重置
    if ($email) {
        checkRateLimit('password_reset_email', $email, 2, 3600);
    }
}
?>
