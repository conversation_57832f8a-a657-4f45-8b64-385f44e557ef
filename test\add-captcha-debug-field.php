<?php
/**
 * 为验证码表添加调试字段
 */

$dbPath = '../server/user_system.db3';

try {
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>添加验证码调试字段</h2>";
    
    // 检查code_value字段是否已存在
    $stmt = $pdo->query("PRAGMA table_info(captcha_codes)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasCodeValue = false;
    foreach ($columns as $column) {
        if ($column['name'] === 'code_value') {
            $hasCodeValue = true;
            break;
        }
    }
    
    if ($hasCodeValue) {
        echo "<p>✅ code_value字段已存在</p>";
    } else {
        echo "<p>🔧 正在添加code_value字段...</p>";
        
        // 添加code_value字段
        $pdo->exec("ALTER TABLE captcha_codes ADD COLUMN code_value VARCHAR(10)");
        
        echo "<p>✅ code_value字段添加成功</p>";
    }
    
    // 显示更新后的表结构
    $stmt = $pdo->query("PRAGMA table_info(captcha_codes)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>更新后的表结构：</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>列名</th><th>类型</th><th>非空</th><th>默认值</th><th>主键</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['name']}</td>";
        echo "<td>{$column['type']}</td>";
        echo "<td>" . ($column['notnull'] ? '是' : '否') . "</td>";
        echo "<td>{$column['dflt_value']}</td>";
        echo "<td>" . ($column['pk'] ? '是' : '否') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<hr>";
    echo "<h3>测试链接：</h3>";
    echo "<p><a href='../api/captcha.php' target='_blank'>生成新验证码</a></p>";
    echo "<p><a href='get-captcha-value.php'>查看验证码值</a></p>";
    echo "<p><a href='test-forgot-password-simple.html'>测试密码找回</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ 错误: " . $e->getMessage() . "</p>";
}
?>
