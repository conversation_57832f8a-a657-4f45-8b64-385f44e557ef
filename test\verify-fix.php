<?php
/**
 * 验证激活码绑定修复效果
 */

header('Content-Type: text/html; charset=utf-8');

$dbPath = '../server/user_system.db3';
$testCode = 'N3WK7-7YJW7-JHXB4-NWWEJ';

// 模拟修复前后的逻辑对比
function testOldLogic($codeInfo) {
    // 修复前的逻辑（有问题的）
    if ($codeInfo['is_used']) {  // 这里会有问题
        if ($codeInfo['bound_user_id'] === null || $codeInfo['bound_user_id'] === '') {
            return ['result' => 'error', 'message' => '激活码状态异常，请联系管理员或稍后重试'];
        }
        return ['result' => 'error', 'message' => '激活码已被其他用户使用'];
    }
    return ['result' => 'success', 'message' => '可以绑定'];
}

function testNewLogic($codeInfo) {
    // 修复后的逻辑（正确的）
    if ((int)$codeInfo['is_used'] === 1) {  // 使用严格类型检查
        if ($codeInfo['bound_user_id'] === null || $codeInfo['bound_user_id'] === '') {
            return ['result' => 'error', 'message' => '激活码状态异常，请联系管理员或稍后重试'];
        }
        return ['result' => 'error', 'message' => '激活码已被其他用户使用'];
    }
    return ['result' => 'success', 'message' => '可以绑定'];
}

try {
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->prepare("
        SELECT id, code, is_used, bound_user_id, status, created_at, expires_at, used_at
        FROM activation_codes 
        WHERE code = ?
    ");
    $stmt->execute([$testCode]);
    $codeInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $codeInfo = false;
    $error = $e->getMessage();
}

if ($codeInfo) {
    $oldResult = testOldLogic($codeInfo);
    $newResult = testNewLogic($codeInfo);
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复效果验证</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
        .warning-box { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; }
        .error-box { background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .mono { font-family: monospace; background: #f5f5f5; padding: 2px 4px; }
        .comparison { display: flex; gap: 20px; }
        .comparison > div { flex: 1; }
    </style>
</head>
<body>
    <h1>🔍 修复效果验证</h1>
    <p><strong>验证时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    <p><strong>测试激活码:</strong> <code><?php echo $testCode; ?></code></p>
    
    <?php if (isset($error)): ?>
        <div class="error-box">
            <h3>❌ 数据库错误</h3>
            <p><?php echo htmlspecialchars($error); ?></p>
        </div>
    <?php elseif ($codeInfo): ?>
        
        <div class="section">
            <h2>📊 激活码当前状态</h2>
            
            <table>
                <tr><th>字段</th><th>原始值</th><th>类型</th><th>说明</th></tr>
                <tr>
                    <td>is_used</td>
                    <td class="mono"><?php echo var_export($codeInfo['is_used'], true); ?></td>
                    <td><?php echo gettype($codeInfo['is_used']); ?></td>
                    <td><?php echo $codeInfo['is_used'] ? '⚠️ 布尔转换为true' : '✅ 布尔转换为false'; ?></td>
                </tr>
                <tr>
                    <td>bound_user_id</td>
                    <td class="mono"><?php echo var_export($codeInfo['bound_user_id'], true); ?></td>
                    <td><?php echo gettype($codeInfo['bound_user_id']); ?></td>
                    <td><?php echo $codeInfo['bound_user_id'] === null ? '✅ 未绑定' : '❌ 已绑定'; ?></td>
                </tr>
                <tr>
                    <td>status</td>
                    <td class="mono"><?php echo htmlspecialchars($codeInfo['status']); ?></td>
                    <td><?php echo gettype($codeInfo['status']); ?></td>
                    <td><?php echo $codeInfo['status'] === 'active' ? '✅ 活跃' : '❌ 非活跃'; ?></td>
                </tr>
            </table>
        </div>
        
        <div class="section">
            <h2>🔄 修复前后对比</h2>
            
            <div class="comparison">
                <div class="<?php echo $oldResult['result'] === 'success' ? 'highlight' : 'error-box'; ?>">
                    <h3>❌ 修复前逻辑</h3>
                    <p><strong>检查条件:</strong> <code>if ($codeInfo['is_used'])</code></p>
                    <p><strong>结果:</strong> <?php echo $oldResult['result'] === 'success' ? '✅ 通过' : '❌ 失败'; ?></p>
                    <p><strong>消息:</strong> <?php echo htmlspecialchars($oldResult['message']); ?></p>
                    <hr>
                    <p><strong>问题分析:</strong></p>
                    <ul>
                        <li>SQLite返回字符串 <code>'FALSE'</code></li>
                        <li>PHP中 <code>'FALSE'</code> 转换为布尔值是 <code>true</code></li>
                        <li>导致条件错误地成立</li>
                        <li>进入错误的处理分支</li>
                    </ul>
                </div>
                
                <div class="<?php echo $newResult['result'] === 'success' ? 'highlight' : 'error-box'; ?>">
                    <h3>✅ 修复后逻辑</h3>
                    <p><strong>检查条件:</strong> <code>if ((int)$codeInfo['is_used'] === 1)</code></p>
                    <p><strong>结果:</strong> <?php echo $newResult['result'] === 'success' ? '✅ 通过' : '❌ 失败'; ?></p>
                    <p><strong>消息:</strong> <?php echo htmlspecialchars($newResult['message']); ?></p>
                    <hr>
                    <p><strong>修复要点:</strong></p>
                    <ul>
                        <li>使用 <code>(int)</code> 强制类型转换</li>
                        <li>使用 <code>===</code> 严格比较</li>
                        <li>字符串 <code>'FALSE'</code> 转换为整数 <code>0</code></li>
                        <li>条件正确地不成立</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>🧪 类型转换测试</h2>
            
            <table>
                <tr><th>表达式</th><th>结果</th><th>说明</th></tr>
                <tr>
                    <td class="mono">$codeInfo['is_used']</td>
                    <td class="mono"><?php echo var_export((bool)$codeInfo['is_used'], true); ?></td>
                    <td>直接布尔转换（有问题）</td>
                </tr>
                <tr>
                    <td class="mono">(int)$codeInfo['is_used']</td>
                    <td class="mono"><?php echo (int)$codeInfo['is_used']; ?></td>
                    <td>整数转换（正确）</td>
                </tr>
                <tr>
                    <td class="mono">(int)$codeInfo['is_used'] === 1</td>
                    <td class="mono"><?php echo var_export((int)$codeInfo['is_used'] === 1, true); ?></td>
                    <td>严格比较结果</td>
                </tr>
            </table>
        </div>
        
        <div class="section">
            <h2>🎯 修复效果总结</h2>
            
            <?php if ($oldResult['result'] !== $newResult['result']): ?>
                <div class="highlight">
                    <h3>🎉 修复成功！</h3>
                    <p><strong>修复前:</strong> <?php echo htmlspecialchars($oldResult['message']); ?></p>
                    <p><strong>修复后:</strong> <?php echo htmlspecialchars($newResult['message']); ?></p>
                    <hr>
                    <h4>✅ 修复效果确认:</h4>
                    <ul>
                        <li>✅ 解决了SQLite布尔字段类型问题</li>
                        <li>✅ 激活码现在可以正常绑定</li>
                        <li>✅ 不再出现误导性错误提示</li>
                        <li>✅ 用户体验得到改善</li>
                    </ul>
                </div>
            <?php else: ?>
                <div class="warning-box">
                    <h3>⚠️ 结果相同</h3>
                    <p>修复前后的结果相同，可能需要进一步检查。</p>
                </div>
            <?php endif; ?>
        </div>
        
    <?php else: ?>
        <div class="error-box">
            <h3>❌ 激活码不存在</h3>
            <p>未找到激活码 <code><?php echo $testCode; ?></code></p>
        </div>
    <?php endif; ?>
    
    <div class="section">
        <h2>🔗 相关工具</h2>
        <ul>
            <li><a href="debug-activation-code.php">激活码数据调试</a></li>
            <li><a href="activation-code-diagnosis.php">激活码状态诊断</a></li>
            <li><a href="../new-lock.html">用户登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
