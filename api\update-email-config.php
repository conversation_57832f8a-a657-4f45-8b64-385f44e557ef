<?php
/**
 * 更新邮件配置API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => '只支持POST请求'
    ]);
    exit;
}

try {
    // 获取POST数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('无效的JSON数据');
    }
    
    $provider = $data['provider'] ?? '';
    $email = $data['email'] ?? '';
    $password = $data['password'] ?? '';
    
    if (empty($provider) || empty($email) || empty($password)) {
        throw new Exception('缺少必要参数：provider, email, password');
    }
    
    // 验证邮件服务商
    $supportedProviders = ['qq', 'gmail', 'outlook', '163', 'aliyun'];
    if (!in_array($provider, $supportedProviders)) {
        throw new Exception('不支持的邮件服务商: ' . $provider);
    }
    
    // 读取配置文件
    $configFile = '../server/email-config.php';
    if (!file_exists($configFile)) {
        throw new Exception('配置文件不存在');
    }
    
    $configContent = file_get_contents($configFile);
    if ($configContent === false) {
        throw new Exception('无法读取配置文件');
    }
    
    // 更新provider
    $configContent = preg_replace(
        "/('provider'\s*=>\s*')[^']*(')/",
        "$1{$provider}$2",
        $configContent
    );
    
    // 更新对应服务商的配置
    switch ($provider) {
        case '163':
            // 更新163配置
            $configContent = preg_replace(
                "/('163'\s*=>\s*\[\s*[^}]*'username'\s*=>\s*')[^']*(')/s",
                "$1{$email}$2",
                $configContent
            );
            $configContent = preg_replace(
                "/('163'\s*=>\s*\[[^}]*'password'\s*=>\s*')[^']*(')/s",
                "$1{$password}$2",
                $configContent
            );
            $configContent = preg_replace(
                "/('163'\s*=>\s*\[[^}]*'from_email'\s*=>\s*')[^']*(')/s",
                "$1{$email}$2",
                $configContent
            );
            break;
            
        case 'gmail':
            // 更新Gmail配置
            $configContent = preg_replace(
                "/('gmail'\s*=>\s*\[\s*[^}]*'username'\s*=>\s*')[^']*(')/s",
                "$1{$email}$2",
                $configContent
            );
            $configContent = preg_replace(
                "/('gmail'\s*=>\s*\[[^}]*'password'\s*=>\s*')[^']*(')/s",
                "$1{$password}$2",
                $configContent
            );
            $configContent = preg_replace(
                "/('gmail'\s*=>\s*\[[^}]*'from_email'\s*=>\s*')[^']*(')/s",
                "$1{$email}$2",
                $configContent
            );
            break;
            
        case 'outlook':
            // 更新Outlook配置
            $configContent = preg_replace(
                "/('outlook'\s*=>\s*\[\s*[^}]*'username'\s*=>\s*')[^']*(')/s",
                "$1{$email}$2",
                $configContent
            );
            $configContent = preg_replace(
                "/('outlook'\s*=>\s*\[[^}]*'password'\s*=>\s*')[^']*(')/s",
                "$1{$password}$2",
                $configContent
            );
            $configContent = preg_replace(
                "/('outlook'\s*=>\s*\[[^}]*'from_email'\s*=>\s*')[^']*(')/s",
                "$1{$email}$2",
                $configContent
            );
            break;
            
        default:
            throw new Exception('暂不支持自动配置此邮件服务商');
    }
    
    // 写入配置文件
    if (file_put_contents($configFile, $configContent) === false) {
        throw new Exception('无法写入配置文件');
    }
    
    // 验证配置
    require_once '../server/email-config.php';
    $config = validateEmailConfig();
    
    if (!$config['valid']) {
        throw new Exception('配置验证失败: ' . $config['error']);
    }
    
    echo json_encode([
        'success' => true,
        'message' => '邮件配置更新成功',
        'data' => [
            'provider' => $provider,
            'email' => $email,
            'smtp_host' => $config['smtp_host'],
            'smtp_port' => $config['smtp_port']
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
