<?php
/**
 * 汽车品牌配置文件
 * 定义支持的汽车品牌及其相关信息
 */

// 品牌配置数组
$brands = [
    'wuling' => [
        'name' => '五菱汽车',
        'english_name' => 'Wuling',
        'path' => 'wuling',
        'logo' => 'images/wuling/logo.png',
        'car_image' => 'images/wuling/upload1.png',
        'description' => '五菱汽车 - 人民需要什么，五菱就造什么',
        'color_theme' => '#ff4757', // 主题色
        'is_active' => true
    ],
    'byd' => [
        'name' => '比亚迪',
        'english_name' => 'BYD',
        'path' => 'byd',
        'logo' => 'images/byd/logo.png',
        'car_image' => 'images/byd/upload1.png',
        'description' => '比亚迪 - 技术为王，创新为本',
        'color_theme' => '#1e90ff',
        'is_active' => true
    ],
    'geely' => [
        'name' => '吉利汽车',
        'english_name' => 'Geely',
        'path' => 'geely',
        'logo' => 'images/geely/logo.png',
        'car_image' => 'images/geely/upload1.png',
        'description' => '吉利汽车 - 让世界充满吉利',
        'color_theme' => '#ff6b35',
        'is_active' => true
    ],
    'chery' => [
        'name' => '奇瑞汽车',
        'english_name' => 'Chery',
        'path' => 'chery',
        'logo' => 'images/chery/logo.png',
        'car_image' => 'images/chery/upload1.png',
        'description' => '奇瑞汽车 - 技术奇瑞，品质生活',
        'color_theme' => '#e74c3c',
        'is_active' => true
    ],
    'changan' => [
        'name' => '长安汽车',
        'english_name' => 'Changan',
        'path' => 'changan',
        'logo' => 'images/changan/logo.png',
        'car_image' => 'images/changan/upload1.png',
        'description' => '长安汽车 - 引领汽车文明，造福人类生活',
        'color_theme' => '#2ecc71',
        'is_active' => true
    ],
    'haval' => [
        'name' => '哈弗',
        'english_name' => 'Haval',
        'path' => 'haval',
        'logo' => 'images/haval/logo.png',
        'car_image' => 'images/haval/upload1.png',
        'description' => '哈弗 - 专业SUV品牌',
        'color_theme' => '#9b59b6',
        'is_active' => true
    ],
    'greatwall' => [
        'name' => '长城汽车',
        'english_name' => 'Great Wall',
        'path' => 'greatwall',
        'logo' => 'images/greatwall/logo.png',
        'car_image' => 'images/greatwall/upload1.png',
        'description' => '长城汽车 - 专注、专业、专家',
        'color_theme' => '#34495e',
        'is_active' => true
    ],
    'mg' => [
        'name' => '名爵',
        'english_name' => 'MG',
        'path' => 'mg',
        'logo' => 'images/mg/logo.png',
        'car_image' => 'images/mg/upload1.png',
        'description' => '名爵 - 百年英伦运动基因',
        'color_theme' => '#e67e22',
        'is_active' => true
    ]
];

/**
 * 获取品牌信息
 * @param string $brandPath 品牌路径标识
 * @return array|null 品牌信息数组，如果不存在返回null
 */
function getBrandInfo($brandPath) {
    global $brands;
    return isset($brands[$brandPath]) ? $brands[$brandPath] : null;
}

/**
 * 获取所有激活的品牌
 * @return array 激活的品牌数组
 */
function getActiveBrands() {
    global $brands;
    return array_filter($brands, function($brand) {
        return $brand['is_active'];
    });
}

/**
 * 验证品牌路径是否有效
 * @param string $brandPath 品牌路径标识
 * @return bool 是否有效
 */
function isValidBrand($brandPath) {
    global $brands;
    return isset($brands[$brandPath]) && $brands[$brandPath]['is_active'];
}

/**
 * 获取默认品牌（五菱）
 * @return array 默认品牌信息
 */
function getDefaultBrand() {
    global $brands;
    return $brands['wuling'];
}

/**
 * 根据URL路径解析品牌
 * @param string $requestUri 请求URI
 * @return string|null 品牌路径标识
 */
function parseBrandFromUrl($requestUri) {
    // 移除查询参数
    $path = parse_url($requestUri, PHP_URL_PATH);
    
    // 匹配 /brand/index.php 或 /brand/ 格式
    if (preg_match('/^\/([a-zA-Z0-9_-]+)\/(index\.php|index-tv-optimized\.php)?/', $path, $matches)) {
        $brandPath = $matches[1];
        if (isValidBrand($brandPath)) {
            return $brandPath;
        }
    }
    
    return null;
}

/**
 * 生成品牌URL
 * @param string $brandPath 品牌路径标识
 * @param string $page 页面名称（默认index.php）
 * @return string 完整URL
 */
function generateBrandUrl($brandPath, $page = 'index.php') {
    return "/{$brandPath}/{$page}";
}
?>
