<?php
/**
 * 简单的邮件配置测试
 */

echo "邮件配置测试开始...\n";

try {
    require_once '../server/email-config.php';
    echo "✅ 配置文件加载成功\n";
    
    $config = getEmailConfig();
    echo "✅ getEmailConfig() 调用成功\n";
    echo "Provider: " . ($GLOBALS['emailConfig']['provider'] ?? 'unknown') . "\n";
    echo "SMTP Host: " . $config['smtp_host'] . "\n";
    echo "From Email: " . $config['from_email'] . "\n";
    
    require_once '../server/email-sender.php';
    echo "✅ EmailSender 类加载成功\n";
    
    $emailSender = new EmailSender();
    echo "✅ EmailSender 对象创建成功\n";
    
    echo "邮件配置测试完成 - 所有测试通过！\n";
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
?>
