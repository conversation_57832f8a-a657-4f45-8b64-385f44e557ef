<?php
/**
 * login_logs自动清理状态检查
 * 检查login_logs表的自动清理机制是否正常工作
 */

header('Content-Type: text/html; charset=utf-8');

$dbPath = '../server/user_system.db3';

// 引入清理助手
require_once '../server/auto-cleanup-helper.php';

function checkLoginLogsAutoCleanup() {
    global $dbPath;
    
    $status = [
        'auto_cleanup_enabled' => false,
        'current_stats' => [],
        'cleanup_config' => [],
        'cleanup_needed' => [],
        'recommendations' => []
    ];
    
    // 检查自动清理是否集成到登录API
    $loginApiPath = '../api/login.php';
    if (file_exists($loginApiPath)) {
        $content = file_get_contents($loginApiPath);
        $status['auto_cleanup_enabled'] = strpos($content, 'autoCleanupOnLogin') !== false;
    }
    
    // 获取清理配置
    $status['cleanup_config'] = [
        'max_records' => 1000,
        'keep_days' => 30,
        'trigger_threshold' => 500,
        'trigger_probability' => '10%',
        'keep_important_logs' => true
    ];
    
    // 获取当前统计
    $status['current_stats'] = getLoginLogsCurrentStats();
    
    // 检查清理需求
    $status['cleanup_needed'] = shouldCleanupLoginLogs($dbPath);
    
    // 生成建议
    $stats = $status['current_stats'];
    if (isset($stats['total_records']) && $stats['total_records'] > 1000) {
        $status['recommendations'][] = [
            'type' => 'urgent',
            'message' => "记录数超过1000条 ({$stats['total_records']}条)，建议立即清理"
        ];
    } elseif (isset($stats['total_records']) && $stats['total_records'] > 500) {
        $status['recommendations'][] = [
            'type' => 'warning', 
            'message' => "记录数接近阈值 ({$stats['total_records']}条)，建议关注"
        ];
    }
    
    if (!$status['auto_cleanup_enabled']) {
        $status['recommendations'][] = [
            'type' => 'error',
            'message' => '自动清理机制未启用，需要手动集成到登录API'
        ];
    }
    
    return $status;
}

function getLoginLogsCurrentStats() {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stats = [];
        
        // 总记录数
        $stmt = $pdo->query("SELECT COUNT(*) FROM login_logs");
        $stats['total_records'] = $stmt->fetchColumn();
        
        // 按状态统计
        $stmt = $pdo->query("
            SELECT status, COUNT(*) as count 
            FROM login_logs 
            GROUP BY status 
            ORDER BY count DESC
        ");
        $stats['by_status'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 时间范围统计
        $timeRanges = [
            '1天内' => '-1 days',
            '7天内' => '-7 days', 
            '30天内' => '-30 days'
        ];
        
        foreach ($timeRanges as $label => $interval) {
            $stmt = $pdo->prepare("
                SELECT COUNT(*) FROM login_logs 
                WHERE login_time >= datetime('now', ?)
            ");
            $stmt->execute([$interval]);
            $stats['time_ranges'][$label] = $stmt->fetchColumn();
        }
        
        // 最新和最旧记录
        $stmt = $pdo->query("SELECT MIN(login_time) as oldest, MAX(login_time) as newest FROM login_logs");
        $timeRange = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['oldest_record'] = $timeRange['oldest'];
        $stats['newest_record'] = $timeRange['newest'];
        
        // 最后清理时间
        $stmt = $pdo->prepare("SELECT config_value FROM system_config WHERE config_key = 'last_cleanup_time'");
        $stmt->execute();
        $stats['last_cleanup'] = $stmt->fetchColumn();
        
        // 数据库大小
        if (file_exists($dbPath)) {
            $stats['db_size'] = filesize($dbPath);
            $stats['db_size_mb'] = round($stats['db_size'] / 1024 / 1024, 2);
        }
        
        return $stats;
        
    } catch (Exception $e) {
        return ['error' => $e->getMessage()];
    }
}

$status = checkLoginLogsAutoCleanup();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>login_logs自动清理状态</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
        .warning-box { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; }
        .error-box { background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0; }
        .status-enabled { background: #d4edda; color: #155724; padding: 5px 10px; border-radius: 3px; }
        .status-disabled { background: #f8d7da; color: #721c24; padding: 5px 10px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>📊 login_logs自动清理状态</h1>
    <p><strong>检查时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="test-section">
        <h2>🤖 自动清理机制状态</h2>
        
        <div class="<?php echo $status['auto_cleanup_enabled'] ? 'highlight' : 'error-box'; ?>">
            <h3><?php echo $status['auto_cleanup_enabled'] ? '✅ 自动清理已启用' : '❌ 自动清理未启用'; ?></h3>
            <?php if ($status['auto_cleanup_enabled']): ?>
                <p>login_logs表的自动清理机制已集成到登录API中，会在用户登录时自动触发清理检查。</p>
            <?php else: ?>
                <p>自动清理机制未启用，login_logs表不会自动清理，可能导致记录数量无限增长。</p>
            <?php endif; ?>
        </div>
        
        <h3>清理配置:</h3>
        <table>
            <tr>
                <th>配置项</th>
                <th>当前值</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>最大保留记录数</td>
                <td><strong><?php echo number_format($status['cleanup_config']['max_records']); ?></strong></td>
                <td>超过此数量会触发按数量清理</td>
            </tr>
            <tr>
                <td>保留天数</td>
                <td><strong><?php echo $status['cleanup_config']['keep_days']; ?>天</strong></td>
                <td>超过此时间的记录会被清理</td>
            </tr>
            <tr>
                <td>触发阈值</td>
                <td><strong><?php echo number_format($status['cleanup_config']['trigger_threshold']); ?></strong></td>
                <td>记录数超过此值时触发清理检查</td>
            </tr>
            <tr>
                <td>触发概率</td>
                <td><strong><?php echo $status['cleanup_config']['trigger_probability']; ?></strong></td>
                <td>登录时触发清理的概率</td>
            </tr>
            <tr>
                <td>保留重要日志</td>
                <td><strong><?php echo $status['cleanup_config']['keep_important_logs'] ? '是' : '否'; ?></strong></td>
                <td>是否优先保留成功登录等重要记录</td>
            </tr>
        </table>
    </div>
    
    <div class="test-section">
        <h2>📈 当前login_logs统计</h2>
        
        <?php if (isset($status['current_stats']['error'])): ?>
            <p class="error">❌ 获取统计失败: <?php echo htmlspecialchars($status['current_stats']['error']); ?></p>
        <?php else: ?>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3><?php echo number_format($status['current_stats']['total_records']); ?></h3>
                    <p>总记录数</p>
                </div>
                <div class="stat-card">
                    <h3><?php echo number_format($status['current_stats']['time_ranges']['7天内'] ?? 0); ?></h3>
                    <p>最近7天</p>
                </div>
                <div class="stat-card">
                    <h3><?php echo number_format($status['current_stats']['time_ranges']['30天内'] ?? 0); ?></h3>
                    <p>最近30天</p>
                </div>
                <div class="stat-card">
                    <h3><?php echo $status['current_stats']['db_size_mb'] ?? 0; ?> MB</h3>
                    <p>数据库大小</p>
                </div>
            </div>
            
            <?php
            $totalRecords = $status['current_stats']['total_records'];
            $maxRecords = $status['cleanup_config']['max_records'];
            $usageRatio = round(($totalRecords / $maxRecords) * 100, 1);
            ?>
            
            <div class="<?php echo $totalRecords > $maxRecords ? 'error-box' : ($totalRecords > $maxRecords * 0.8 ? 'warning-box' : 'highlight'); ?>">
                <h3><?php echo $totalRecords > $maxRecords ? '🔴 记录数超限' : ($totalRecords > $maxRecords * 0.8 ? '🟡 记录数较多' : '✅ 记录数正常'); ?></h3>
                <p><strong>当前记录数:</strong> <?php echo number_format($totalRecords); ?> / <?php echo number_format($maxRecords); ?> (<?php echo $usageRatio; ?>%)</p>
                <p><strong>最后清理时间:</strong> <?php echo $status['current_stats']['last_cleanup'] ?? '从未清理'; ?></p>
                <?php if ($totalRecords > $maxRecords): ?>
                    <p><strong>建议:</strong> 立即执行清理，记录数已超过配置的最大值</p>
                <?php elseif ($totalRecords > $maxRecords * 0.8): ?>
                    <p><strong>建议:</strong> 考虑执行清理，记录数接近上限</p>
                <?php endif; ?>
            </div>
            
            <h3>按状态统计:</h3>
            <table>
                <tr>
                    <th>状态</th>
                    <th>记录数</th>
                    <th>占比</th>
                </tr>
                <?php foreach ($status['current_stats']['by_status'] as $statusItem): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($statusItem['status']); ?></td>
                        <td><?php echo number_format($statusItem['count']); ?></td>
                        <td><?php echo round(($statusItem['count'] / $totalRecords) * 100, 1); ?>%</td>
                    </tr>
                <?php endforeach; ?>
            </table>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>🎯 清理需求分析</h2>
        
        <?php if ($status['cleanup_needed']['needs_cleanup']): ?>
            <div class="error-box">
                <h3>🔴 需要立即清理</h3>
                <p><strong>原因:</strong> <?php echo htmlspecialchars($status['cleanup_needed']['reason']); ?></p>
                <p><strong>建议:</strong> 立即执行手动清理或等待下次登录时自动触发</p>
            </div>
        <?php else: ?>
            <div class="highlight">
                <h3>✅ 暂无清理需求</h3>
                <p>当前记录数在合理范围内，自动清理机制正常工作。</p>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>💡 优化建议</h2>
        
        <?php if (empty($status['recommendations'])): ?>
            <div class="highlight">
                <h3>✅ 系统状态良好</h3>
                <p>login_logs表状态正常，自动清理机制运行良好。</p>
            </div>
        <?php else: ?>
            <?php foreach ($status['recommendations'] as $rec): ?>
                <div class="<?php echo $rec['type'] === 'urgent' || $rec['type'] === 'error' ? 'error-box' : ($rec['type'] === 'warning' ? 'warning-box' : 'highlight'); ?>">
                    <p><strong><?php echo ucfirst($rec['type']); ?>:</strong> <?php echo htmlspecialchars($rec['message']); ?></p>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>🔄 自动清理工作原理</h2>
        
        <h3>触发机制:</h3>
        <ul>
            <li><strong>登录时触发</strong> - 用户成功登录时10%概率检查清理需求</li>
            <li><strong>条件检查</strong> - 记录数超过500条或距离上次清理超过7天</li>
            <li><strong>智能清理</strong> - 优先删除失败记录，保留重要日志</li>
        </ul>
        
        <h3>清理策略:</h3>
        <ul>
            <li><strong>按时间清理</strong> - 删除30天前的失败登录记录</li>
            <li><strong>按数量清理</strong> - 记录数超过1000时保留最新800条</li>
            <li><strong>保护重要日志</strong> - 优先保留成功登录、密码重置等记录</li>
        </ul>
        
        <h3>清理效果:</h3>
        <ul>
            <li>自动维持记录数在合理范围内（≤1000条）</li>
            <li>保留最近30天的重要登录历史</li>
            <li>优化数据库查询性能</li>
            <li>减少存储空间占用</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关工具</h2>
        <ul>
            <li><a href="login-logs-manager.php">login_logs管理界面</a></li>
            <li><a href="session-auto-cleanup-status.php">Session自动清理状态</a></li>
            <li><a href="security-check.php">安全检查工具</a></li>
            <li><a href="../new-lock.html">返回登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
