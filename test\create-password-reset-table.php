<?php
/**
 * 创建密码重置表
 */

echo "🔧 创建密码重置表...\n\n";

try {
    // 连接数据库
    $db = new PDO('sqlite:../server/user_system.db3');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 创建密码重置表
    $sql = "
    CREATE TABLE IF NOT EXISTS password_resets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        token TEXT NOT NULL UNIQUE,
        expires_at DATETIME NOT NULL,
        created_at DATETIME NOT NULL,
        used BOOLEAN DEFAULT 0,
        used_at DATETIME NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    $db->exec($sql);
    
    echo "✅ 密码重置表创建成功\n";
    
    // 创建索引
    $db->exec("CREATE INDEX IF NOT EXISTS idx_password_resets_token ON password_resets(token)");
    $db->exec("CREATE INDEX IF NOT EXISTS idx_password_resets_user_id ON password_resets(user_id)");
    $db->exec("CREATE INDEX IF NOT EXISTS idx_password_resets_expires_at ON password_resets(expires_at)");
    
    echo "✅ 索引创建成功\n";
    
    // 验证表结构
    $stmt = $db->query("PRAGMA table_info(password_resets)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\n📋 表结构验证:\n";
    echo "==================\n";
    foreach ($columns as $column) {
        echo "   " . $column['name'] . " (" . $column['type'] . ")\n";
    }
    
    echo "\n🎉 密码重置表准备完成！\n";
    
} catch (Exception $e) {
    echo "❌ 创建失败: " . $e->getMessage() . "\n";
    exit(1);
}
?>
