<?php
/**
 * 直接测试密码找回API
 */

echo "🔐 直接测试密码找回API...\n\n";

// 模拟POST请求数据
$_POST['username'] = 'ataehee1';
$_POST['email'] = '<EMAIL>';
$_POST['captcha'] = 'test'; // 我们需要先生成一个有效的验证码

// 设置请求方法
$_SERVER['REQUEST_METHOD'] = 'POST';

// 启动会话
session_start();

echo "1. 生成测试验证码:\n";
echo "==================\n";

try {
    // 连接数据库
    $db = new PDO('sqlite:../server/user_system.db3');
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 生成测试验证码
    $testCode = 'TEST';
    $codeHash = hash('sha256', strtoupper($testCode));
    $sessionId = session_id();
    $expiresAt = date('Y-m-d H:i:s', time() + 300); // 5分钟有效期
    
    // 清除旧的验证码
    $stmt = $db->prepare("DELETE FROM captcha_codes WHERE session_id = ?");
    $stmt->execute([$sessionId]);
    
    // 插入新验证码
    $stmt = $db->prepare("
        INSERT INTO captcha_codes (code_hash, session_id, expires_at, created_at, is_used)
        VALUES (?, ?, ?, ?, 0)
    ");
    $stmt->execute([$codeHash, $sessionId, $expiresAt, date('Y-m-d H:i:s')]);
    
    echo "✅ 测试验证码生成成功\n";
    echo "   验证码: $testCode\n";
    echo "   会话ID: $sessionId\n";
    echo "   过期时间: $expiresAt\n";
    
    // 更新POST数据
    $_POST['captcha'] = $testCode;
    
} catch (Exception $e) {
    echo "❌ 验证码生成失败: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n" . str_repeat("=", 50) . "\n\n";

echo "2. 调用密码找回API:\n";
echo "==================\n";

// 捕获输出
ob_start();

try {
    // 包含API文件
    include '../api/forgot-password.php';
} catch (Exception $e) {
    echo "❌ API调用异常: " . $e->getMessage() . "\n";
}

// 获取API输出
$apiOutput = ob_get_clean();

echo "API响应:\n";
echo "----------\n";
echo $apiOutput . "\n";

// 尝试解析JSON响应
try {
    $response = json_decode($apiOutput, true);
    if ($response) {
        echo "\n解析后的响应:\n";
        echo "----------\n";
        echo "成功: " . ($response['success'] ? '是' : '否') . "\n";
        echo "消息: " . $response['message'] . "\n";
        if (isset($response['debug'])) {
            echo "调试信息: " . print_r($response['debug'], true) . "\n";
        }
    } else {
        echo "\n❌ JSON解析失败\n";
    }
} catch (Exception $e) {
    echo "\n❌ 响应解析错误: " . $e->getMessage() . "\n";
}

echo "\n🎉 API测试完成！\n";
?>
