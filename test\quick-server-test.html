<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器验证码问题快速测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #555;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .test-item h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background: #e0a800;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .captcha-test {
            text-align: center;
            margin: 20px 0;
        }
        .captcha-test img {
            border: 2px solid #ddd;
            border-radius: 5px;
            margin: 10px;
            max-width: 150px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
            border: 1px solid #e9ecef;
        }
        .instructions {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            margin: 20px 0;
        }
        .instructions h3 {
            margin-top: 0;
            color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 服务器验证码问题快速测试</h1>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <p><strong>问题描述：</strong>验证码在本地localhost:8080正常显示，但在服务器上无法显示</p>
            <p><strong>可能原因：</strong>服务器PHP的GD扩展未正确配置或加载</p>
            <p><strong>测试步骤：</strong></p>
            <ol>
                <li>首先运行"服务器环境检查"，查看整体环境状态</li>
                <li>然后运行"GD扩展详细检查"，专门检查GD扩展</li>
                <li>测试不同格式的验证码，找到可用的方案</li>
                <li>根据检查结果选择解决方案</li>
            </ol>
        </div>
    </div>

    <div class="container">
        <h2>🔧 环境检查工具</h2>
        <div class="test-grid">
            <div class="test-item">
                <h3>🌐 服务器环境全面检查</h3>
                <p>检查PHP版本、扩展、配置、权限等</p>
                <a href="server-check.php" target="_blank" class="btn">运行检查</a>
            </div>
            
            <div class="test-item">
                <h3>🎨 GD扩展详细检查</h3>
                <p>专门检查GD扩展状态和图像处理功能</p>
                <a href="check-gd-extension.php" target="_blank" class="btn">检查GD扩展</a>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🖼️ 验证码测试</h2>
        <div class="captcha-test">
            <h3>测试不同格式的验证码</h3>
            <p>点击下面的链接测试不同格式的验证码是否能正常显示：</p>
            
            <div style="display: flex; justify-content: center; flex-wrap: wrap; gap: 20px; margin: 20px 0;">
                <div style="text-align: center;">
                    <h4>原始验证码 (GD)</h4>
                    <img src="../api/captcha.php?t=1" alt="原始验证码" id="captcha1">
                    <br>
                    <a href="../api/captcha.php" target="_blank" class="btn">测试原始验证码</a>
                    <button onclick="refreshCaptcha('captcha1', '../api/captcha.php')" class="btn btn-warning">刷新</button>
                </div>
                
                <div style="text-align: center;">
                    <h4>备用验证码 (SVG)</h4>
                    <img src="../api/captcha-fallback.php?format=svg&t=1" alt="SVG验证码" id="captcha2">
                    <br>
                    <a href="../api/captcha-fallback.php?format=svg" target="_blank" class="btn btn-success">测试SVG验证码</a>
                    <button onclick="refreshCaptcha('captcha2', '../api/captcha-fallback.php?format=svg')" class="btn btn-warning">刷新</button>
                </div>
                
                <div style="text-align: center;">
                    <h4>自动检测验证码</h4>
                    <img src="../api/captcha-fallback.php?t=1" alt="自动检测验证码" id="captcha3">
                    <br>
                    <a href="../api/captcha-fallback.php" target="_blank" class="btn btn-success">测试自动检测</a>
                    <button onclick="refreshCaptcha('captcha3', '../api/captcha-fallback.php')" class="btn btn-warning">刷新</button>
                </div>
            </div>
            
            <div style="text-align: center; margin: 20px 0;">
                <h4>纯文本验证码</h4>
                <div class="code" id="textCaptcha">加载中...</div>
                <a href="../api/captcha-fallback.php?format=text" target="_blank" class="btn btn-warning">查看文本验证码</a>
                <button onclick="loadTextCaptcha()" class="btn btn-warning">刷新文本</button>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>💡 解决方案</h2>
        <div class="test-grid">
            <div class="test-item">
                <h3>🚀 立即可用方案</h3>
                <p>如果GD扩展有问题，可以立即使用SVG验证码</p>
                <div class="code">
                    // 在前端代码中替换验证码URL<br>
                    // 从: ../api/captcha.php<br>
                    // 改为: ../api/captcha-fallback.php?format=svg
                </div>
                <div class="status warning">
                    <strong>优点：</strong>无需服务器配置，立即可用<br>
                    <strong>缺点：</strong>安全性略低于图像验证码
                </div>
            </div>
            
            <div class="test-item">
                <h3>🔧 根本解决方案</h3>
                <p>修复服务器GD扩展配置</p>
                <div class="code">
                    1. 编辑 php.ini 文件<br>
                    2. 确保 extension=gd 已启用<br>
                    3. 重启Web服务器<br>
                    4. 运行检查工具验证
                </div>
                <div class="status success">
                    <strong>优点：</strong>完全兼容，安全性最高<br>
                    <strong>缺点：</strong>需要服务器管理权限
                </div>
            </div>
            
            <div class="test-item">
                <h3>🔄 自动切换方案</h3>
                <p>使用智能检测，自动选择最佳方案</p>
                <div class="code">
                    // 使用自动检测API<br>
                    // URL: ../api/captcha-fallback.php<br>
                    // 自动检测GD扩展并选择最佳方案
                </div>
                <div class="status success">
                    <strong>优点：</strong>兼容性最好，自动适应<br>
                    <strong>缺点：</strong>需要更新前端代码
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📞 技术支持</h2>
        <div class="status warning">
            <h3>如果问题仍未解决：</h3>
            <ol>
                <li>运行所有检查工具，截图保存结果</li>
                <li>测试所有验证码格式，记录哪些可用</li>
                <li>检查服务器错误日志</li>
                <li>联系服务器管理员或主机商</li>
            </ol>
        </div>
    </div>

    <script>
        // 刷新验证码图片
        function refreshCaptcha(imgId, url) {
            const img = document.getElementById(imgId);
            img.src = url + '?t=' + new Date().getTime();
        }
        
        // 加载文本验证码
        function loadTextCaptcha() {
            fetch('../api/captcha-fallback.php?format=text&t=' + new Date().getTime())
                .then(response => response.text())
                .then(data => {
                    document.getElementById('textCaptcha').textContent = data;
                })
                .catch(error => {
                    document.getElementById('textCaptcha').textContent = '加载失败: ' + error.message;
                });
        }
        
        // 页面加载时自动加载文本验证码
        window.onload = function() {
            loadTextCaptcha();
        };
        
        // 自动刷新验证码图片（每30秒）
        setInterval(function() {
            refreshCaptcha('captcha1', '../api/captcha.php');
            refreshCaptcha('captcha2', '../api/captcha-fallback.php?format=svg');
            refreshCaptcha('captcha3', '../api/captcha-fallback.php');
        }, 30000);
    </script>
</body>
</html>
