<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>激活码本地测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border-left: 4px solid #00ff88;
        }
        
        .code-list {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .code-item {
            font-family: 'Courier New', monospace;
            font-size: 16px;
            margin: 8px 0;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .code-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(5px);
        }
        
        .test-input {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            border: none;
            border-radius: 8px;
            margin: 10px 0;
            text-align: center;
            font-family: 'Courier New', monospace;
            letter-spacing: 2px;
        }
        
        .test-button {
            width: 100%;
            padding: 15px;
            font-size: 18px;
            background: linear-gradient(135deg, #00ff88, #00cc6a);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 136, 0.4);
        }
        
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        
        .success {
            background: rgba(0, 255, 136, 0.2);
            border: 1px solid #00ff88;
        }
        
        .error {
            background: rgba(255, 82, 82, 0.2);
            border: 1px solid #ff5252;
        }
        
        .info {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
        }
        
        .fingerprint {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            word-break: break-all;
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .links {
            text-align: center;
            margin-top: 30px;
        }
        
        .links a {
            color: #00ff88;
            text-decoration: none;
            margin: 0 15px;
            padding: 10px 20px;
            border: 1px solid #00ff88;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .links a:hover {
            background: #00ff88;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 激活码本地测试</h1>
        
        <div class="test-section">
            <h3>📱 设备指纹信息</h3>
            <div id="deviceInfo" class="fingerprint">生成中...</div>
        </div>
        
        <div class="test-section">
            <h3>🔑 测试激活码列表</h3>
            <p>点击任意激活码自动填入测试框：</p>
            <div class="code-list">
                <div class="code-item" onclick="fillCode('ABCDE-FGHIJ-KLMNO-PQRST')">1. ABCDE-FGHIJ-KLMNO-PQRST</div>
                <div class="code-item" onclick="fillCode('TEST1-TEST2-TEST3-TEST4')">2. TEST1-TEST2-TEST3-TEST4</div>
                <div class="code-item" onclick="fillCode('DEMO1-DEMO2-DEMO3-DEMO4')">3. DEMO1-DEMO2-DEMO3-DEMO4</div>
                <div class="code-item" onclick="fillCode('VALID-CODE1-CODE2-CODE3')">4. VALID-CODE1-CODE2-CODE3</div>
                <div class="code-item" onclick="fillCode('QUICK-TEST5-TEST6-TEST7')">5. QUICK-TEST5-TEST6-TEST7</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔓 激活码验证测试</h3>
            <input type="text" 
                   id="testCode" 
                   class="test-input" 
                   placeholder="输入激活码或点击上方列表选择"
                   maxlength="23">
            <button class="test-button" onclick="testActivation()">🚀 测试激活码</button>
            <div id="testResult"></div>
        </div>
        
        <div class="test-section">
            <h3>ℹ️ 测试说明</h3>
            <p><strong>本地测试模式：</strong></p>
            <ul>
                <li>✅ 无需启动后端服务器</li>
                <li>✅ 模拟完整的激活码验证流程</li>
                <li>✅ 测试设备指纹生成</li>
                <li>✅ 验证前端逻辑正确性</li>
            </ul>
            
            <p><strong>测试结果：</strong></p>
            <ul>
                <li>🟢 成功：激活码格式正确且通过验证</li>
                <li>🔴 失败：激活码格式错误或验证失败</li>
                <li>🟡 警告：网络连接问题（正常，因为是本地测试）</li>
            </ul>
        </div>
        
        <div class="links">
            <a href="../lock.html">🔐 锁屏页面</a>
            <a href="../index.php">🏠 主页面</a>
            <a href="test-unlock-system.html">🧪 完整测试</a>
        </div>
    </div>

    <script src="../js/device-fingerprint.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('🧪 本地测试页面加载完成');
            await generateDeviceInfo();
        });

        async function generateDeviceInfo() {
            try {
                const fingerprint = await window.deviceFingerprint.generateFingerprint();
                const deviceInfo = window.deviceFingerprint.getDeviceInfoString();
                
                document.getElementById('deviceInfo').innerHTML = `
                    <strong>设备指纹:</strong> ${fingerprint.substring(0, 16)}...<br>
                    <strong>设备信息:</strong> ${deviceInfo}<br>
                    <strong>屏幕分辨率:</strong> ${screen.width}x${screen.height}<br>
                    <strong>浏览器:</strong> ${navigator.userAgent.split(' ')[0]}
                `;
                
                console.log('✅ 设备指纹生成成功');
            } catch (error) {
                console.error('❌ 设备指纹生成失败:', error);
                document.getElementById('deviceInfo').textContent = '设备指纹生成失败: ' + error.message;
            }
        }

        function fillCode(code) {
            document.getElementById('testCode').value = code;
            showResult('已选择激活码: ' + code, 'info');
        }

        async function testActivation() {
            const code = document.getElementById('testCode').value.trim().toUpperCase();
            
            if (!code) {
                showResult('❌ 请输入激活码', 'error');
                return;
            }

            // 验证激活码格式
            const pattern = /^[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}$/;
            if (!pattern.test(code)) {
                showResult('❌ 激活码格式错误，正确格式: XXXXX-XXXXX-XXXXX-XXXXX', 'error');
                return;
            }

            showResult('🔄 正在验证激活码...', 'info');

            // 模拟验证过程
            setTimeout(() => {
                // 本地测试：预设的激活码都视为有效
                const validCodes = [
                    'ABCDE-FGHIJ-KLMNO-PQRST',
                    'TEST1-TEST2-TEST3-TEST4',
                    'DEMO1-DEMO2-DEMO3-DEMO4',
                    'VALID-CODE1-CODE2-CODE3',
                    'QUICK-TEST5-TEST6-TEST7'
                ];

                if (validCodes.includes(code)) {
                    showResult(`✅ 激活码验证成功！<br>
                        📱 设备已绑定<br>
                        ⏰ 有效期：10天<br>
                        🎉 可以正常访问主页面`, 'success');
                    
                    // 保存测试解锁状态
                    const unlockData = {
                        activationCode: code,
                        deviceFingerprint: window.deviceFingerprint.getFingerprint(),
                        unlockTime: Date.now(),
                        expiresAt: Date.now() + (10 * 24 * 60 * 60 * 1000), // 10天后
                        isTest: true
                    };
                    
                    try {
                        localStorage.setItem('unlockStatus', JSON.stringify(unlockData));
                        console.log('✅ 测试解锁状态已保存');
                    } catch (e) {
                        console.warn('⚠️ 无法保存到本地存储');
                    }
                    
                } else {
                    showResult('❌ 激活码无效或已过期', 'error');
                }
            }, 1500);
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = message;
            resultDiv.className = `result ${type}`;
        }

        // 自动格式化激活码输入
        document.getElementById('testCode').addEventListener('input', function(e) {
            let value = e.target.value.toUpperCase();
            value = value.replace(/[^A-Z0-9]/g, '');
            
            if (value.length > 0) {
                value = value.match(/.{1,5}/g).join('-');
                if (value.length > 23) {
                    value = value.substring(0, 23);
                }
            }
            
            e.target.value = value;
        });
    </script>
</body>
</html>
