<?php
/**
 * 最终验证工具
 * 确认所有test_email_system引用已完全清除
 */

header('Content-Type: text/html; charset=utf-8');

function finalVerification() {
    $result = [
        'old_references' => [],
        'new_references' => [],
        'total_files_scanned' => 0,
        'summary' => []
    ];
    
    try {
        $projectRoot = dirname(__DIR__);
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($projectRoot, RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && preg_match('/\.(php|html|js|css|json|md)$/i', $file->getFilename())) {
                $filePath = $file->getPathname();
                $relativePath = str_replace($projectRoot . DIRECTORY_SEPARATOR, '', $filePath);
                
                // 跳过vendor目录和一些不需要检查的目录
                if (strpos($relativePath, 'vendor' . DIRECTORY_SEPARATOR) === 0 ||
                    strpos($relativePath, '.git' . DIRECTORY_SEPARATOR) === 0 ||
                    strpos($relativePath, 'node_modules' . DIRECTORY_SEPARATOR) === 0) {
                    continue;
                }
                
                $result['total_files_scanned']++;
                $content = file_get_contents($filePath);
                
                // 检查任何test_email_system引用
                if (strpos($content, 'test_email_system') !== false) {
                    $result['old_references'][] = [
                        'file' => $relativePath,
                        'type' => 'test_email_system引用'
                    ];
                }
                
                // 检查新的vendor引用
                if (strpos($content, 'vendor/autoload.php') !== false) {
                    $result['new_references'][] = [
                        'file' => $relativePath,
                        'type' => 'vendor/autoload.php引用'
                    ];
                }
            }
        }
        
        // 生成总结
        $result['summary'] = [
            'total_scanned' => $result['total_files_scanned'],
            'old_count' => count($result['old_references']),
            'new_count' => count($result['new_references']),
            'migration_complete' => count($result['old_references']) === 0
        ];
        
    } catch (Exception $e) {
        $result['error'] = $e->getMessage();
    }
    
    return $result;
}

$verification = finalVerification();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终验证 - PHPMailer迁移完成</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
        .warning-box { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; }
        .stats-box { background: #f0f7ff; padding: 15px; border-left: 4px solid #2196F3; margin: 15px 0; }
    </style>
</head>
<body>
    <h1>🎯 最终验证 - PHPMailer迁移完成</h1>
    <p><strong>验证时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="stats-box">
        <h3>📊 扫描统计</h3>
        <p><strong>扫描文件数:</strong> <?php echo $verification['summary']['total_scanned']; ?> 个</p>
        <p><strong>旧引用数:</strong> <?php echo $verification['summary']['old_count']; ?> 处</p>
        <p><strong>新引用数:</strong> <?php echo $verification['summary']['new_count']; ?> 处</p>
        <p><strong>迁移状态:</strong> 
            <?php if ($verification['summary']['migration_complete']): ?>
                <span class="success">✅ 完成</span>
            <?php else: ?>
                <span class="warning">⚠️ 未完成</span>
            <?php endif; ?>
        </p>
    </div>
    
    <?php if ($verification['summary']['migration_complete']): ?>
        <div class="highlight">
            <h3>🎉 迁移完成！</h3>
            <p class="success">✅ 所有test_email_system引用已成功清除</p>
            <p class="success">✅ 项目已完全独立，可以安全部署</p>
            
            <h4>迁移成果:</h4>
            <ul>
                <li>✅ PHPMailer依赖已复制到本项目vendor目录</li>
                <li>✅ 所有文件路径引用已更新为本地路径</li>
                <li>✅ 项目不再依赖外部test_email_system项目</li>
                <li>✅ 可以直接部署到任何服务器环境</li>
            </ul>
        </div>
    <?php else: ?>
        <div class="warning-box">
            <h3>⚠️ 仍有未完成的迁移</h3>
            <p class="warning">发现 <?php echo $verification['summary']['old_count']; ?> 处旧引用需要处理</p>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($verification['old_references'])): ?>
        <div class="test-section">
            <h2>🔍 剩余的旧引用</h2>
            <table>
                <tr>
                    <th>文件路径</th>
                    <th>引用类型</th>
                    <th>建议操作</th>
                </tr>
                <?php foreach ($verification['old_references'] as $ref): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($ref['file']); ?></td>
                        <td><?php echo htmlspecialchars($ref['type']); ?></td>
                        <td>
                            <?php if (strpos($ref['file'], 'test/') === 0): ?>
                                <span class="info">测试文件 - 可能是说明文字</span>
                            <?php else: ?>
                                <span class="warning">需要手动更新</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($verification['new_references'])): ?>
        <div class="test-section">
            <h2>✅ 新的本地引用</h2>
            <table>
                <tr>
                    <th>文件路径</th>
                    <th>引用类型</th>
                    <th>状态</th>
                </tr>
                <?php foreach ($verification['new_references'] as $ref): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($ref['file']); ?></td>
                        <td><?php echo htmlspecialchars($ref['type']); ?></td>
                        <td><span class="success">✅ 正确</span></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
    <?php endif; ?>
    
    <div class="test-section">
        <h2>📋 迁移总结</h2>
        
        <h3>已更新的关键文件:</h3>
        <ul>
            <li>✅ <strong>server/working-email-sender.php</strong> - 主要邮件发送器</li>
            <li>✅ <strong>server/phpmailer-email-sender.php</strong> - PHPMailer邮件发送器</li>
            <li>✅ <strong>test/verify-phpmailer-path.php</strong> - 路径验证工具</li>
            <li>✅ <strong>test/test-working-email-sender.php</strong> - 邮件发送器测试</li>
            <li>✅ <strong>test/check-paths.php</strong> - 路径检查工具</li>
            <li>✅ <strong>test/test-forgot-password-fixed.php</strong> - 密码找回测试</li>
        </ul>
        
        <h3>路径迁移:</h3>
        <table>
            <tr>
                <th>组件</th>
                <th>旧路径</th>
                <th>新路径</th>
            </tr>
            <tr>
                <td>PHPMailer自动加载</td>
                <td><code>../../test_email_system/vendor/autoload.php</code></td>
                <td><code>../vendor/autoload.php</code></td>
            </tr>
            <tr>
                <td>邮件配置</td>
                <td><code>../../test_email_system/config/email.php</code></td>
                <td><code>email-config.php</code></td>
            </tr>
        </table>
        
        <h3>部署优势:</h3>
        <ul>
            <li>✅ <strong>完全独立:</strong> 不依赖任何外部项目</li>
            <li>✅ <strong>简单部署:</strong> 复制整个项目目录即可</li>
            <li>✅ <strong>版本固定:</strong> PHPMailer版本锁定，避免兼容性问题</li>
            <li>✅ <strong>路径统一:</strong> 所有引用使用项目内相对路径</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🧪 功能测试</h2>
        <p>现在可以测试完整的功能:</p>
        <ol>
            <li><strong>测试本地PHPMailer:</strong> <a href="test-local-phpmailer.php">test-local-phpmailer.php</a></li>
            <li><strong>测试邮件发送器:</strong> <a href="test-working-email-sender.php">test-working-email-sender.php</a></li>
            <li><strong>测试密码找回:</strong> <a href="../new-lock.html">登录页面 → 忘记密码</a></li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>🚀 部署指南</h2>
        <h3>服务器部署步骤:</h3>
        <ol>
            <li><strong>复制项目:</strong> 将整个项目目录复制到服务器</li>
            <li><strong>设置权限:</strong> 确保PHP可以读取vendor目录</li>
            <li><strong>配置邮件:</strong> 根据需要调整server/email-config.php</li>
            <li><strong>测试功能:</strong> 访问登录页面测试密码找回功能</li>
        </ol>
        
        <h3>不再需要:</h3>
        <ul>
            <li>❌ 在服务器上安装Composer</li>
            <li>❌ 运行composer install</li>
            <li>❌ 配置外部依赖路径</li>
            <li>❌ 担心PHPMailer版本兼容性</li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
