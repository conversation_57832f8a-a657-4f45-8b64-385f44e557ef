<?php
/**
 * 检查激活码表结构
 */

$dbPath = '../server/user_system.db3';

try {
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>激活码表结构检查</h2>";
    
    // 检查表是否存在
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='activation_codes'");
    if ($stmt->fetch()) {
        echo "<p>✅ activation_codes表存在</p>";
        
        // 获取表结构
        $stmt = $pdo->query("PRAGMA table_info(activation_codes)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>表结构：</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>列名</th><th>类型</th><th>非空</th><th>默认值</th><th>主键</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['name']}</td>";
            echo "<td>{$column['type']}</td>";
            echo "<td>" . ($column['notnull'] ? '是' : '否') . "</td>";
            echo "<td>{$column['dflt_value']}</td>";
            echo "<td>" . ($column['pk'] ? '是' : '否') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 查看表中的数据（使用正确的字段名）
        $stmt = $pdo->query("SELECT * FROM activation_codes ORDER BY created_at DESC LIMIT 5");
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>最新5条数据：</h3>";
        if (empty($data)) {
            echo "<p>表中没有数据</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            $headers = array_keys($data[0]);
            echo "<tr>";
            foreach ($headers as $header) {
                echo "<th>$header</th>";
            }
            echo "</tr>";
            
            foreach ($data as $row) {
                echo "<tr>";
                foreach ($row as $value) {
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<p>❌ activation_codes表不存在</p>";
        
        // 显示所有表
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<h3>数据库中的所有表：</h3>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ 数据库错误: " . $e->getMessage() . "</p>";
}
?>
