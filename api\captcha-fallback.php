<?php
/**
 * 验证码备用方案
 * 当GD扩展不可用时使用纯文本验证码
 */

session_start();

// 数据库配置
$dbPath = '../server/user_system.db3';

/**
 * 生成随机验证码
 */
function generateCaptchaCode($length = 4) {
    $charset = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
    $code = '';
    for ($i = 0; $i < $length; $i++) {
        $code .= $charset[random_int(0, strlen($charset) - 1)];
    }
    return $code;
}

/**
 * 创建SVG验证码图片
 */
function createSVGCaptcha($code) {
    $width = 120;
    $height = 40;
    
    $svg = '<?xml version="1.0" encoding="UTF-8"?>';
    $svg .= '<svg width="' . $width . '" height="' . $height . '" xmlns="http://www.w3.org/2000/svg">';
    
    // 背景
    $svg .= '<rect width="100%" height="100%" fill="#f0f0f0" stroke="#ccc" stroke-width="1"/>';
    
    // 添加干扰线
    for ($i = 0; $i < 3; $i++) {
        $x1 = random_int(0, $width);
        $y1 = random_int(0, $height);
        $x2 = random_int(0, $width);
        $y2 = random_int(0, $height);
        $svg .= '<line x1="' . $x1 . '" y1="' . $y1 . '" x2="' . $x2 . '" y2="' . $y2 . '" stroke="#ddd" stroke-width="1"/>';
    }
    
    // 添加验证码文字
    $x = 15;
    for ($i = 0; $i < strlen($code); $i++) {
        $y = random_int(20, 30);
        $rotate = random_int(-15, 15);
        $color = '#' . sprintf('%02x%02x%02x', random_int(50, 100), random_int(50, 100), random_int(50, 100));
        
        $svg .= '<text x="' . $x . '" y="' . $y . '" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="' . $color . '" transform="rotate(' . $rotate . ' ' . $x . ' ' . $y . ')">';
        $svg .= $code[$i];
        $svg .= '</text>';
        
        $x += 22;
    }
    
    $svg .= '</svg>';
    
    return $svg;
}

/**
 * 创建纯文本验证码
 */
function createTextCaptcha($code) {
    return "验证码: " . implode(' ', str_split($code));
}

/**
 * 保存验证码到数据库
 */
function saveCaptchaToDatabase($code, $sessionId) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 清除过期的验证码
        $pdo->exec("DELETE FROM captcha_codes WHERE expires_at < datetime('now')");
        
        // 清除同一会话的旧验证码
        $stmt = $pdo->prepare("DELETE FROM captcha_codes WHERE session_id = ?");
        $stmt->execute([$sessionId]);
        
        // 保存新验证码
        $codeHash = hash('sha256', strtoupper($code));
        $expiresAt = date('Y-m-d H:i:s', time() + 300); // 5分钟过期
        
        $stmt = $pdo->prepare("
            INSERT INTO captcha_codes (code_hash, session_id, expires_at)
            VALUES (?, ?, ?)
        ");
        $stmt->execute([$codeHash, $sessionId, $expiresAt]);
        
        return true;
    } catch (Exception $e) {
        error_log("保存验证码失败: " . $e->getMessage());
        return false;
    }
}

try {
    // 生成验证码
    $captchaCode = generateCaptchaCode(4);
    
    // 获取会话ID
    $sessionId = session_id();
    
    // 保存到数据库
    if (!saveCaptchaToDatabase($captchaCode, $sessionId)) {
        throw new Exception('保存验证码失败');
    }
    
    // 检查输出格式
    $format = $_GET['format'] ?? 'image';
    
    if ($format === 'svg') {
        // 输出SVG格式
        header('Content-Type: image/svg+xml');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        echo createSVGCaptcha($captchaCode);
        
    } elseif ($format === 'text') {
        // 输出纯文本格式
        header('Content-Type: text/plain; charset=utf-8');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        echo createTextCaptcha($captchaCode);
        
    } elseif (extension_loaded('gd')) {
        // 如果GD可用，使用原始方法
        include 'captcha.php';
        
    } else {
        // GD不可用，使用SVG备用方案
        header('Content-Type: image/svg+xml');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        echo createSVGCaptcha($captchaCode);
    }
    
} catch (Exception $e) {
    // 错误时返回错误信息
    if (($_GET['format'] ?? 'image') === 'text') {
        header('Content-Type: text/plain; charset=utf-8');
        echo "验证码生成失败: " . $e->getMessage();
    } else {
        // 返回错误SVG
        header('Content-Type: image/svg+xml');
        echo '<?xml version="1.0" encoding="UTF-8"?>';
        echo '<svg width="120" height="40" xmlns="http://www.w3.org/2000/svg">';
        echo '<rect width="100%" height="100%" fill="#ffeeee" stroke="#ff0000"/>';
        echo '<text x="10" y="25" font-family="Arial" font-size="12" fill="#ff0000">ERROR</text>';
        echo '</svg>';
    }
}
?>
