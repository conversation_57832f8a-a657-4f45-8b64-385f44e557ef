<?php
/**
 * PHP 会话功能测试
 * 用于诊断和修复会话相关问题
 */

// 关闭错误显示，避免污染输出
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 设置内容类型
header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP 会话测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>🔧 PHP 会话功能测试</h1>
    <p><strong>测试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="test-section">
        <h2>📊 会话配置检查</h2>
        <table border="1" cellpadding="5" cellspacing="0">
            <tr>
                <th>配置项</th>
                <th>当前值</th>
                <th>状态</th>
            </tr>
            <tr>
                <td><strong>session.save_path</strong></td>
                <td><?php echo ini_get('session.save_path') ?: '(未设置)'; ?></td>
                <td>
                    <?php 
                    $save_path = ini_get('session.save_path');
                    if ($save_path && is_dir($save_path) && is_writable($save_path)) {
                        echo '<span class="success">✅ 正常</span>';
                    } else {
                        echo '<span class="error">❌ 问题</span>';
                    }
                    ?>
                </td>
            </tr>
            <tr>
                <td><strong>session.save_handler</strong></td>
                <td><?php echo ini_get('session.save_handler'); ?></td>
                <td><span class="info">ℹ️ 信息</span></td>
            </tr>
            <tr>
                <td><strong>session.auto_start</strong></td>
                <td><?php echo ini_get('session.auto_start') ? 'On' : 'Off'; ?></td>
                <td><span class="info">ℹ️ 信息</span></td>
            </tr>
            <tr>
                <td><strong>session.gc_maxlifetime</strong></td>
                <td><?php echo ini_get('session.gc_maxlifetime'); ?> 秒</td>
                <td><span class="info">ℹ️ 信息</span></td>
            </tr>
        </table>
    </div>
    
    <div class="test-section">
        <h2>📁 目录权限检查</h2>
        <?php
        $save_path = ini_get('session.save_path');
        if ($save_path) {
            echo "<p><strong>会话目录:</strong> $save_path</p>";
            
            if (is_dir($save_path)) {
                echo "<p class='success'>✅ 目录存在</p>";
                
                if (is_readable($save_path)) {
                    echo "<p class='success'>✅ 目录可读</p>";
                } else {
                    echo "<p class='error'>❌ 目录不可读</p>";
                }
                
                if (is_writable($save_path)) {
                    echo "<p class='success'>✅ 目录可写</p>";
                } else {
                    echo "<p class='error'>❌ 目录不可写</p>";
                }
                
                // 尝试创建测试文件
                $test_file = $save_path . '/test_' . time() . '.txt';
                if (@file_put_contents($test_file, 'test')) {
                    echo "<p class='success'>✅ 可以创建文件</p>";
                    @unlink($test_file); // 清理测试文件
                } else {
                    echo "<p class='error'>❌ 无法创建文件</p>";
                }
                
            } else {
                echo "<p class='error'>❌ 目录不存在</p>";
            }
        } else {
            echo "<p class='error'>❌ 会话路径未配置</p>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>🧪 会话功能测试</h2>
        <?php
        try {
            // 尝试启动会话
            if (session_status() === PHP_SESSION_NONE) {
                $session_result = @session_start();
                if ($session_result) {
                    echo "<p class='success'>✅ 会话启动成功</p>";
                    echo "<p><strong>会话ID:</strong> " . session_id() . "</p>";
                    
                    // 测试会话变量
                    if (!isset($_SESSION['test_count'])) {
                        $_SESSION['test_count'] = 0;
                    }
                    $_SESSION['test_count']++;
                    $_SESSION['test_time'] = date('Y-m-d H:i:s');
                    
                    echo "<p><strong>测试计数:</strong> " . $_SESSION['test_count'] . "</p>";
                    echo "<p><strong>测试时间:</strong> " . $_SESSION['test_time'] . "</p>";
                    
                    // 检查会话文件
                    $session_file = ini_get('session.save_path') . '/sess_' . session_id();
                    if (file_exists($session_file)) {
                        echo "<p class='success'>✅ 会话文件已创建</p>";
                        echo "<p><strong>文件路径:</strong> $session_file</p>";
                        echo "<p><strong>文件大小:</strong> " . filesize($session_file) . " 字节</p>";
                    } else {
                        echo "<p class='warning'>⚠️ 会话文件未找到</p>";
                    }
                    
                } else {
                    echo "<p class='error'>❌ 会话启动失败</p>";
                }
            } else {
                echo "<p class='info'>ℹ️ 会话已经启动</p>";
                echo "<p><strong>会话ID:</strong> " . session_id() . "</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ 会话测试异常: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>🔍 错误信息检查</h2>
        <?php
        // 获取最后的错误
        $last_error = error_get_last();
        if ($last_error && strpos($last_error['message'], 'session') !== false) {
            echo "<p class='error'>❌ 检测到会话相关错误:</p>";
            echo "<pre>" . htmlspecialchars(print_r($last_error, true)) . "</pre>";
        } else {
            echo "<p class='success'>✅ 没有检测到会话相关错误</p>";
        }
        
        // 检查错误日志配置
        $error_log = ini_get('error_log');
        if ($error_log) {
            echo "<p><strong>错误日志文件:</strong> $error_log</p>";
            if (file_exists($error_log)) {
                echo "<p class='success'>✅ 错误日志文件存在</p>";
                echo "<p><strong>文件大小:</strong> " . filesize($error_log) . " 字节</p>";
                echo "<p><strong>最后修改:</strong> " . date('Y-m-d H:i:s', filemtime($error_log)) . "</p>";
            } else {
                echo "<p class='warning'>⚠️ 错误日志文件不存在</p>";
            }
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>🛠️ 修复建议</h2>
        <?php
        $issues = [];
        
        $save_path = ini_get('session.save_path');
        if (!$save_path) {
            $issues[] = "session.save_path 未配置";
        } elseif (!is_dir($save_path)) {
            $issues[] = "会话目录不存在: $save_path";
        } elseif (!is_writable($save_path)) {
            $issues[] = "会话目录不可写: $save_path";
        }
        
        if (session_status() === PHP_SESSION_DISABLED) {
            $issues[] = "会话功能被禁用";
        }
        
        if (empty($issues)) {
            echo "<p class='success'>✅ 会话配置正常！</p>";
        } else {
            echo "<p class='error'>❌ 发现以下问题:</p>";
            echo "<ul>";
            foreach ($issues as $issue) {
                echo "<li class='error'>$issue</li>";
            }
            echo "</ul>";
            
            echo "<h3>修复步骤:</h3>";
            echo "<ol>";
            echo "<li>运行 <code>fix-session-issue.bat</code> 脚本</li>";
            echo "<li>确保目录权限正确设置</li>";
            echo "<li>检查 php.ini 配置</li>";
            echo "<li>重启 IIS 服务 (<code>iisreset</code>)</li>";
            echo "<li>重新测试此页面</li>";
            echo "</ol>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关链接</h2>
        <ul>
            <li><a href="php-config-test.php">PHP 配置测试</a></li>
            <li><a href="../new-lock.html">登录页面测试</a></li>
            <li><a href="../api/forgot-password.php">密码找回 API</a> (需要POST请求)</li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?> | 🖥️ 服务器: <?php echo $_SERVER['SERVER_NAME'] ?? 'localhost'; ?></small></p>
</body>
</html>
