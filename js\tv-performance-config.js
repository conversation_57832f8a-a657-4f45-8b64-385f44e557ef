/**
 * Android TV性能配置文件
 * 根据设备性能自动调整倒计时参数
 */

class TVPerformanceConfig {
    constructor() {
        this.deviceInfo = this.detectDevice();
        this.performanceLevel = this.assessPerformance();
        this.config = this.generateConfig();
        
        console.log('🚀 Android TV性能配置:', this.config);
    }
    
    /**
     * 检测设备信息
     */
    detectDevice() {
        const userAgent = navigator.userAgent.toLowerCase();
        const isAndroidTV = userAgent.includes('android') && 
                           (userAgent.includes('tv') || userAgent.includes('smarttv'));
        
        return {
            isAndroidTV: isAndroidTV,
            userAgent: userAgent,
            screen: {
                width: screen.width,
                height: screen.height,
                pixelRatio: window.devicePixelRatio || 1
            },
            memory: navigator.deviceMemory || 'unknown',
            cores: navigator.hardwareConcurrency || 'unknown',
            connection: navigator.connection ? {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink
            } : null
        };
    }
    
    /**
     * 评估设备性能等级
     */
    assessPerformance() {
        let score = 0;
        
        // 基于内存评分
        if (this.deviceInfo.memory >= 4) score += 3;
        else if (this.deviceInfo.memory >= 2) score += 2;
        else if (this.deviceInfo.memory >= 1) score += 1;
        
        // 基于CPU核心数评分
        if (this.deviceInfo.cores >= 8) score += 3;
        else if (this.deviceInfo.cores >= 4) score += 2;
        else if (this.deviceInfo.cores >= 2) score += 1;
        
        // 基于屏幕分辨率评分
        const totalPixels = this.deviceInfo.screen.width * this.deviceInfo.screen.height;
        if (totalPixels >= 3840 * 2160) score += 1; // 4K
        else if (totalPixels >= 1920 * 1080) score += 2; // 1080p
        else score += 3; // 720p或更低
        
        // 性能等级分类
        if (score >= 7) return 'high';
        else if (score >= 4) return 'medium';
        else return 'low';
    }
    
    /**
     * 生成性能配置
     */
    generateConfig() {
        const configs = {
            high: {
                updateInterval: 33,      // 30fps
                enableAnimations: true,
                enableGlow: true,
                enableBackgroundAnimation: false, // 即使高性能也关闭背景动画
                cacheSize: 1000,
                batchUpdates: true,
                useRequestAnimationFrame: true,
                enableVisibilityAPI: true,
                description: '高性能设备配置'
            },
            medium: {
                updateInterval: 50,      // 20fps
                enableAnimations: true,
                enableGlow: false,
                enableBackgroundAnimation: false,
                cacheSize: 500,
                batchUpdates: true,
                useRequestAnimationFrame: true,
                enableVisibilityAPI: true,
                description: '中等性能设备配置'
            },
            low: {
                updateInterval: 100,     // 10fps
                enableAnimations: false,
                enableGlow: false,
                enableBackgroundAnimation: false,
                cacheSize: 100,
                batchUpdates: true,
                useRequestAnimationFrame: true,
                enableVisibilityAPI: true,
                description: '低性能设备配置'
            }
        };
        
        return {
            ...configs[this.performanceLevel],
            performanceLevel: this.performanceLevel,
            deviceInfo: this.deviceInfo
        };
    }
    
    /**
     * 应用性能配置到CSS
     */
    applyCSSOptimizations() {
        const style = document.createElement('style');
        let css = '';
        
        if (!this.config.enableAnimations) {
            css += `
                *, *::before, *::after {
                    animation-duration: 0s !important;
                    animation-delay: 0s !important;
                    transition-duration: 0s !important;
                    transition-delay: 0s !important;
                }
            `;
        }
        
        if (!this.config.enableGlow) {
            css += `
                .countdown-number {
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7) !important;
                }
            `;
        }
        
        if (!this.config.enableBackgroundAnimation) {
            css += `
                body::before {
                    animation: none !important;
                }
                .warning-title::before {
                    animation: none !important;
                }
            `;
        }
        
        // 低性能设备额外优化
        if (this.performanceLevel === 'low') {
            css += `
                * {
                    will-change: auto !important;
                    transform: none !important;
                }
                .countdown-item {
                    background: rgba(0, 0, 0, 0.5) !important;
                    border: 1px solid rgba(255, 255, 255, 0.3) !important;
                }
            `;
        }
        
        style.textContent = css;
        document.head.appendChild(style);
    }
    
    /**
     * 获取优化建议
     */
    getOptimizationTips() {
        const tips = {
            high: [
                '✅ 设备性能良好，可以享受完整的视觉效果',
                '💡 建议开启硬件加速以获得最佳体验'
            ],
            medium: [
                '⚠️ 已自动关闭部分动画效果以提升性能',
                '💡 如果仍有卡顿，可以关闭浏览器的其他标签页',
                '💡 确保设备有足够的可用内存'
            ],
            low: [
                '🔧 已启用最大性能优化模式',
                '💡 建议关闭其他应用程序释放内存',
                '💡 考虑降低屏幕分辨率以提升性能',
                '💡 确保设备散热良好，避免过热降频'
            ]
        };
        
        return tips[this.performanceLevel] || [];
    }
    
    /**
     * 性能监控
     */
    startPerformanceMonitoring() {
        if (!window.performance || !window.performance.memory) {
            console.warn('⚠️ 浏览器不支持性能监控API');
            return;
        }
        
        let frameCount = 0;
        let lastTime = performance.now();
        let lastMemoryCheck = lastTime;
        
        const monitor = () => {
            const currentTime = performance.now();
            frameCount++;
            
            // 每5秒检查一次性能
            if (currentTime - lastMemoryCheck >= 5000) {
                const memory = performance.memory;
                const fps = Math.round(frameCount * 1000 / (currentTime - lastTime));
                
                const memoryUsage = {
                    used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
                    total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
                    limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
                };
                
                console.log(`📊 性能监控 - FPS: ${fps}, 内存: ${memoryUsage.used}MB/${memoryUsage.total}MB`);
                
                // 内存使用率过高时的警告
                if (memoryUsage.used / memoryUsage.limit > 0.8) {
                    console.warn('⚠️ 内存使用率过高，可能影响性能');
                }
                
                // FPS过低时的警告
                if (fps < 15) {
                    console.warn('⚠️ FPS过低，建议降低更新频率');
                }
                
                frameCount = 0;
                lastTime = currentTime;
                lastMemoryCheck = currentTime;
            }
            
            requestAnimationFrame(monitor);
        };
        
        requestAnimationFrame(monitor);
    }
    
    /**
     * 导出配置信息
     */
    exportConfig() {
        return {
            timestamp: new Date().toISOString(),
            deviceInfo: this.deviceInfo,
            performanceLevel: this.performanceLevel,
            config: this.config,
            optimizationTips: this.getOptimizationTips()
        };
    }
}

// 全局配置实例
window.TVPerformanceConfig = TVPerformanceConfig;

// 自动初始化（如果在Android TV环境中）
if (typeof window !== 'undefined') {
    window.addEventListener('DOMContentLoaded', () => {
        const tvConfig = new TVPerformanceConfig();
        
        // 应用CSS优化
        tvConfig.applyCSSOptimizations();
        
        // 显示优化提示
        const tips = tvConfig.getOptimizationTips();
        if (tips.length > 0) {
            console.group('🚀 Android TV优化提示');
            tips.forEach(tip => console.log(tip));
            console.groupEnd();
        }
        
        // 开始性能监控（仅在开发模式）
        if (window.location.hostname === 'localhost' || window.location.hostname.includes('192.168')) {
            tvConfig.startPerformanceMonitoring();
        }
        
        // 将配置暴露到全局
        window.tvPerformanceConfig = tvConfig;
    });
}
