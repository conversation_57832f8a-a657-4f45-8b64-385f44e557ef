<?php
/**
 * 检查测试用户是否存在
 */

$dbPath = '../server/user_system.db3';

try {
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>检查用户数据库</h2>";
    
    // 检查用户表是否存在
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='users'");
    if ($stmt->fetch()) {
        echo "<p>✅ users表存在</p>";
        
        // 查询所有用户
        $stmt = $pdo->query("SELECT id, username, email, status, created_at FROM users");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>用户列表：</h3>";
        if (empty($users)) {
            echo "<p>❌ 没有找到任何用户</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>用户名</th><th>邮箱</th><th>状态</th><th>创建时间</th></tr>";
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>{$user['id']}</td>";
                echo "<td>{$user['username']}</td>";
                echo "<td>{$user['email']}</td>";
                echo "<td>{$user['status']}</td>";
                echo "<td>{$user['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // 特别检查测试用户
        $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? AND email = ?");
        $stmt->execute(['ataehee1', '<EMAIL>']);
        $testUser = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<h3>测试用户检查：</h3>";
        if ($testUser) {
            echo "<p>✅ 测试用户 ataehee1 存在</p>";
            echo "<pre>" . print_r($testUser, true) . "</pre>";
        } else {
            echo "<p>❌ 测试用户 ataehee1 不存在</p>";
            echo "<p>需要先注册用户：ataehee1 / <EMAIL></p>";
        }
        
    } else {
        echo "<p>❌ users表不存在</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ 数据库错误: " . $e->getMessage() . "</p>";
}
?>
