<?php
/**
 * PHP错误日志测试
 * 验证各种类型的错误是否正确记录到日志文件
 */

// 关闭错误显示，确保错误只记录到日志
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP错误日志测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
        button { padding: 8px 16px; margin: 5px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
    </style>
</head>
<body>
    <h1>🔧 PHP错误日志测试</h1>
    <p><strong>测试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="test-section">
        <h2>📊 当前错误配置</h2>
        <table border="1" cellpadding="5" cellspacing="0">
            <tr>
                <th>配置项</th>
                <th>当前值</th>
                <th>说明</th>
            </tr>
            <tr>
                <td><strong>error_reporting</strong></td>
                <td><?php echo error_reporting(); ?></td>
                <td>
                    <?php 
                    $level = error_reporting();
                    if ($level & E_WARNING) {
                        echo '<span class="success">✅ 包含Warning</span>';
                    } else {
                        echo '<span class="error">❌ 不包含Warning</span>';
                    }
                    ?>
                </td>
            </tr>
            <tr>
                <td><strong>log_errors</strong></td>
                <td><?php echo ini_get('log_errors') ? 'On' : 'Off'; ?></td>
                <td>
                    <?php echo ini_get('log_errors') ? '<span class="success">✅ 已启用</span>' : '<span class="error">❌ 未启用</span>'; ?>
                </td>
            </tr>
            <tr>
                <td><strong>error_log</strong></td>
                <td><?php echo ini_get('error_log') ?: '(未设置)'; ?></td>
                <td>
                    <?php 
                    $log_file = ini_get('error_log');
                    if ($log_file && file_exists($log_file)) {
                        echo '<span class="success">✅ 文件存在</span>';
                    } elseif ($log_file) {
                        echo '<span class="warning">⚠️ 文件不存在</span>';
                    } else {
                        echo '<span class="error">❌ 未配置</span>';
                    }
                    ?>
                </td>
            </tr>
            <tr>
                <td><strong>display_errors</strong></td>
                <td><?php echo ini_get('display_errors') ? 'On' : 'Off'; ?></td>
                <td>
                    <?php echo ini_get('display_errors') ? '<span class="error">❌ 应该关闭</span>' : '<span class="success">✅ 已关闭</span>'; ?>
                </td>
            </tr>
        </table>
    </div>
    
    <div class="test-section">
        <h2>🧪 错误类型测试</h2>
        <p>点击按钮测试不同类型的错误是否记录到日志：</p>
        
        <?php
        if (isset($_GET['test'])) {
            $test_type = $_GET['test'];
            echo "<h3>测试结果：</h3>";
            
            switch ($test_type) {
                case 'warning':
                    echo "<p>触发PHP Warning...</p>";
                    // 故意访问未定义变量
                    $undefined_result = $undefined_variable;
                    echo "<p class='success'>Warning测试完成</p>";
                    break;
                    
                case 'notice':
                    echo "<p>触发PHP Notice...</p>";
                    // 故意使用未定义的数组索引
                    $array = ['a' => 1];
                    $notice_result = $array['undefined_key'];
                    echo "<p class='success'>Notice测试完成</p>";
                    break;
                    
                case 'error_log':
                    echo "<p>使用error_log()函数...</p>";
                    error_log("手动错误日志测试 - " . date('Y-m-d H:i:s'));
                    echo "<p class='success'>error_log()测试完成</p>";
                    break;
                    
                case 'session_warning':
                    echo "<p>触发会话相关Warning...</p>";
                    // 模拟会话问题
                    if (session_status() === PHP_SESSION_NONE) {
                        // 设置一个不存在的会话路径来触发warning
                        ini_set('session.save_path', '/nonexistent/path');
                        @session_start();
                    }
                    echo "<p class='success'>会话Warning测试完成</p>";
                    break;
            }
        }
        ?>
        
        <div>
            <a href="?test=warning"><button>测试 PHP Warning</button></a>
            <a href="?test=notice"><button>测试 PHP Notice</button></a>
            <a href="?test=error_log"><button>测试 error_log()</button></a>
            <a href="?test=session_warning"><button>测试会话Warning</button></a>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📋 错误日志内容</h2>
        <?php
        $log_file = ini_get('error_log');
        if ($log_file && file_exists($log_file)) {
            echo "<p><strong>日志文件:</strong> $log_file</p>";
            echo "<p><strong>文件大小:</strong> " . filesize($log_file) . " 字节</p>";
            echo "<p><strong>最后修改:</strong> " . date('Y-m-d H:i:s', filemtime($log_file)) . "</p>";
            
            // 读取最后20行
            $lines = file($log_file);
            if ($lines) {
                $last_lines = array_slice($lines, -20);
                echo "<h3>最后20行日志:</h3>";
                echo "<pre>";
                foreach ($last_lines as $line) {
                    echo htmlspecialchars($line);
                }
                echo "</pre>";
            } else {
                echo "<p class='warning'>无法读取日志文件内容</p>";
            }
        } else {
            echo "<p class='error'>错误日志文件不存在或无法访问</p>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>🔍 错误级别说明</h2>
        <table border="1" cellpadding="5" cellspacing="0">
            <tr>
                <th>错误类型</th>
                <th>常量值</th>
                <th>是否记录</th>
                <th>说明</th>
            </tr>
            <?php
            $error_types = [
                'E_ERROR' => [E_ERROR, '致命错误'],
                'E_WARNING' => [E_WARNING, 'PHP警告'],
                'E_PARSE' => [E_PARSE, '解析错误'],
                'E_NOTICE' => [E_NOTICE, 'PHP通知'],
                'E_CORE_ERROR' => [E_CORE_ERROR, '核心错误'],
                'E_CORE_WARNING' => [E_CORE_WARNING, '核心警告'],
                'E_USER_ERROR' => [E_USER_ERROR, '用户错误'],
                'E_USER_WARNING' => [E_USER_WARNING, '用户警告'],
                'E_USER_NOTICE' => [E_USER_NOTICE, '用户通知'],
                'E_STRICT' => [E_STRICT, '严格模式'],
                'E_DEPRECATED' => [E_DEPRECATED, '废弃警告']
            ];
            
            $current_level = error_reporting();
            
            foreach ($error_types as $name => $info) {
                $value = $info[0];
                $description = $info[1];
                $is_logged = ($current_level & $value) ? true : false;
                
                echo "<tr>";
                echo "<td><strong>$name</strong></td>";
                echo "<td>$value</td>";
                echo "<td>" . ($is_logged ? '<span class="success">✅ 是</span>' : '<span class="warning">❌ 否</span>') . "</td>";
                echo "<td>$description</td>";
                echo "</tr>";
            }
            ?>
        </table>
    </div>
    
    <div class="test-section">
        <h2>🎯 配置建议</h2>
        <?php
        $issues = [];
        
        if (!ini_get('log_errors')) {
            $issues[] = "log_errors 未启用";
        }
        
        if (ini_get('display_errors')) {
            $issues[] = "display_errors 仍然开启，应该关闭";
        }
        
        $log_file = ini_get('error_log');
        if (!$log_file) {
            $issues[] = "error_log 路径未配置";
        } elseif (!file_exists($log_file)) {
            $issues[] = "错误日志文件不存在: $log_file";
        }
        
        $current_level = error_reporting();
        if (!($current_level & E_WARNING)) {
            $issues[] = "error_reporting 不包含 E_WARNING";
        }
        
        if (empty($issues)) {
            echo "<p class='success'>✅ 错误日志配置正确！</p>";
            echo "<p>当前配置可以记录包括Warning在内的所有重要错误到日志文件。</p>";
        } else {
            echo "<p class='error'>❌ 发现以下配置问题:</p>";
            echo "<ul>";
            foreach ($issues as $issue) {
                echo "<li class='error'>$issue</li>";
            }
            echo "</ul>";
        }
        ?>
        
        <h3>推荐配置 (生产环境):</h3>
        <pre>
; 记录所有错误包括Warning，但排除废弃警告
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT

; 启用错误日志
log_errors = On

; 关闭错误显示
display_errors = Off
display_startup_errors = Off

; 指定错误日志文件
error_log = "D:/error_log/php/php_errors.log"
        </pre>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关链接</h2>
        <ul>
            <li><a href="php-config-test.php">PHP配置测试</a></li>
            <li><a href="session-test.php">会话功能测试</a></li>
            <li><a href="forgot-password-debug.php">密码找回调试</a></li>
            <li><a href="../new-lock.html">返回登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
