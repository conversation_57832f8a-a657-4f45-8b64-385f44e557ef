<?php
/**
 * 调试激活码生成问题
 * 检查服务器和本地环境的差异
 */

header('Content-Type: text/html; charset=utf-8');

$dbPath = '../server/user_system.db3';

function debugActivationCodesIssue() {
    global $dbPath;
    
    echo "<h1>🔍 激活码生成问题调试</h1>";
    echo "<p><strong>检查时间:</strong> " . date('Y-m-d H:i:s') . "</p>";
    
    // 检查数据库是否存在
    if (!file_exists($dbPath)) {
        echo "<p style='color: red;'>❌ 数据库文件不存在: $dbPath</p>";
        return;
    }
    
    echo "<p style='color: green;'>✅ 数据库文件存在: $dbPath</p>";
    echo "<p><strong>数据库大小:</strong> " . round(filesize($dbPath) / 1024 / 1024, 2) . " MB</p>";
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 1. 检查activation_codes表结构
        echo "<h2>📋 activation_codes表结构</h2>";
        $stmt = $pdo->query("PRAGMA table_info(activation_codes)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>字段名</th><th>类型</th><th>非空</th><th>默认值</th><th>主键</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>{$col['name']}</td>";
            echo "<td>{$col['type']}</td>";
            echo "<td>" . ($col['notnull'] ? '是' : '否') . "</td>";
            echo "<td>" . ($col['dflt_value'] ?? 'NULL') . "</td>";
            echo "<td>" . ($col['pk'] ? '是' : '否') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 2. 检查最近生成的激活码
        echo "<h2>📊 最近生成的激活码 (最新10条)</h2>";
        $stmt = $pdo->query("
            SELECT id, code, created_at, expires_at, is_used, used_at, bound_user_id, status 
            FROM activation_codes 
            ORDER BY created_at DESC 
            LIMIT 10
        ");
        $codes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($codes) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
            echo "<tr>";
            echo "<th>ID</th><th>激活码</th><th>创建时间</th><th>过期时间</th>";
            echo "<th>is_used</th><th>使用时间</th><th>绑定用户ID</th><th>状态</th>";
            echo "</tr>";
            
            foreach ($codes as $code) {
                $isUsedDisplay = $code['is_used'] ? '1 (已使用)' : '0 (未使用)';
                $usedAtDisplay = $code['used_at'] ?? '未使用';
                $boundUserDisplay = $code['bound_user_id'] ?? '未绑定';
                
                echo "<tr>";
                echo "<td>{$code['id']}</td>";
                echo "<td style='font-family: monospace;'>{$code['code']}</td>";
                echo "<td>{$code['created_at']}</td>";
                echo "<td>{$code['expires_at']}</td>";
                echo "<td style='color: " . ($code['is_used'] ? 'red' : 'green') . ";'>{$isUsedDisplay}</td>";
                echo "<td>{$usedAtDisplay}</td>";
                echo "<td>{$boundUserDisplay}</td>";
                echo "<td>{$code['status']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>📭 暂无激活码记录</p>";
        }
        
        // 3. 统计信息
        echo "<h2>📈 激活码统计</h2>";
        
        $stats = [];
        
        // 总数
        $stmt = $pdo->query("SELECT COUNT(*) FROM activation_codes");
        $stats['total'] = $stmt->fetchColumn();
        
        // 已使用数量
        $stmt = $pdo->query("SELECT COUNT(*) FROM activation_codes WHERE is_used = 1");
        $stats['used'] = $stmt->fetchColumn();
        
        // 未使用数量
        $stmt = $pdo->query("SELECT COUNT(*) FROM activation_codes WHERE is_used = 0");
        $stats['unused'] = $stmt->fetchColumn();
        
        // 按状态统计
        $stmt = $pdo->query("SELECT status, COUNT(*) as count FROM activation_codes GROUP BY status");
        $statusStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>统计项</th><th>数量</th></tr>";
        echo "<tr><td>总激活码数</td><td>{$stats['total']}</td></tr>";
        echo "<tr><td>已使用 (is_used=1)</td><td style='color: red;'>{$stats['used']}</td></tr>";
        echo "<tr><td>未使用 (is_used=0)</td><td style='color: green;'>{$stats['unused']}</td></tr>";
        echo "</table>";
        
        echo "<h3>按状态统计:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>状态</th><th>数量</th></tr>";
        foreach ($statusStats as $stat) {
            echo "<tr><td>{$stat['status']}</td><td>{$stat['count']}</td></tr>";
        }
        echo "</table>";
        
        // 4. 检查是否有触发器或其他机制
        echo "<h2>🔧 数据库触发器检查</h2>";
        $stmt = $pdo->query("SELECT name, sql FROM sqlite_master WHERE type='trigger'");
        $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($triggers) {
            echo "<p style='color: orange;'>⚠️ 发现数据库触发器:</p>";
            foreach ($triggers as $trigger) {
                echo "<h4>触发器: {$trigger['name']}</h4>";
                echo "<pre style='background: #f5f5f5; padding: 10px; overflow-x: auto;'>";
                echo htmlspecialchars($trigger['sql']);
                echo "</pre>";
            }
        } else {
            echo "<p style='color: green;'>✅ 没有发现数据库触发器</p>";
        }
        
        // 5. 检查最近的插入操作
        echo "<h2>🕒 最近的数据库操作时间</h2>";
        
        // 检查最新记录的创建时间
        $stmt = $pdo->query("SELECT MAX(created_at) as latest_created FROM activation_codes");
        $latestCreated = $stmt->fetchColumn();
        
        if ($latestCreated) {
            echo "<p><strong>最新激活码创建时间:</strong> $latestCreated</p>";
            
            // 计算时间差
            $now = new DateTime();
            $created = new DateTime($latestCreated);
            $diff = $now->diff($created);
            
            echo "<p><strong>距离现在:</strong> ";
            if ($diff->days > 0) echo "{$diff->days}天 ";
            if ($diff->h > 0) echo "{$diff->h}小时 ";
            if ($diff->i > 0) echo "{$diff->i}分钟 ";
            echo "{$diff->s}秒前</p>";
        }
        
        // 6. 模拟生成激活码测试
        echo "<h2>🧪 模拟生成激活码测试</h2>";
        
        // 生成测试激活码（不保存到数据库）
        $charset = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
        $segments = [];
        for ($i = 0; $i < 4; $i++) {
            $segment = '';
            for ($j = 0; $j < 5; $j++) {
                $segment .= $charset[random_int(0, strlen($charset) - 1)];
            }
            $segments[] = $segment;
        }
        $testCode = implode('-', $segments);
        
        echo "<p><strong>测试生成的激活码:</strong> <code>$testCode</code></p>";
        
        // 检查这个测试码是否已存在
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM activation_codes WHERE code = ?");
        $stmt->execute([$testCode]);
        $exists = $stmt->fetchColumn() > 0;
        
        echo "<p><strong>测试码是否已存在:</strong> " . ($exists ? '是' : '否') . "</p>";
        
        // 7. 检查Python脚本可能的问题
        echo "<h2>🐍 Python脚本问题分析</h2>";
        
        echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #0066cc;'>";
        echo "<h3>可能的原因分析:</h3>";
        echo "<ol>";
        echo "<li><strong>数据库路径问题:</strong> 服务器和本地环境使用了不同的数据库文件</li>";
        echo "<li><strong>数据库权限问题:</strong> 服务器环境中Python脚本没有写权限</li>";
        echo "<li><strong>数据库锁定:</strong> 数据库被其他进程锁定，导致写入异常</li>";
        echo "<li><strong>触发器影响:</strong> 数据库中有触发器自动修改is_used字段</li>";
        echo "<li><strong>时区问题:</strong> 服务器和本地时区不同，影响时间判断</li>";
        echo "<li><strong>并发问题:</strong> 多个进程同时操作数据库</li>";
        echo "</ol>";
        echo "</div>";
        
        // 8. 建议的解决方案
        echo "<h2>💡 建议的解决方案</h2>";
        
        echo "<div style='background: #f0fff0; padding: 15px; border-left: 4px solid #00cc66;'>";
        echo "<h3>解决步骤:</h3>";
        echo "<ol>";
        echo "<li><strong>检查数据库路径:</strong> 确认Python脚本使用的数据库路径正确</li>";
        echo "<li><strong>检查文件权限:</strong> 确保Python脚本有数据库文件的读写权限</li>";
        echo "<li><strong>添加调试输出:</strong> 在Python脚本中添加更多调试信息</li>";
        echo "<li><strong>检查事务处理:</strong> 确保数据库事务正确提交</li>";
        echo "<li><strong>测试直接SQL:</strong> 直接在数据库中执行INSERT语句测试</li>";
        echo "</ol>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 数据库连接失败: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

debugActivationCodesIssue();
?>

<hr>
<p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
