<?php
/**
 * 用户登录API
 * 支持单设备登录，新登录会挤掉旧登录
 */

session_start();
header('Content-Type: application/json; charset=utf-8');

// 引入安全模块
require_once 'csrf-protection.php';
require_once 'rate-limiter.php';

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit();
}

// 数据库配置
$dbPath = '../server/user_system.db3';

/**
 * 验证验证码
 */
function verifyCaptcha($inputCode, $sessionId) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $codeHash = hash('sha256', strtoupper($inputCode));
        
        $stmt = $pdo->prepare("
            SELECT id FROM captcha_codes 
            WHERE code_hash = ? AND session_id = ? 
            AND expires_at > datetime('now') AND is_used = 0
        ");
        $stmt->execute([$codeHash, $sessionId]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            // 标记验证码为已使用
            $stmt = $pdo->prepare("UPDATE captcha_codes SET is_used = 1 WHERE id = ?");
            $stmt->execute([$result['id']]);
            return true;
        }
        
        return false;
    } catch (Exception $e) {
        error_log("验证码验证失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 验证用户登录 (支持新旧密码哈希算法)
 */
function verifyUser($username, $password) {
    global $dbPath;

    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // 首先获取用户的密码哈希
        $stmt = $pdo->prepare("
            SELECT id, username, password_hash, status, last_login, login_count
            FROM users
            WHERE username = ? AND status = 'active'
        ");
        $stmt->execute([$username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            return false;
        }

        // 检查是否是新的password_hash格式
        if (password_verify($password, $user['password_hash'])) {
            // 新格式验证成功
            return $user;
        }

        // 检查是否是旧的SHA-256格式
        $oldPasswordHash = hash('sha256', $password);
        if (hash_equals($user['password_hash'], $oldPasswordHash)) {
            // 旧格式验证成功，升级到新格式
            $newPasswordHash = password_hash($password, PASSWORD_ARGON2ID);

            $updateStmt = $pdo->prepare("
                UPDATE users
                SET password_hash = ?
                WHERE id = ?
            ");
            $updateStmt->execute([$newPasswordHash, $user['id']]);

            error_log("用户 {$username} 的密码哈希已升级到新格式");
            return $user;
        }

        return false;
    } catch (Exception $e) {
        error_log("用户验证失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 生成会话令牌
 */
function generateSessionToken() {
    return bin2hex(random_bytes(32));
}

/**
 * 创建用户会话 (挤掉旧会话)
 */
function createUserSession($userId, $deviceInfo, $userAgent, $ipAddress) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 开始事务
        $pdo->beginTransaction();
        
        // 1. 将该用户的所有旧会话设为非活跃
        $stmt = $pdo->prepare("UPDATE user_sessions SET is_active = 0 WHERE user_id = ?");
        $stmt->execute([$userId]);
        
        // 2. 创建新会话
        $sessionToken = generateSessionToken();
        $expiresAt = date('Y-m-d H:i:s', time() + 7776000); // 90天过期 (90 * 24 * 60 * 60 = 7776000秒) - 适合商家广告长期展示
        
        $stmt = $pdo->prepare("
            INSERT INTO user_sessions (
                user_id, session_token, device_info, ip_address, 
                user_agent, expires_at, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, 1)
        ");
        
        $stmt->execute([
            $userId, $sessionToken, $deviceInfo, 
            $ipAddress, $userAgent, $expiresAt
        ]);
        
        // 3. 更新用户登录信息
        $stmt = $pdo->prepare("
            UPDATE users 
            SET last_login = datetime('now'), login_count = login_count + 1
            WHERE id = ?
        ");
        $stmt->execute([$userId]);
        
        // 提交事务
        $pdo->commit();
        
        return $sessionToken;
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("创建会话失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 记录登录日志
 */
function logLogin($userId, $username, $success, $reason = null) {
    global $dbPath;

    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $stmt = $pdo->prepare("
            INSERT INTO login_logs (user_id, username, ip_address, user_agent, status, failure_reason)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $userId,
            $username,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            $success ? 'success' : 'failed',
            $reason
        ]);

        // 在成功登录时触发自动清理检查
        if ($success) {
            require_once '../server/auto-cleanup-helper.php';
            autoCleanupOnLogin($dbPath);

            // 同时执行session清理
            require_once '../server/session-auto-cleaner.php';
            loginSessionCleanup($dbPath, $userId);
        }

    } catch (Exception $e) {
        error_log("记录登录日志失败: " . $e->getMessage());
    }
}

try {
    // 获取请求数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('无效的JSON数据');
    }
    
    // 验证必需字段
    $requiredFields = ['username', 'password'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            throw new Exception("缺少必需字段: $field");
        }
    }
    
    $username = trim($data['username']);
    $password = trim($data['password']);
    $deviceInfo = $data['device_info'] ?? 'Unknown Device';

    // 安全检查
    // 1. 验证CSRF令牌 (暂时禁用用于测试)
    // if (!isset($data['csrf_token'])) {
    //     throw new Exception('缺少CSRF令牌');
    // }
    // if (!verifyCSRFToken($data['csrf_token'])) {
    //     throw new Exception('CSRF令牌验证失败');
    // }

    // 2. 检查速率限制
    checkLoginRateLimit($username);
    
    // 验证用户登录
    $user = verifyUser($username, $password);
    if (!$user) {
        logLogin(null, $username, false, '用户名或密码错误');
        throw new Exception('用户名或密码错误');
    }
    
    // 创建会话 (挤掉旧会话)
    $sessionToken = createUserSession(
        $user['id'],
        $deviceInfo,
        $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    );
    
    if (!$sessionToken) {
        throw new Exception('创建会话失败');
    }
    
    // 记录成功日志
    logLogin($user['id'], $username, true);

    // 重置速率限制计数器
    recordSuccessfulAction('login_ip', getClientIdentifier());
    recordSuccessfulAction('login_user', $username);

    // 设置会话cookie (增强安全性) - 90天过期，适合商家广告长期展示
    setcookie('session_token', $sessionToken, [
        'expires' => time() + 7776000, // 90天过期 (90 * 24 * 60 * 60 = 7776000秒)
        'path' => '/',
        'domain' => '',
        'secure' => isset($_SERVER['HTTPS']),
        'httponly' => true,
        'samesite' => 'Strict'
    ]);
    
    echo json_encode([
        'success' => true,
        'message' => '登录成功！',
        'data' => [
            'user_id' => $user['id'],
            'username' => $user['username'],
            'session_token' => $sessionToken,
            'expires_at' => time() + 7776000 // 90天过期
        ]
    ]);
    
} catch (Exception $e) {
    // 记录失败日志
    if (isset($username)) {
        logLogin(null, $username, false, $e->getMessage());
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
