<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选择汽车品牌 - 湖南省省补倒计时</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header {
            margin-bottom: 40px;
        }

        .title {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 20px;
        }

        .countdown-info {
            background: linear-gradient(135deg, #ff4757, #ff3838);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            display: inline-block;
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .brands-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .brand-card {
            background: white;
            border-radius: 15px;
            padding: 30px 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .brand-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .brand-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .brand-card:hover::before {
            left: 100%;
        }

        .brand-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: #f8f9fa;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: #667eea;
        }

        .brand-name {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .brand-english {
            font-size: 1rem;
            color: #888;
            margin-bottom: 15px;
        }

        .brand-description {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .brand-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .brand-button:hover {
            background: linear-gradient(135deg, #764ba2, #667eea);
            transform: scale(1.05);
        }

        .welcome-info {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            display: inline-block;
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .skip-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }

        .skip-button {
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 10px;
        }

        .skip-button:hover {
            background: #5a6268;
            transform: scale(1.05);
        }

        .footer {
            margin-top: 30px;
            color: #666;
            font-size: 0.9rem;
        }

        .features {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .feature {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #333;
            font-size: 1rem;
        }

        .feature-icon {
            font-size: 1.5rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }

            .title {
                font-size: 2rem;
            }

            .brands-grid {
                grid-template-columns: 1fr;
            }

            .features {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title" id="pageTitle">🚗 选择您的汽车品牌</h1>
            <div class="welcome-info" id="welcomeInfo" style="display: none;">
                欢迎回来！请选择您的汽车品牌
            </div>
            <p class="subtitle">选择品牌后查看对应品牌logo的省补倒计时</p>
            <div class="countdown-info">
                ⏰ 湖南省省补7.31 24:00截止 - 8月起可显示异地省补或国补
            </div>
        </div>

        <div class="features">
            <div class="feature">
                <span class="feature-icon">🏷️</span>
                <span>专属品牌logo</span>
            </div>
            <div class="feature">
                <span class="feature-icon">🚗</span>
                <span>品牌汽车图片</span>
            </div>
            <div class="feature">
                <span class="feature-icon">📱</span>
                <span>TV版本支持</span>
            </div>
            <div class="feature">
                <span class="feature-icon">🔄</span>
                <span>实时倒计时</span>
            </div>
        </div>

        <div class="brands-grid" id="brandsGrid">
            <!-- 品牌卡片将通过JavaScript动态生成 -->
        </div>

        <div class="skip-section" id="skipSection" style="display: none;">
            <p style="margin-bottom: 15px;">💡 也可以使用默认品牌（五菱汽车）直接进入</p>
            <button class="skip-button" onclick="selectBrand('wuling')">使用默认品牌进入</button>
        </div>

        <div class="footer">
            <p>💡 提示：不同品牌只有logo和汽车图片不同，倒计时内容完全一致</p>
            <p>🔗 选择品牌后将显示对应的品牌logo和汽车图片</p>
        </div>
    </div>

    <script>
        // 品牌配置数据
        const brands = [
            {
                path: 'wuling',
                name: '五菱汽车',
                english: 'Wuling',
                description: '人民需要什么，五菱就造什么',
                icon: '🚐',
                color: '#ff4757'
            },
            {
                path: 'byd',
                name: '比亚迪',
                english: 'BYD',
                description: '技术为王，创新为本',
                icon: '🔋',
                color: '#1e90ff'
            },
            {
                path: 'geely',
                name: '吉利汽车',
                english: 'Geely',
                description: '让世界充满吉利',
                icon: '🌟',
                color: '#ff6b35'
            },
            {
                path: 'chery',
                name: '奇瑞汽车',
                english: 'Chery',
                description: '技术奇瑞，品质生活',
                icon: '⚡',
                color: '#e74c3c'
            },
            {
                path: 'changan',
                name: '长安汽车',
                english: 'Changan',
                description: '引领汽车文明，造福人类生活',
                icon: '🏛️',
                color: '#2ecc71'
            },
            {
                path: 'haval',
                name: '哈弗',
                english: 'Haval',
                description: '专业SUV品牌',
                icon: '🏔️',
                color: '#9b59b6'
            },
            {
                path: 'greatwall',
                name: '长城汽车',
                english: 'Great Wall',
                description: '专注、专业、专家',
                icon: '🏰',
                color: '#34495e'
            },
            {
                path: 'mg',
                name: '名爵',
                english: 'MG',
                description: '百年英伦运动基因',
                icon: '🏁',
                color: '#e67e22'
            }
        ];

        // 生成品牌卡片
        function generateBrandCards() {
            const grid = document.getElementById('brandsGrid');
            
            brands.forEach(brand => {
                const card = document.createElement('div');
                card.className = 'brand-card';
                card.onclick = () => selectBrand(brand.path);
                
                card.innerHTML = `
                    <div class="brand-logo" style="color: ${brand.color}">
                        ${brand.icon}
                    </div>
                    <div class="brand-name">${brand.name}</div>
                    <div class="brand-english">${brand.english}</div>
                    <div class="brand-description">${brand.description}</div>
                    <button class="brand-button" style="background: linear-gradient(135deg, ${brand.color}, ${adjustColor(brand.color, -20)})">
                        进入 ${brand.name} 倒计时
                    </button>
                `;
                
                grid.appendChild(card);
            });
        }

        // 调整颜色亮度
        function adjustColor(color, amount) {
            const usePound = color[0] === '#';
            const col = usePound ? color.slice(1) : color;
            const num = parseInt(col, 16);
            let r = (num >> 16) + amount;
            let g = (num >> 8 & 0x00FF) + amount;
            let b = (num & 0x0000FF) + amount;
            r = r > 255 ? 255 : r < 0 ? 0 : r;
            g = g > 255 ? 255 : g < 0 ? 0 : g;
            b = b > 255 ? 255 : b < 0 ? 0 : b;
            return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
        }

        // 检查用户登录状态
        function checkUserStatus() {
            // 检查是否有会话token或其他登录标识
            const hasSession = document.cookie.includes('session_token') ||
                              localStorage.getItem('user_session') ||
                              sessionStorage.getItem('user_logged_in');

            if (hasSession) {
                // 用户已登录，显示欢迎信息
                document.getElementById('pageTitle').innerHTML = '🎉 登录成功！';
                document.getElementById('welcomeInfo').style.display = 'inline-block';
                document.getElementById('skipSection').style.display = 'block';
                document.title = '选择汽车品牌 - 湖南省省补倒计时';
            }
        }

        // 选择品牌
        function selectBrand(brandPath) {
            // 保存用户选择的品牌到localStorage
            localStorage.setItem('selectedBrand', brandPath);

            // 添加选择动画
            if (event && event.target) {
                event.target.closest('.brand-card').style.transform = 'scale(0.95)';
            }

            // 延迟跳转以显示动画效果
            setTimeout(() => {
                window.location.href = `/index.php?brand=${brandPath}`;
            }, 200);
        }

        // 页面加载完成后执行初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkUserStatus();
            generateBrandCards();
        });
    </script>
</body>
</html>
