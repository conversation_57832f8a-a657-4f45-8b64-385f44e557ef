<?php
/**
 * 付费解锁系统 - PHP后端验证API
 * 处理激活码验证和用户记录
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit();
}

// 数据库配置
$dbPath = '../server/user_system.db3';

/**
 * 获取数据库连接
 */
function getDbConnection($dbPath) {
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        error_log("数据库连接失败: " . $e->getMessage());
        return null;
    }
}

/**
 * 验证激活码格式
 */
function validateCodeFormat($code) {
    return preg_match('/^[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}$/', $code);
}

/**
 * 记录日志
 */
function logMessage($message) {
    error_log(date('Y-m-d H:i:s') . " - " . $message);
}

try {
    // 获取POST数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('请求数据格式错误');
    }
    
    // 提取数据
    $activationCode = strtoupper(trim($data['activationCode'] ?? ''));
    $deviceFingerprint = trim($data['deviceFingerprint'] ?? '');
    $deviceInfo = $data['deviceInfo'] ?? '';
    $userAgent = $data['userAgent'] ?? '';
    $screenResolution = $data['screenResolution'] ?? '';
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
    
    // 基础验证
    if (empty($activationCode)) {
        throw new Exception('激活码不能为空');
    }
    
    if (empty($deviceFingerprint)) {
        throw new Exception('设备指纹获取失败');
    }
    
    if (!validateCodeFormat($activationCode)) {
        throw new Exception('激活码格式错误');
    }
    
    // 连接数据库
    $pdo = getDbConnection($dbPath);
    if (!$pdo) {
        throw new Exception('数据库连接失败');
    }
    
    // 查询激活码
    $stmt = $pdo->prepare("
        SELECT * FROM activation_codes 
        WHERE code = ? AND status = 'active'
    ");
    $stmt->execute([$activationCode]);
    $codeRecord = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$codeRecord) {
        logMessage("激活码不存在: $activationCode");
        throw new Exception('激活码不存在或已失效');
    }
    
    // 检查是否过期
    $expiresAt = new DateTime($codeRecord['expires_at']);
    $now = new DateTime();
    
    if ($now > $expiresAt) {
        // 更新状态为过期
        $stmt = $pdo->prepare("
            UPDATE activation_codes 
            SET status = 'expired' 
            WHERE code = ?
        ");
        $stmt->execute([$activationCode]);
        
        logMessage("激活码已过期: $activationCode");
        throw new Exception('激活码已过期');
    }
    
    // 检查是否已被使用
    if ($codeRecord['is_used']) {
        // 检查是否是同一设备
        if ($codeRecord['device_fingerprint'] === $deviceFingerprint) {
            // 同一设备，允许重新访问
            $stmt = $pdo->prepare("
                UPDATE user_records 
                SET last_access = datetime('now'), access_count = access_count + 1
                WHERE activation_code = ? AND device_fingerprint = ?
            ");
            $stmt->execute([$activationCode, $deviceFingerprint]);
            
            logMessage("用户重新访问: $activationCode");
            
            echo json_encode([
                'success' => true,
                'message' => '欢迎回来！激活码验证成功',
                'data' => [
                    'expiresAt' => $expiresAt->getTimestamp() * 1000,
                    'isReturningUser' => true
                ]
            ]);
            exit();
        } else {
            logMessage("激活码已在其他设备使用: $activationCode");
            throw new Exception('激活码已在其他设备上使用');
        }
    }
    
    // 首次使用激活码
    $pdo->beginTransaction();
    
    try {
        // 更新激活码状态
        $stmt = $pdo->prepare("
            UPDATE activation_codes 
            SET is_used = 1, 
                device_fingerprint = ?, 
                used_at = datetime('now'),
                status = 'used'
            WHERE code = ?
        ");
        $stmt->execute([$deviceFingerprint, $activationCode]);
        
        // 记录用户信息
        $stmt = $pdo->prepare("
            INSERT INTO user_records 
            (activation_code, device_fingerprint, device_info, 
             ip_address, user_agent, screen_resolution, unlock_time)
            VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
        ");
        $stmt->execute([
            $activationCode, 
            $deviceFingerprint, 
            $deviceInfo,
            $clientIP, 
            $userAgent, 
            $screenResolution
        ]);
        
        $pdo->commit();
        
        logMessage("激活码验证成功: $activationCode, 设备: " . substr($deviceFingerprint, 0, 16) . "...");
        
        echo json_encode([
            'success' => true,
            'message' => '激活成功！欢迎使用',
            'data' => [
                'expiresAt' => $expiresAt->getTimestamp() * 1000,
                'isReturningUser' => false
            ]
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    logMessage("验证失败: " . $e->getMessage());
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
