<?php
/**
 * 调试会话和验证码匹配问题
 */

session_start();
$dbPath = '../server/user_system.db3';

try {
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $currentSessionId = session_id();
    
    echo "<h2>会话和验证码调试</h2>";
    echo "<p><strong>当前会话ID:</strong> $currentSessionId</p>";
    
    // 查询所有验证码记录
    $stmt = $pdo->query("
        SELECT id, code_value, code_hash, session_id, expires_at, is_used, created_at 
        FROM captcha_codes 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $captchas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>最新10条验证码记录：</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>验证码值</th><th>会话ID</th><th>匹配</th><th>状态</th><th>过期时间</th><th>创建时间</th></tr>";
    
    foreach ($captchas as $captcha) {
        $isCurrentSession = $captcha['session_id'] === $currentSessionId;
        $isExpired = strtotime($captcha['expires_at']) < time();
        $status = $captcha['is_used'] ? '已使用' : ($isExpired ? '已过期' : '有效');
        
        $rowColor = $isCurrentSession ? '#e6ffe6' : '#ffe6e6';
        if ($captcha['is_used']) $rowColor = '#ffcccc';
        elseif ($isExpired) $rowColor = '#ffffcc';
        
        echo "<tr style='background-color: $rowColor;'>";
        echo "<td>{$captcha['id']}</td>";
        echo "<td><strong>{$captcha['code_value']}</strong></td>";
        echo "<td>" . substr($captcha['session_id'], 0, 20) . "...</td>";
        echo "<td>" . ($isCurrentSession ? '✅' : '❌') . "</td>";
        echo "<td>$status</td>";
        echo "<td>{$captcha['expires_at']}</td>";
        echo "<td>{$captcha['created_at']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 查找当前会话的有效验证码
    $stmt = $pdo->prepare("
        SELECT code_value, code_hash, expires_at, is_used 
        FROM captcha_codes 
        WHERE session_id = ? AND is_used = 0 AND expires_at > datetime('now')
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$currentSessionId]);
    $validCaptcha = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($validCaptcha) {
        echo "<div style='background: #d4edda; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3>✅ 当前会话的有效验证码：</h3>";
        echo "<p style='font-size: 24px; font-weight: bold; color: #155724;'>{$validCaptcha['code_value']}</p>";
        echo "<p>过期时间：{$validCaptcha['expires_at']}</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3>❌ 当前会话没有有效验证码</h3>";
        echo "<p>需要在当前会话中生成验证码</p>";
        echo "</div>";
    }
    
    // 测试验证码验证逻辑
    if (isset($_GET['test_code'])) {
        $testCode = $_GET['test_code'];
        echo "<h3>测试验证码验证：</h3>";
        echo "<p>测试验证码：<strong>$testCode</strong></p>";
        
        $codeHash = hash('sha256', strtoupper($testCode));
        $stmt = $pdo->prepare("
            SELECT id FROM captcha_codes 
            WHERE code_hash = ? AND session_id = ? 
            AND expires_at > datetime('now') AND is_used = 0
        ");
        $stmt->execute([$codeHash, $currentSessionId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            echo "<p style='color: green;'>✅ 验证码验证成功！</p>";
        } else {
            echo "<p style='color: red;'>❌ 验证码验证失败！</p>";
            echo "<p>哈希值：$codeHash</p>";
        }
    }
    
    echo "<hr>";
    echo "<h3>操作链接：</h3>";
    echo "<p><a href='../api/captcha.php' target='_blank'>在当前会话生成验证码</a></p>";
    if ($validCaptcha) {
        echo "<p><a href='?test_code={$validCaptcha['code_value']}'>测试验证码验证</a></p>";
    }
    echo "<p><a href='javascript:location.reload()'>刷新页面</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ 错误: " . $e->getMessage() . "</p>";
}
?>
