/**
 * 新的会话检查系统
 * 用于主页面验证用户登录状态和激活状态
 */

class NewSessionChecker {
    constructor() {
        this.sessionToken = null;
        this.userInfo = null;
        this.checkInterval = null;
        this.networkCheckInterval = null;
        this.lastOnlineTime = Date.now();
        this.maxOfflineTime = 60 * 1000; // 最大离线时间：60秒
        this.isOnline = navigator.onLine;
        this.consecutiveFailures = 0;
        this.maxConsecutiveFailures = 3;
        this.init();
    }

    async init() {
        console.log('🔍 开始检查用户会话状态...');
        
        // 获取会话令牌
        this.sessionToken = this.getSessionToken();
        
        if (!this.sessionToken) {
            console.log('❌ 未找到会话令牌，跳转到登录页面');
            this.redirectToLogin();
            return;
        }
        
        // 验证会话
        const isValid = await this.verifySession();
        
        if (!isValid) {
            console.log('❌ 会话验证失败，跳转到登录页面');
            this.redirectToLogin();
            return;
        }
        
        console.log('✅ 会话验证通过，允许访问主页面');
        this.initMainPage();

        // 启动定期检查
        this.startPeriodicCheck();

        // 启动网络监控
        this.startNetworkMonitoring();
    }

    getSessionToken() {
        // 优先从localStorage获取
        let token = localStorage.getItem('session_token');
        if (token) return token;
        
        // 从sessionStorage获取
        token = sessionStorage.getItem('session_token');
        if (token) return token;
        
        // 从Cookie获取
        token = this.getCookie('session_token');
        if (token) return token;
        
        return null;
    }

    async verifySession() {
        try {
            const response = await fetch('api/verify-session.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    session_token: this.sessionToken
                })
            });

            const data = await response.json();

            if (data.success) {
                this.userInfo = data.data;
                this.consecutiveFailures = 0; // 重置失败计数
                this.lastOnlineTime = Date.now(); // 更新最后在线时间
                console.log('✅ 用户信息:', this.userInfo);
                return true;
            } else if (data.code === 'NEED_ACTIVATION') {
                console.log('⚠️ 需要激活码，跳转到登录页面');
                this.showActivationNeededMessage();
                return false;
            } else {
                console.log('❌ 会话验证失败:', data.message);
                this.clearSession();
                return false;
            }
        } catch (error) {
            console.error('❌ 会话验证请求失败:', error);
            this.consecutiveFailures++;

            // 检查是否是网络问题
            if (!navigator.onLine) {
                console.log('🌐 检测到离线状态');
                this.handleOfflineState();
                return false;
            }

            // 连续失败次数过多，可能是故意断网
            if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
                console.log('⚠️ 连续验证失败次数过多，可能存在网络问题或恶意行为');
                this.handleSuspiciousActivity();
                return false;
            }

            // 网络错误时的降级处理
            if (this.sessionToken && this.isRecentSession()) {
                console.log('🌐 网络错误，使用本地会话继续访问');
                return true;
            }

            return false;
        }
    }

    isRecentSession() {
        // 检查会话是否是最近的（90天内）- 适合商家广告长期展示
        const sessionTime = localStorage.getItem('session_time');
        if (!sessionTime) return false;

        const now = Date.now();
        const sessionAge = now - parseInt(sessionTime);
        const maxAge = 90 * 24 * 60 * 60 * 1000; // 90天

        return sessionAge < maxAge;
    }

    initMainPage() {
        console.log('🎉 主页面初始化完成');

        // 显示用户图标菜单
        this.displayUserInfo();
    }

    displayUserInfo() {
        if (!this.userInfo) return;

        // 创建用户图标容器
        const userContainer = document.createElement('div');
        userContainer.id = 'userInfoContainer';
        userContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        `;

        // 创建用户图标按钮
        const userIcon = document.createElement('div');
        userIcon.id = 'userIcon';
        userIcon.style.cssText = `
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            // border: 2px solid rgba(0,0,0,0.1);
        `;

        // SVG用户图标
        userIcon.innerHTML = `
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
            </svg>
        `;

        // 创建下拉菜单
        const dropdownMenu = document.createElement('div');
        dropdownMenu.id = 'userDropdown';
        dropdownMenu.style.cssText = `
            position: absolute;
            top: 50px;
            right: 0;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            padding: 15px;
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.1);
        `;

        const expiresDate = new Date(this.userInfo.activation_expires * 1000);
        dropdownMenu.innerHTML = `
            <div style="padding-bottom: 10px; border-bottom: 1px solid #eee; margin-bottom: 10px;">
                <div style="font-weight: bold; color: #333; margin-bottom: 5px;">👤 ${this.userInfo.username}</div>
                <div style="font-size: 12px; color: #666;">已登录</div>
            </div>
            <div style="margin-bottom: 10px;">
                <div style="font-size: 12px; color: #666; margin-bottom: 3px;">激活码</div>
                <div style="font-size: 13px; color: #333; font-family: monospace;">${this.userInfo.activation_code}</div>
            </div>
            <div style="margin-bottom: 15px;">
                <div style="font-size: 12px; color: #666; margin-bottom: 3px;">到期时间</div>
                <div style="font-size: 13px; color: #333;">${expiresDate.toLocaleDateString()}</div>
            </div>
            <button id="logoutBtn" style="
                width: 100%;
                background: #f44336;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 13px;
                transition: background 0.3s ease;
            ">🚪 退出登录</button>
        `;

        // 组装容器
        userContainer.appendChild(userIcon);
        userContainer.appendChild(dropdownMenu);
        document.body.appendChild(userContainer);

        // 添加事件监听
        this.setupUserMenuEvents(userIcon, dropdownMenu);
    }

    setupUserMenuEvents(userIcon, dropdownMenu) {
        let hideTimeout = null;
        let isMenuVisible = false;

        // 鼠标悬停显示菜单
        userIcon.addEventListener('mouseenter', () => {
            clearTimeout(hideTimeout);
            isMenuVisible = true;

            // 显示菜单
            dropdownMenu.style.opacity = '1';
            dropdownMenu.style.visibility = 'visible';
            dropdownMenu.style.transform = 'translateY(0)';

            // 图标高亮
            userIcon.style.background = 'rgba(76, 175, 80, 0.1)';
            userIcon.style.borderColor = 'rgba(76, 175, 80, 0.3)';
        });

        // 鼠标离开图标
        userIcon.addEventListener('mouseleave', () => {
            if (!isMenuVisible) return;

            hideTimeout = setTimeout(() => {
                this.hideUserMenu(userIcon, dropdownMenu);
            }, 100); // 短暂延迟，允许鼠标移到菜单上
        });

        // 鼠标进入菜单
        dropdownMenu.addEventListener('mouseenter', () => {
            clearTimeout(hideTimeout);
        });

        // 鼠标离开菜单
        dropdownMenu.addEventListener('mouseleave', () => {
            hideTimeout = setTimeout(() => {
                this.hideUserMenu(userIcon, dropdownMenu);
            }, 100);
        });

        // 退出登录按钮
        const logoutBtn = dropdownMenu.querySelector('#logoutBtn');
        logoutBtn.addEventListener('click', () => {
            this.logout();
        });

        logoutBtn.addEventListener('mouseenter', () => {
            logoutBtn.style.background = '#d32f2f';
        });

        logoutBtn.addEventListener('mouseleave', () => {
            logoutBtn.style.background = '#f44336';
        });

        // 3秒后降低透明度
        setTimeout(() => {
            if (!isMenuVisible) {
                userIcon.style.opacity = '0.4';
            }
        }, 3000);
    }

    hideUserMenu(userIcon, dropdownMenu) {
        // 隐藏菜单
        dropdownMenu.style.opacity = '0';
        dropdownMenu.style.visibility = 'hidden';
        dropdownMenu.style.transform = 'translateY(-10px)';

        // 恢复图标样式
        userIcon.style.background = 'rgba(255, 255, 255, 0.9)';
        userIcon.style.borderColor = 'rgba(0,0,0,0.1)';

        // 3秒后降低透明度
        setTimeout(() => {
            userIcon.style.opacity = '0.4';
        }, 3000);
    }

    // 原有的显示方法已被新的用户图标菜单替代

    startPeriodicCheck() {
        // 每30分钟检查一次会话状态（适合长期展示的商家广告）
        this.checkInterval = setInterval(async () => {
            console.log('🔄 定期检查会话状态...');
            const isValid = await this.verifySession();

            if (!isValid) {
                console.log('❌ 会话已失效，跳转到登录页面');
                this.showSessionExpiredMessage();
                this.redirectToLogin();
            }
        }, 30 * 60 * 1000); // 30分钟
    }

    startNetworkMonitoring() {
        console.log('🌐 启动网络监控...');

        // 监听网络状态变化
        window.addEventListener('online', () => {
            console.log('🌐 网络已连接');
            this.isOnline = true;
            this.lastOnlineTime = Date.now();
            this.consecutiveFailures = 0;

            // 网络恢复后立即验证会话
            this.verifySession().then(isValid => {
                if (!isValid) {
                    this.showNetworkRecoveryMessage();
                    this.redirectToLogin();
                }
            });
        });

        window.addEventListener('offline', () => {
            console.log('🌐 网络已断开');
            this.isOnline = false;
            this.handleOfflineState();
        });

        // 定期检查网络状态和离线时间
        this.networkCheckInterval = setInterval(() => {
            this.checkNetworkStatus();
        }, 10 * 1000); // 每10秒检查一次

        // 启动心跳检测
        this.startHeartbeat();
    }

    checkNetworkStatus() {
        const currentTime = Date.now();
        const offlineTime = currentTime - this.lastOnlineTime;

        // 如果离线时间超过限制
        if (!this.isOnline && offlineTime > this.maxOfflineTime) {
            console.log(`⚠️ 离线时间过长: ${Math.round(offlineTime / 1000)}秒`);
            this.handleLongOffline();
            return;
        }

        // 检查是否故意断网（页面活跃但网络断开）
        if (!navigator.onLine && this.isPageActive()) {
            console.log('🚨 检测到可能的故意断网行为');
            this.handleSuspiciousActivity();
        }
    }

    isPageActive() {
        // 检查页面是否活跃（用户是否在操作页面）
        return document.hasFocus() && !document.hidden;
    }

    handleOfflineState() {
        console.log('🌐 处理离线状态...');

        // 显示离线提示
        this.showOfflineWarning();

        // 如果离线时间过长，强制退出
        setTimeout(() => {
            if (!this.isOnline) {
                this.handleLongOffline();
            }
        }, this.maxOfflineTime);
    }

    handleLongOffline() {
        console.log('⚠️ 离线时间过长，强制退出');
        this.showLongOfflineMessage();
        this.clearSession();
        setTimeout(() => {
            this.redirectToLogin();
        }, 3000);
    }

    handleSuspiciousActivity() {
        console.log('🚨 检测到可疑活动，强制退出');
        this.showSuspiciousActivityMessage();
        this.clearSession();
        setTimeout(() => {
            this.redirectToLogin();
        }, 2000);
    }

    startHeartbeat() {
        console.log('💓 启动心跳检测...');

        // 每20秒发送一次心跳
        this.heartbeatInterval = setInterval(async () => {
            await this.sendHeartbeat();
        }, 20 * 1000);
    }

    async sendHeartbeat() {
        if (!this.sessionToken) return;

        try {
            const currentTime = Date.now();
            const offlineDuration = this.isOnline ? 0 : Math.round((currentTime - this.lastOnlineTime) / 1000);

            const response = await fetch('api/network-heartbeat.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    session_token: this.sessionToken,
                    online_status: this.isOnline,
                    last_offline_duration: offlineDuration,
                    client_timestamp: currentTime
                })
            });

            const data = await response.json();

            if (!data.success) {
                console.log('💓 心跳检测失败:', data.message);

                if (data.code === 'MULTIPLE_SESSIONS') {
                    this.showMultipleSessionsMessage();
                    this.redirectToLogin();
                } else if (data.code === 'LONG_OFFLINE') {
                    this.showLongOfflineMessage();
                    this.redirectToLogin();
                } else {
                    // 其他错误，可能是会话过期
                    this.showSessionExpiredMessage();
                    this.redirectToLogin();
                }
            } else {
                console.log('💓 心跳检测正常');
                this.consecutiveFailures = 0;
                this.lastOnlineTime = currentTime;
            }

        } catch (error) {
            console.error('💓 心跳检测请求失败:', error);
            this.consecutiveFailures++;

            // 如果连续失败且不是网络问题，可能是恶意行为
            if (this.consecutiveFailures >= this.maxConsecutiveFailures && navigator.onLine) {
                this.handleSuspiciousActivity();
            }
        }
    }

    showMultipleSessionsMessage() {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        `;

        overlay.innerHTML = `
            <div style="
                background: white;
                padding: 40px;
                border-radius: 10px;
                text-align: center;
                max-width: 400px;
            ">
                <h2 style="color: #f44336; margin-bottom: 20px;">🔒 检测到多设备登录</h2>
                <p style="margin-bottom: 20px;">系统检测到您的账号在其他设备上登录，当前会话已失效</p>
                <div style="color: #666; font-size: 14px;">即将跳转到登录页面...</div>
            </div>
        `;

        document.body.appendChild(overlay);
    }

    stopPeriodicCheck() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
        }

        if (this.networkCheckInterval) {
            clearInterval(this.networkCheckInterval);
            this.networkCheckInterval = null;
        }

        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    showActivationNeededMessage() {
        // 显示需要激活的提示
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        `;

        overlay.innerHTML = `
            <div style="
                background: white;
                padding: 40px;
                border-radius: 10px;
                text-align: center;
                max-width: 400px;
            ">
                <h2 style="color: #FFC107; margin-bottom: 20px;">⚠️ 需要激活码</h2>
                <p style="margin-bottom: 20px;">您的账号需要绑定有效的激活码才能访问系统</p>
                <div style="color: #666; font-size: 14px;">3秒后自动跳转到登录页面...</div>
            </div>
        `;

        document.body.appendChild(overlay);

        setTimeout(() => this.redirectToLogin(), 3000);
    }

    showSessionExpiredMessage() {
        // 显示会话过期提示
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        `;

        overlay.innerHTML = `
            <div style="
                background: white;
                padding: 40px;
                border-radius: 10px;
                text-align: center;
                max-width: 400px;
            ">
                <h2 style="color: #f44336; margin-bottom: 20px;">🔒 会话已失效</h2>
                <p style="margin-bottom: 20px;">您的账号在其他设备登录，当前会话已失效</p>
                <div style="color: #666; font-size: 14px;">即将跳转到登录页面...</div>
            </div>
        `;

        document.body.appendChild(overlay);
    }

    showOfflineWarning() {
        // 显示离线警告（不阻塞页面）
        const warning = document.createElement('div');
        warning.id = 'offlineWarning';
        warning.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 193, 7, 0.95);
            color: #856404;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 9999;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            animation: slideDown 0.3s ease;
        `;

        warning.innerHTML = `
            ⚠️ 网络连接已断开，请尽快恢复网络连接
        `;

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideDown {
                from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
                to { transform: translateX(-50%) translateY(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);

        // 移除已存在的警告
        const existingWarning = document.getElementById('offlineWarning');
        if (existingWarning) {
            existingWarning.remove();
        }

        document.body.appendChild(warning);

        // 网络恢复时自动移除
        const removeWarning = () => {
            if (navigator.onLine && warning.parentNode) {
                warning.remove();
                window.removeEventListener('online', removeWarning);
            }
        };
        window.addEventListener('online', removeWarning);
    }

    showLongOfflineMessage() {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        `;

        overlay.innerHTML = `
            <div style="
                background: white;
                padding: 40px;
                border-radius: 10px;
                text-align: center;
                max-width: 400px;
            ">
                <h2 style="color: #ff9800; margin-bottom: 20px;">⚠️ 网络断开时间过长</h2>
                <p style="margin-bottom: 20px;">为了安全起见，系统将自动退出登录</p>
                <div style="color: #666; font-size: 14px;">3秒后自动跳转到登录页面...</div>
            </div>
        `;

        document.body.appendChild(overlay);
    }

    showSuspiciousActivityMessage() {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        `;

        overlay.innerHTML = `
            <div style="
                background: white;
                padding: 40px;
                border-radius: 10px;
                text-align: center;
                max-width: 400px;
            ">
                <h2 style="color: #f44336; margin-bottom: 20px;">🚨 检测到异常行为</h2>
                <p style="margin-bottom: 20px;">系统检测到可能的恶意网络操作，为了安全起见将退出登录</p>
                <div style="color: #666; font-size: 14px;">2秒后自动跳转到登录页面...</div>
            </div>
        `;

        document.body.appendChild(overlay);
    }

    showNetworkRecoveryMessage() {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        `;

        overlay.innerHTML = `
            <div style="
                background: white;
                padding: 40px;
                border-radius: 10px;
                text-align: center;
                max-width: 400px;
            ">
                <h2 style="color: #2196F3; margin-bottom: 20px;">🌐 网络已恢复</h2>
                <p style="margin-bottom: 20px;">检测到您的会话在离线期间已失效，请重新登录</p>
                <div style="color: #666; font-size: 14px;">即将跳转到登录页面...</div>
            </div>
        `;

        document.body.appendChild(overlay);
    }

    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
        return null;
    }

    clearSession() {
        localStorage.removeItem('session_token');
        localStorage.removeItem('session_time');
        sessionStorage.removeItem('session_token');
        
        // 清除Cookie
        document.cookie = 'session_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        
        this.sessionToken = null;
        this.userInfo = null;
    }

    logout() {
        console.log('🚪 用户主动退出登录');
        this.stopPeriodicCheck();
        this.clearSession();
        this.redirectToLogin();
    }

    redirectToLogin() {
        console.log('🔄 跳转到登录页面');
        this.stopPeriodicCheck();
        
        // 显示跳转提示
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        `;
        
        overlay.innerHTML = `
            <div style="
                background: white;
                padding: 40px;
                border-radius: 10px;
                text-align: center;
                max-width: 400px;
            ">
                <h2 style="color: #2196F3; margin-bottom: 20px;">🔄 正在跳转</h2>
                <p style="margin-bottom: 20px;">即将跳转到登录页面...</p>
                <div style="color: #666; font-size: 14px;">请稍候...</div>
            </div>
        `;
        
        document.body.appendChild(overlay);
        
        setTimeout(() => {
            window.location.href = 'new-lock.html';
        }, 1500);
    }
}

// 页面加载完成后立即检查会话状态
document.addEventListener('DOMContentLoaded', () => {
    console.log('🔧 新会话检查系统启动');
    window.sessionChecker = new NewSessionChecker();
});
