<?php
/**
 * 激活码重新绑定API
 * 允许将已使用的激活码重新绑定到当前设备
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit();
}

// 数据库配置
$dbPath = '../server/user_system.db3';

/**
 * 记录日志
 */
function logMessage($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message" . PHP_EOL;
    file_put_contents('../server/activation.log', $logEntry, FILE_APPEND | LOCK_EX);
}

try {
    // 获取请求数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('无效的JSON数据');
    }
    
    // 验证必需字段
    $requiredFields = ['activationCode', 'deviceFingerprint'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            throw new Exception("缺少必需字段: $field");
        }
    }
    
    $activationCode = trim($data['activationCode']);
    $deviceFingerprint = trim($data['deviceFingerprint']);
    $deviceInfo = $data['deviceInfo'] ?? '';
    $userAgent = $data['userAgent'] ?? '';
    $screenResolution = $data['screenResolution'] ?? '';
    
    logMessage("重新绑定请求: $activationCode");
    
    // 连接数据库
    $pdo = new PDO("sqlite:$dbPath");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 查找激活码
    $stmt = $pdo->prepare("SELECT * FROM activation_codes WHERE code = ?");
    $stmt->execute([$activationCode]);
    $codeRecord = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$codeRecord) {
        logMessage("激活码不存在: $activationCode");
        throw new Exception('激活码不存在');
    }
    
    // 检查激活码状态
    if ($codeRecord['status'] !== 'active') {
        logMessage("激活码状态无效: $activationCode, 状态: " . $codeRecord['status']);
        throw new Exception('激活码状态无效');
    }
    
    // 检查是否过期
    $expiresAt = new DateTime($codeRecord['expires_at']);
    $now = new DateTime();
    
    if ($expiresAt <= $now) {
        logMessage("激活码已过期: $activationCode");
        throw new Exception('激活码已过期');
    }
    
    // 开始事务
    $pdo->beginTransaction();
    
    try {
        if ($codeRecord['is_used']) {
            // 激活码已被使用，进行重新绑定
            logMessage("重新绑定激活码: $activationCode 到设备: $deviceFingerprint");
            
            // 更新激活码的设备指纹
            $stmt = $pdo->prepare("
                UPDATE activation_codes 
                SET device_fingerprint = ?, used_at = datetime('now')
                WHERE code = ?
            ");
            $stmt->execute([$deviceFingerprint, $activationCode]);
            
            // 删除旧的用户记录
            $stmt = $pdo->prepare("DELETE FROM user_records WHERE activation_code = ?");
            $stmt->execute([$activationCode]);
            
            // 创建新的用户记录
            $stmt = $pdo->prepare("
                INSERT INTO user_records (
                    activation_code, device_fingerprint, device_info,
                    user_agent, screen_resolution, unlock_time, last_access, access_count
                ) VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'), 1)
            ");
            $stmt->execute([
                $activationCode, $deviceFingerprint, $deviceInfo,
                $userAgent, $screenResolution
            ]);
            
        } else {
            // 激活码未使用，正常激活
            logMessage("首次激活: $activationCode");
            
            // 标记激活码为已使用
            $stmt = $pdo->prepare("
                UPDATE activation_codes 
                SET is_used = 1, device_fingerprint = ?, used_at = datetime('now')
                WHERE code = ?
            ");
            $stmt->execute([$deviceFingerprint, $activationCode]);
            
            // 创建用户记录
            $stmt = $pdo->prepare("
                INSERT INTO user_records (
                    activation_code, device_fingerprint, device_info,
                    user_agent, screen_resolution, unlock_time, last_access, access_count
                ) VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'), 1)
            ");
            $stmt->execute([
                $activationCode, $deviceFingerprint, $deviceInfo,
                $userAgent, $screenResolution
            ]);
        }
        
        // 提交事务
        $pdo->commit();
        
        logMessage("激活码绑定成功: $activationCode");
        
        echo json_encode([
            'success' => true,
            'message' => '激活码绑定成功',
            'data' => [
                'activationCode' => $activationCode,
                'deviceFingerprint' => $deviceFingerprint,
                'expiresAt' => $expiresAt->getTimestamp() * 1000,
                'isRebind' => $codeRecord['is_used'] ? true : false
            ]
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    logMessage("重新绑定失败: " . $e->getMessage());
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
