<?php
/**
 * Session自动清理状态检查
 * 检查当前的自动清理机制是否正常工作
 */

header('Content-Type: text/html; charset=utf-8');

$dbPath = '../server/user_system.db3';

// 引入自动清理助手
require_once '../server/session-auto-cleaner.php';

function checkAutoCleanupStatus() {
    global $dbPath;
    
    $status = [
        'mechanisms' => [],
        'current_stats' => [],
        'recommendations' => []
    ];
    
    // 检查各种自动清理机制
    $status['mechanisms'] = [
        'verify_session_api' => [
            'name' => 'Session验证API清理',
            'file' => 'api/verify-session.php',
            'enabled' => checkFileContains('api/verify-session.php', 'session-auto-cleaner.php'),
            'probability' => '5%',
            'description' => '用户访问时随机触发清理'
        ],
        'heartbeat_api' => [
            'name' => '心跳API清理',
            'file' => 'api/network-heartbeat.php', 
            'enabled' => checkFileContains('api/network-heartbeat.php', 'session-auto-cleaner.php'),
            'probability' => '3%',
            'description' => '心跳检测时随机触发清理'
        ],
        'login_api' => [
            'name' => '登录时清理',
            'file' => 'api/login.php',
            'enabled' => checkFileContains('api/login.php', 'session-auto-cleaner.php'),
            'probability' => '100%',
            'description' => '用户登录时必定执行清理'
        ]
    ];
    
    // 获取当前统计信息
    $status['current_stats'] = getSessionCleanupStats($dbPath);
    
    // 检查清理需求
    $cleanupCheck = shouldCleanupSessions($dbPath);
    $status['cleanup_needed'] = $cleanupCheck;
    
    // 生成建议
    if ($cleanupCheck['needs_cleanup']) {
        $status['recommendations'][] = [
            'type' => 'urgent',
            'message' => '需要立即清理: ' . $cleanupCheck['reason']
        ];
    }
    
    if ($status['current_stats']['waste_ratio'] > 50) {
        $status['recommendations'][] = [
            'type' => 'warning',
            'message' => '无效session占比过高 (' . $status['current_stats']['waste_ratio'] . '%)'
        ];
    }
    
    $enabledCount = 0;
    foreach ($status['mechanisms'] as $mechanism) {
        if ($mechanism['enabled']) $enabledCount++;
    }
    
    if ($enabledCount < 3) {
        $status['recommendations'][] = [
            'type' => 'info',
            'message' => '建议启用更多自动清理机制以提高清理频率'
        ];
    }
    
    return $status;
}

function checkFileContains($filePath, $searchString) {
    // 添加相对路径前缀
    $fullPath = '../' . $filePath;

    if (!file_exists($fullPath)) {
        return false;
    }

    $content = file_get_contents($fullPath);
    return strpos($content, $searchString) !== false;
}

$status = checkAutoCleanupStatus();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session自动清理状态</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
        .warning-box { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; }
        .error-box { background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0; }
        .status-enabled { background: #d4edda; color: #155724; padding: 5px 10px; border-radius: 3px; }
        .status-disabled { background: #f8d7da; color: #721c24; padding: 5px 10px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🤖 Session自动清理状态</h1>
    <p><strong>检查时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="test-section">
        <h2>📊 当前Session统计</h2>
        
        <?php if (isset($status['current_stats']['error'])): ?>
            <p class="error">❌ 获取统计失败: <?php echo htmlspecialchars($status['current_stats']['error']); ?></p>
        <?php else: ?>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3><?php echo number_format($status['current_stats']['total_sessions']); ?></h3>
                    <p>总会话数</p>
                </div>
                <div class="stat-card">
                    <h3 class="success"><?php echo number_format($status['current_stats']['active_sessions']); ?></h3>
                    <p>活跃会话</p>
                </div>
                <div class="stat-card">
                    <h3 class="<?php echo $status['current_stats']['expired_sessions'] > 0 ? 'warning' : 'success'; ?>">
                        <?php echo number_format($status['current_stats']['expired_sessions']); ?>
                    </h3>
                    <p>过期会话</p>
                </div>
                <div class="stat-card">
                    <h3 class="info"><?php echo number_format($status['current_stats']['inactive_sessions']); ?></h3>
                    <p>非活跃会话</p>
                </div>
            </div>
            
            <?php $wasteRatio = $status['current_stats']['waste_ratio']; ?>
            <div class="<?php echo $wasteRatio > 50 ? 'warning-box' : ($wasteRatio > 30 ? 'warning-box' : 'highlight'); ?>">
                <h3><?php echo $wasteRatio > 50 ? '⚠️ 存储浪费严重' : ($wasteRatio > 30 ? '⚠️ 存储浪费中等' : '✅ 存储效率良好'); ?></h3>
                <p><strong>无效会话占比:</strong> <?php echo $wasteRatio; ?>%</p>
                <p><strong>最后清理时间:</strong> <?php echo $status['current_stats']['last_cleanup'] ?? '从未清理'; ?></p>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>🔧 自动清理机制状态</h2>
        
        <table>
            <tr>
                <th>清理机制</th>
                <th>状态</th>
                <th>触发概率</th>
                <th>说明</th>
                <th>文件位置</th>
            </tr>
            <?php foreach ($status['mechanisms'] as $key => $mechanism): ?>
                <tr>
                    <td><strong><?php echo htmlspecialchars($mechanism['name']); ?></strong></td>
                    <td>
                        <?php if ($mechanism['enabled']): ?>
                            <span class="status-enabled">✅ 已启用</span>
                        <?php else: ?>
                            <span class="status-disabled">❌ 未启用</span>
                        <?php endif; ?>
                    </td>
                    <td><?php echo htmlspecialchars($mechanism['probability']); ?></td>
                    <td><?php echo htmlspecialchars($mechanism['description']); ?></td>
                    <td><code><?php echo htmlspecialchars($mechanism['file']); ?></code></td>
                </tr>
            <?php endforeach; ?>
        </table>
        
        <?php
        $enabledCount = 0;
        foreach ($status['mechanisms'] as $mechanism) {
            if ($mechanism['enabled']) $enabledCount++;
        }
        ?>
        
        <div class="<?php echo $enabledCount >= 3 ? 'highlight' : 'warning-box'; ?>">
            <h3><?php echo $enabledCount >= 3 ? '✅ 自动清理机制完整' : '⚠️ 自动清理机制不完整'; ?></h3>
            <p><strong>已启用机制:</strong> <?php echo $enabledCount; ?>/3</p>
            <?php if ($enabledCount >= 3): ?>
                <p>所有自动清理机制都已启用，过期session会被自动清理。</p>
            <?php else: ?>
                <p>部分自动清理机制未启用，可能导致过期session积累。</p>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🎯 清理需求分析</h2>
        
        <?php if ($status['cleanup_needed']['needs_cleanup']): ?>
            <div class="error-box">
                <h3>🔴 需要立即清理</h3>
                <p><strong>原因:</strong> <?php echo htmlspecialchars($status['cleanup_needed']['reason']); ?></p>
                <p><strong>建议:</strong> 立即执行手动清理或等待自动清理触发</p>
            </div>
        <?php else: ?>
            <div class="highlight">
                <h3>✅ 无需立即清理</h3>
                <p>当前session状态良好，自动清理机制正常工作。</p>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>💡 优化建议</h2>
        
        <?php if (empty($status['recommendations'])): ?>
            <div class="highlight">
                <h3>✅ 系统状态良好</h3>
                <p>当前session自动清理机制运行正常，无需特别优化。</p>
            </div>
        <?php else: ?>
            <?php foreach ($status['recommendations'] as $rec): ?>
                <div class="<?php echo $rec['type'] === 'urgent' ? 'error-box' : ($rec['type'] === 'warning' ? 'warning-box' : 'highlight'); ?>">
                    <p><strong><?php echo ucfirst($rec['type']); ?>:</strong> <?php echo htmlspecialchars($rec['message']); ?></p>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>🔄 自动清理工作原理</h2>
        
        <h3>触发机制:</h3>
        <ul>
            <li><strong>Session验证时</strong> - 用户访问页面时5%概率触发清理</li>
            <li><strong>心跳检测时</strong> - 每20秒心跳时3%概率触发清理</li>
            <li><strong>用户登录时</strong> - 登录成功时100%执行智能清理</li>
        </ul>
        
        <h3>清理策略:</h3>
        <ul>
            <li><strong>过期session</strong> - 立即删除所有过期的session</li>
            <li><strong>用户session限制</strong> - 每用户最多保留3个最新session</li>
            <li><strong>非活跃session</strong> - 删除7天前的非活跃session</li>
        </ul>
        
        <h3>清理条件:</h3>
        <ul>
            <li>发现过期session</li>
            <li>总session数超过50个</li>
            <li>距离上次清理超过24小时</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关工具</h2>
        <ul>
            <li><a href="session-analysis.php">Session存储分析</a></li>
            <li><a href="session-cleaner.php">手动Session清理</a></li>
            <li><a href="login-logs-manager.php">登录日志管理</a></li>
            <li><a href="../new-lock.html">返回登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
