<?php
/**
 * 测试修复后的激活码绑定功能
 */

header('Content-Type: text/html; charset=utf-8');

$dbPath = '../server/user_system.db3';

function checkActivationCodeStatus($code) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->prepare("
            SELECT id, code, is_used, bound_user_id, status, created_at, expires_at, used_at
            FROM activation_codes 
            WHERE code = ?
        ");
        $stmt->execute([$code]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return false;
    }
}

function simulateBindingAPI($activationCode) {
    global $dbPath;
    
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 模拟检查激活码的逻辑（来自修复后的 bind-activation.php）
        $stmt = $pdo->prepare("
            SELECT id, code, is_used, bound_user_id, expires_at, status
            FROM activation_codes 
            WHERE code = ?
        ");
        $stmt->execute([$activationCode]);
        $codeInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$codeInfo) {
            return ['success' => false, 'message' => '激活码不存在'];
        }
        
        // 检查激活码状态
        if ($codeInfo['status'] !== 'active') {
            return ['success' => false, 'message' => '激活码状态无效'];
        }
        
        if ($codeInfo['is_used']) {
            // 检查数据一致性：如果is_used=1但bound_user_id为空，说明数据不一致
            if ($codeInfo['bound_user_id'] === null || $codeInfo['bound_user_id'] === '') {
                // 数据不一致，记录错误并提供修复建议
                error_log("激活码数据不一致: code={$activationCode}, is_used=1, bound_user_id=NULL");
                return ['success' => false, 'message' => '激活码状态异常，请联系管理员或稍后重试'];
            }
            
            // 使用严格比较避免类型转换问题
            $mockUserId = 999; // 模拟用户ID
            if ((int)$codeInfo['bound_user_id'] === (int)$mockUserId) {
                return ['success' => false, 'message' => '您已经绑定过这个激活码了'];
            } else {
                return ['success' => false, 'message' => '激活码已被其他用户使用'];
            }
        }
        
        // 检查是否过期
        if (strtotime($codeInfo['expires_at']) <= time()) {
            return ['success' => false, 'message' => '激活码已过期'];
        }
        
        return ['success' => true, 'message' => '激活码可以正常绑定！'];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => '测试失败: ' . $e->getMessage()];
    }
}

// 测试激活码
$testCode = 'N3WK7-7YJW7-JHXB4-NWWEJ';
$codeStatus = checkActivationCodeStatus($testCode);
$bindingTest = simulateBindingAPI($testCode);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复后激活码测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .highlight { background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0; }
        .warning-box { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; }
        .error-box { background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>🧪 修复后激活码测试</h1>
    <p><strong>测试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    <p><strong>测试激活码:</strong> <code><?php echo $testCode; ?></code></p>
    
    <div class="section">
        <h2>📊 当前激活码状态</h2>
        
        <?php if ($codeStatus): ?>
            <table>
                <tr><th>属性</th><th>值</th><th>状态</th></tr>
                <tr>
                    <td>激活码</td>
                    <td><code><?php echo htmlspecialchars($codeStatus['code']); ?></code></td>
                    <td>✅ 正常</td>
                </tr>
                <tr>
                    <td>is_used</td>
                    <td><?php echo $codeStatus['is_used'] ? '1 (已使用)' : '0 (未使用)'; ?></td>
                    <td><?php echo $codeStatus['is_used'] ? '❌' : '✅'; ?></td>
                </tr>
                <tr>
                    <td>bound_user_id</td>
                    <td><?php echo $codeStatus['bound_user_id'] ?: 'NULL (未绑定)'; ?></td>
                    <td><?php echo $codeStatus['bound_user_id'] ? '✅' : '✅'; ?></td>
                </tr>
                <tr>
                    <td>status</td>
                    <td><?php echo htmlspecialchars($codeStatus['status']); ?></td>
                    <td><?php echo $codeStatus['status'] === 'active' ? '✅' : '❌'; ?></td>
                </tr>
                <tr>
                    <td>创建时间</td>
                    <td><?php echo $codeStatus['created_at']; ?></td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>过期时间</td>
                    <td><?php echo $codeStatus['expires_at']; ?></td>
                    <td><?php echo strtotime($codeStatus['expires_at']) > time() ? '✅ 有效' : '❌ 过期'; ?></td>
                </tr>
                <tr>
                    <td>使用时间</td>
                    <td><?php echo $codeStatus['used_at'] ?: 'NULL (未使用)'; ?></td>
                    <td>✅</td>
                </tr>
            </table>
            
            <div class="<?php echo (!$codeStatus['is_used'] && !$codeStatus['bound_user_id'] && $codeStatus['status'] === 'active') ? 'highlight' : 'warning-box'; ?>">
                <h3>数据一致性检查:</h3>
                <?php if (!$codeStatus['is_used'] && !$codeStatus['bound_user_id'] && $codeStatus['status'] === 'active'): ?>
                    <p class="success">✅ 数据完全一致！激活码处于正常的未使用状态。</p>
                <?php elseif ($codeStatus['is_used'] && !$codeStatus['bound_user_id']): ?>
                    <p class="error">❌ 数据不一致：is_used=1 但 bound_user_id=NULL</p>
                <?php else: ?>
                    <p class="info">ℹ️ 激活码已被正常使用</p>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="error-box">
                <p class="error">❌ 无法获取激活码状态</p>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="section">
        <h2>🔧 绑定API测试</h2>
        
        <div class="<?php echo $bindingTest['success'] ? 'highlight' : 'error-box'; ?>">
            <h3><?php echo $bindingTest['success'] ? '✅ 测试通过' : '❌ 测试结果'; ?></h3>
            <p><strong>API响应:</strong> <?php echo htmlspecialchars($bindingTest['message']); ?></p>
        </div>
        
        <h3>测试说明:</h3>
        <ul>
            <li><strong>修复前:</strong> 数据不一致时会错误提示"激活码已被其他用户使用"</li>
            <li><strong>修复后:</strong> 数据不一致时会提示"激活码状态异常，请联系管理员或稍后重试"</li>
            <li><strong>正常情况:</strong> 未使用的激活码会提示"激活码可以正常绑定！"</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>📋 修复效果总结</h2>
        
        <?php 
        $isFixed = !$codeStatus['is_used'] && !$codeStatus['bound_user_id'] && $codeStatus['status'] === 'active';
        $canBind = $bindingTest['success'];
        ?>
        
        <div class="<?php echo ($isFixed && $canBind) ? 'highlight' : 'warning-box'; ?>">
            <h3><?php echo ($isFixed && $canBind) ? '🎉 修复完全成功！' : '⚠️ 需要进一步检查'; ?></h3>
            
            <table>
                <tr><th>检查项</th><th>状态</th><th>说明</th></tr>
                <tr>
                    <td>数据一致性</td>
                    <td><?php echo $isFixed ? '✅ 正常' : '❌ 异常'; ?></td>
                    <td><?php echo $isFixed ? '激活码状态完全正常' : '仍存在数据不一致'; ?></td>
                </tr>
                <tr>
                    <td>绑定功能</td>
                    <td><?php echo $canBind ? '✅ 正常' : '❌ 异常'; ?></td>
                    <td><?php echo $canBind ? '可以正常绑定' : '绑定时会出错'; ?></td>
                </tr>
                <tr>
                    <td>错误提示</td>
                    <td><?php echo ($bindingTest['message'] !== '激活码已被其他用户使用') ? '✅ 改善' : '❌ 未改善'; ?></td>
                    <td>不再显示误导性错误信息</td>
                </tr>
            </table>
        </div>
        
        <?php if ($isFixed && $canBind): ?>
            <div class="highlight">
                <h4>🎯 修复成功确认:</h4>
                <ul>
                    <li>✅ 激活码 <code><?php echo $testCode; ?></code> 已完全修复</li>
                    <li>✅ 数据状态恢复一致性 (is_used=0, bound_user_id=NULL, status=active)</li>
                    <li>✅ 绑定API可以正常工作</li>
                    <li>✅ 用户可以正常绑定此激活码</li>
                </ul>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="section">
        <h2>🔗 相关工具</h2>
        <ul>
            <li><a href="activation-code-diagnosis.php">激活码状态诊断</a></li>
            <li><a href="fix-activation-code-inconsistency.php">数据不一致修复工具</a></li>
            <li><a href="../new-lock.html">用户登录页面</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
