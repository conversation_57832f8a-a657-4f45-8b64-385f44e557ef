<?php
/**
 * 付费解锁系统 - 统计信息API
 * 获取系统使用统计数据
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只允许GET请求']);
    exit();
}

// 数据库配置
$dbPath = '../server/user_system.db3';

/**
 * 获取数据库连接
 */
function getDbConnection($dbPath) {
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        error_log("数据库连接失败: " . $e->getMessage());
        return null;
    }
}

try {
    // 连接数据库
    $pdo = getDbConnection($dbPath);
    if (!$pdo) {
        throw new Exception('数据库连接失败');
    }
    
    // 统计激活码数量
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM activation_codes");
    $totalCodes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as used FROM activation_codes WHERE is_used = 1");
    $usedCodes = $stmt->fetch(PDO::FETCH_ASSOC)['used'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as active FROM activation_codes WHERE status = 'active' AND expires_at > datetime('now')");
    $activeCodes = $stmt->fetch(PDO::FETCH_ASSOC)['active'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as expired FROM activation_codes WHERE status = 'expired' OR expires_at <= datetime('now')");
    $expiredCodes = $stmt->fetch(PDO::FETCH_ASSOC)['expired'];
    
    // 统计用户数量
    $stmt = $pdo->query("SELECT COUNT(*) as users FROM user_records");
    $totalUsers = $stmt->fetch(PDO::FETCH_ASSOC)['users'];
    
    // 统计今日新增用户
    $stmt = $pdo->query("SELECT COUNT(*) as today FROM user_records WHERE date(unlock_time) = date('now')");
    $todayUsers = $stmt->fetch(PDO::FETCH_ASSOC)['today'];
    
    // 获取最近的激活记录
    $stmt = $pdo->query("
        SELECT 
            ur.activation_code,
            ur.device_info,
            ur.unlock_time,
            ur.access_count,
            ac.expires_at
        FROM user_records ur
        LEFT JOIN activation_codes ac ON ur.activation_code = ac.code
        ORDER BY ur.unlock_time DESC
        LIMIT 10
    ");
    $recentActivations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 统计设备类型分布
    $stmt = $pdo->query("
        SELECT 
            CASE 
                WHEN device_info LIKE '%Android%' THEN 'Android'
                WHEN device_info LIKE '%Windows%' THEN 'Windows'
                WHEN device_info LIKE '%Mac%' THEN 'Mac'
                WHEN device_info LIKE '%iPhone%' OR device_info LIKE '%iPad%' THEN 'iOS'
                ELSE 'Other'
            END as device_type,
            COUNT(*) as count
        FROM user_records
        GROUP BY device_type
        ORDER BY count DESC
    ");
    $deviceStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 返回统计数据
    echo json_encode([
        'success' => true,
        'data' => [
            'codes' => [
                'total' => (int)$totalCodes,
                'used' => (int)$usedCodes,
                'active' => (int)$activeCodes,
                'expired' => (int)$expiredCodes
            ],
            'users' => [
                'total' => (int)$totalUsers,
                'today' => (int)$todayUsers
            ],
            'recentActivations' => $recentActivations,
            'deviceStats' => $deviceStats,
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ]);
    
} catch (Exception $e) {
    error_log("获取统计信息失败: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '获取统计信息失败: ' . $e->getMessage()
    ]);
}
?>
