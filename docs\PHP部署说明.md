# 用户登录系统 - PHP版本部署说明

## 📋 系统概述

本系统是基于PHP的用户登录系统，支持用户注册、登录、密码找回等功能，使用SQLite数据库存储。

## 🚀 快速部署

### 1. 环境要求

- **PHP 7.0+** (推荐PHP 7.4+)
- **Web服务器** (Apache/Nginx/IIS)
- **SQLite3扩展** (PHP内置)
- **PDO扩展** (PHP内置)

### 2. 文件结构

```
用户登录系统/
├── api/                       # PHP API接口
│   ├── login.php              # 用户登录API
│   ├── register.php           # 用户注册API
│   ├── forgot-password.php    # 密码找回API
│   ├── reset-password.php     # 密码重置API
│   ├── verify-session.php     # 会话验证API
│   ├── captcha.php            # 验证码生成API
│   └── stats.php              # 统计信息API
├── server/                    # 数据库和工具
│   ├── user_system.db3        # SQLite数据库文件
│   ├── generate_activation_code.py # 激活码生成工具(Python)
│   └── .htaccess              # 目录保护文件
├── js/                        # 前端JavaScript
│   ├── new-auth-system.js     # 认证系统脚本
│   └── new-session-check.js   # 会话检查脚本
├── css/                       # 样式文件
├── images/                    # 图片资源
├── test/                      # 测试文件
├── docs/                      # 文档
├── new-lock.html              # 登录页面
├── reset-password.html        # 密码重置页面
└── index.php                  # 主页面
```

### 3. 部署步骤

#### 步骤1: 初始化数据库
```bash
# 命令行方式
php admin/init-database.php

# 或通过Web访问
http://your-domain.com/admin/init-database.php
```

#### 步骤2: 生成激活码
```bash
# 命令行方式（推荐）
php admin/generate-codes.php

# Web方式生成单个激活码
http://your-domain.com/admin/generate-codes.php?action=generate&count=1&days=10

# Web方式批量生成
http://your-domain.com/admin/generate-codes.php?action=generate&count=5&days=10
```

#### 步骤3: 配置Web服务器
确保Web服务器可以访问所有文件，特别是：
- `api/` 目录下的PHP文件
- `server/` 目录需要写权限（用于SQLite数据库）

#### 步骤4: 测试系统
访问测试页面：`http://your-domain.com/test/test-unlock-system.html`

## 🔧 API接口说明

### 1. 激活码验证API
**接口**: `POST api/validate-activation.php`

**请求参数**:
```json
{
    "activationCode": "XXXXX-XXXXX-XXXXX-XXXXX",
    "deviceFingerprint": "设备指纹哈希",
    "deviceInfo": "设备信息字符串",
    "userAgent": "浏览器用户代理",
    "screenResolution": "1920x1080"
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "激活成功！欢迎使用",
    "data": {
        "expiresAt": 1627776000000,
        "isReturningUser": false
    }
}
```

### 2. 统计信息API
**接口**: `GET api/stats.php`

**响应示例**:
```json
{
    "success": true,
    "data": {
        "codes": {
            "total": 10,
            "used": 3,
            "active": 7,
            "expired": 0
        },
        "users": {
            "total": 3,
            "today": 1
        },
        "recentActivations": [...],
        "deviceStats": [...]
    }
}
```

## 🛠️ 管理工具

### 1. 激活码生成器

**命令行使用**:
```bash
php admin/generate-codes.php
```

**Web API使用**:
```bash
# 生成1个激活码，有效期10天
GET admin/generate-codes.php?action=generate&count=1&days=10

# 查看所有有效激活码
GET admin/generate-codes.php?action=list
```

### 2. 数据库管理

**查看数据库**:
```bash
# 使用SQLite命令行工具
sqlite3 server/user_system.db3

# 查看所有表
.tables

# 查看用户
SELECT * FROM users;

# 查看激活码
SELECT * FROM activation_codes;

# 查看会话
SELECT * FROM user_sessions;
```

**重新初始化数据库**:
```bash
php admin/init-database.php
```

## 🔒 安全配置

### 1. 目录权限
```bash
# 设置适当的文件权限
chmod 755 api/
chmod 755 admin/
chmod 644 api/*.php
chmod 644 admin/*.php

# 数据库目录需要写权限
chmod 755 server/
chmod 666 server/user_system.db3
```

### 2. Web服务器配置

**Apache (.htaccess)**:
```apache
# 保护数据库文件
<Files "*.db">
    Deny from all
</Files>

# 保护敏感目录
<Directory "server">
    Deny from all
</Directory>
```

**Nginx**:
```nginx
# 禁止访问数据库文件
location ~* \.db$ {
    deny all;
}

# 禁止访问server目录
location /server/ {
    deny all;
}
```

## 📱 使用流程

### 用户端流程
1. 访问主页面 → 自动跳转到锁屏页面
2. 扫描支付宝二维码付款19.9元
3. 扫描QQ二维码联系客服
4. 发送付款截图获取激活码
5. 输入激活码解锁访问

### 管理员流程
1. **生成激活码**: `php admin/generate-codes.php`
2. **查看统计**: 访问 `api/stats.php`
3. **管理数据库**: 使用SQLite工具或重新初始化

## 🛠️ 故障排除

### 常见问题

1. **"数据库连接失败"**
   - 检查PHP是否启用SQLite扩展
   - 确认server目录有写权限
   - 运行数据库初始化脚本

2. **"激活码验证失败"**
   - 检查API路径是否正确
   - 确认Web服务器可以执行PHP
   - 查看PHP错误日志

3. **"网络错误"**
   - 确认Web服务器正在运行
   - 检查防火墙设置
   - 验证API接口可访问性

### 调试方法

1. **启用PHP错误显示**:
```php
ini_set('display_errors', 1);
error_reporting(E_ALL);
```

2. **查看错误日志**:
```bash
# 查看PHP错误日志
tail -f /var/log/php_errors.log

# 或查看Web服务器错误日志
tail -f /var/log/apache2/error.log
```

3. **测试API接口**:
```bash
# 使用curl测试
curl -X POST http://your-domain.com/api/validate-activation.php \
  -H "Content-Type: application/json" \
  -d '{"activationCode":"TEST1-TEST2-TEST3-TEST4","deviceFingerprint":"test123"}'
```

## 📞 技术支持

- 📝 [开发记录](records.md) - 详细的开发历史
- 🧪 [测试页面](../test/test-unlock-system.html) - 系统功能测试
- 💾 数据库工具: SQLite Browser 或命令行工具

---

**🎉 PHP版本部署完成，无需Python环境！** 🔐💰✨
