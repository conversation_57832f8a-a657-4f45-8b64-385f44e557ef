<?php
/**
 * 基于工作项目的邮件发送器
 * 从 test_email_system 项目移植的可工作的邮件发送代码
 */

// 引入PHPMailer (使用本项目的vendor)
$vendorPath = '../vendor/autoload.php';
if (!file_exists($vendorPath)) {
    throw new Exception("PHPMailer依赖文件不存在: $vendorPath");
}
require_once $vendorPath;

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class WorkingEmailSender
{
    private $mail;
    private $config;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        // 使用工作项目的配置
        $this->config = [
            'smtp_host' => 'smtp.163.com',
            'smtp_port' => 465,
            'smtp_secure' => 'ssl',
            'smtp_username' => '<EMAIL>',
            'smtp_password' => 'BJXfKJw32HgDSMSg',
            'from_email' => '<EMAIL>',
            'from_name' => '倒计时系统',
            'debug' => 0  // 生产环境关闭调试
        ];
        
        $this->mail = new PHPMailer(true);
        $this->setupSMTP();
    }
    
    /**
     * 设置SMTP配置
     */
    private function setupSMTP()
    {
        try {
            // 服务器设置
            $this->mail->isSMTP();
            $this->mail->Host = $this->config['smtp_host'];
            $this->mail->SMTPAuth = true;
            $this->mail->Username = $this->config['smtp_username'];
            $this->mail->Password = $this->config['smtp_password'];
            $this->mail->SMTPSecure = $this->config['smtp_secure'];
            $this->mail->Port = $this->config['smtp_port'];
            
            // 字符集设置
            $this->mail->CharSet = 'UTF-8';
            
            // 调试设置
            $this->mail->SMTPDebug = $this->config['debug'];
            
        } catch (Exception $e) {
            throw new Exception("SMTP配置失败: {$this->mail->ErrorInfo}");
        }
    }
    
    /**
     * 发送邮件 (兼容原有接口)
     * @param string $to 收件人邮箱
     * @param string $subject 邮件主题
     * @param string $body 邮件内容
     * @param bool $isHtml 是否HTML格式
     * @return array 返回格式与原EmailSender兼容
     */
    public function sendEmail($to, $subject, $body, $isHtml = true)
    {
        try {
            // 发件人设置
            $this->mail->setFrom($this->config['from_email'], $this->config['from_name']);
            
            // 收件人设置
            $this->mail->addAddress($to);
            
            // 邮件内容设置
            $this->mail->isHTML($isHtml);
            $this->mail->Subject = $subject;
            $this->mail->Body = $body;
            
            // 发送邮件
            $result = $this->mail->send();
            
            // 清理收件人，为下次发送做准备
            $this->mail->clearAddresses();
            
            return [
                'success' => true,
                'message' => '邮件发送成功'
            ];
            
        } catch (Exception $e) {
            // 清理收件人
            $this->mail->clearAddresses();
            
            return [
                'success' => false,
                'message' => "邮件发送失败: {$this->mail->ErrorInfo}",
                'debug' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 发送密码重置邮件 (专用方法)
     * @param string $to 收件人邮箱
     * @param string $username 用户名
     * @param string $token 重置令牌
     * @return array 发送结果
     */
    public function sendPasswordResetEmail($to, $username, $token)
    {
        // 构建重置链接
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $resetLink = $protocol . "://" . $_SERVER['HTTP_HOST'] . "/reset-password.html?token=" . $token;
        
        $subject = "密码重置 - 倒计时系统";
        
        $body = "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>密码重置</title>
        </head>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h2 style='color: #4CAF50;'>🔐 密码重置请求</h2>
                
                <p>亲爱的 <strong>{$username}</strong>，</p>
                
                <p>我们收到了您的密码重置请求。请点击下面的链接来重置您的密码：</p>
                
                <div style='text-align: center; margin: 30px 0;'>
                    <a href='{$resetLink}' 
                       style='background-color: #4CAF50; color: white; padding: 12px 30px; 
                              text-decoration: none; border-radius: 5px; display: inline-block;'>
                        重置密码
                    </a>
                </div>
                
                <p>或者复制以下链接到浏览器地址栏：</p>
                <p style='background-color: #f5f5f5; padding: 10px; border-radius: 3px; word-break: break-all;'>
                    {$resetLink}
                </p>
                
                <div style='margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;'>
                    <p style='color: #666; font-size: 14px;'>
                        <strong>安全提示：</strong><br>
                        • 此链接将在24小时后失效<br>
                        • 如果您没有请求密码重置，请忽略此邮件<br>
                        • 请勿将此链接分享给他人
                    </p>
                </div>
                
                <div style='margin-top: 20px; text-align: center; color: #999; font-size: 12px;'>
                    <p>此邮件由倒计时系统自动发送，请勿回复。</p>
                    <p>发送时间：" . date('Y-m-d H:i:s') . "</p>
                </div>
            </div>
        </body>
        </html>
        ";
        
        return $this->sendEmail($to, $subject, $body, true);
    }
    
    /**
     * 获取最后的错误信息
     * @return string 错误信息
     */
    public function getLastError()
    {
        return $this->mail->ErrorInfo;
    }
    
    /**
     * 设置调试模式
     * @param int $level 调试级别 (0=关闭, 1=客户端, 2=客户端和服务器)
     */
    public function setDebugLevel($level)
    {
        $this->mail->SMTPDebug = $level;
    }
    
    /**
     * 测试SMTP连接
     * @return array 测试结果
     */
    public function testConnection()
    {
        try {
            // 临时启用调试
            $originalDebug = $this->mail->SMTPDebug;
            $this->mail->SMTPDebug = 2;
            
            // 创建测试邮件
            $this->mail->setFrom($this->config['from_email'], $this->config['from_name']);
            $this->mail->addAddress($this->config['from_email']); // 发给自己
            $this->mail->isHTML(true);
            $this->mail->Subject = 'SMTP连接测试';
            $this->mail->Body = '这是一封SMTP连接测试邮件，发送时间：' . date('Y-m-d H:i:s');
            
            // 尝试发送
            $result = $this->mail->send();
            
            // 恢复调试设置
            $this->mail->SMTPDebug = $originalDebug;
            $this->mail->clearAddresses();
            
            return [
                'success' => true,
                'message' => 'SMTP连接测试成功'
            ];
            
        } catch (Exception $e) {
            // 恢复调试设置
            $this->mail->SMTPDebug = $originalDebug;
            $this->mail->clearAddresses();
            
            return [
                'success' => false,
                'message' => "SMTP连接测试失败: {$this->mail->ErrorInfo}",
                'debug' => $e->getMessage()
            ];
        }
    }
}

/**
 * 快速发送邮件函数 (兼容原有代码)
 */
function sendWorkingEmail($to, $subject, $body, $isHtml = true) {
    $sender = new WorkingEmailSender();
    return $sender->sendEmail($to, $subject, $body, $isHtml);
}
?>
