<?php
/**
 * 自动清理助手
 * 在登录过程中自动触发清理，保持数据库性能
 */

/**
 * 检查是否需要清理login_logs
 */
function shouldCleanupLoginLogs($dbPath) {
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 获取记录总数
        $stmt = $pdo->query("SELECT COUNT(*) FROM login_logs");
        $totalRecords = $stmt->fetchColumn();
        
        // 检查最后清理时间（从系统配置表）
        $stmt = $pdo->prepare("SELECT config_value FROM system_config WHERE config_key = 'last_cleanup_time'");
        $stmt->execute();
        $lastCleanup = $stmt->fetchColumn();
        
        $needsCleanup = false;
        $reason = '';
        
        // 条件1: 记录数超过阈值
        if ($totalRecords > 500) {
            $needsCleanup = true;
            $reason = "记录数超过阈值 ($totalRecords > 500)";
        }
        
        // 条件2: 距离上次清理超过7天
        if ($lastCleanup) {
            $daysSinceCleanup = (time() - strtotime($lastCleanup)) / (24 * 3600);
            if ($daysSinceCleanup > 7) {
                $needsCleanup = true;
                $reason = $reason ? $reason . ", 距离上次清理超过7天" : "距离上次清理超过7天";
            }
        } else {
            // 从未清理过
            $needsCleanup = true;
            $reason = $reason ? $reason . ", 从未执行过清理" : "从未执行过清理";
        }
        
        return [
            'needs_cleanup' => $needsCleanup,
            'reason' => $reason,
            'total_records' => $totalRecords,
            'last_cleanup' => $lastCleanup
        ];
        
    } catch (Exception $e) {
        error_log("检查清理需求失败: " . $e->getMessage());
        return ['needs_cleanup' => false, 'error' => $e->getMessage()];
    }
}

/**
 * 执行轻量级清理
 */
function performLightweightCleanup($dbPath) {
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $pdo->beginTransaction();
        
        $deletedCount = 0;
        
        // 删除30天前的失败登录记录
        $stmt = $pdo->prepare("
            DELETE FROM login_logs 
            WHERE login_time < datetime('now', '-30 days') 
            AND status IN ('failed', 'blocked')
        ");
        $stmt->execute();
        $deletedCount += $stmt->rowCount();
        
        // 如果记录数仍然很多，删除更多旧记录
        $stmt = $pdo->query("SELECT COUNT(*) FROM login_logs");
        $currentCount = $stmt->fetchColumn();
        
        if ($currentCount > 1000) {
            // 保留最新的800条记录，删除其余的
            $stmt = $pdo->prepare("
                DELETE FROM login_logs 
                WHERE id NOT IN (
                    SELECT id FROM login_logs 
                    ORDER BY login_time DESC 
                    LIMIT 800
                )
            ");
            $stmt->execute();
            $deletedCount += $stmt->rowCount();
        }
        
        // 更新最后清理时间
        $stmt = $pdo->prepare("
            INSERT OR REPLACE INTO system_config (config_key, config_value, description) 
            VALUES ('last_cleanup_time', ?, '最后一次login_logs清理时间')
        ");
        $stmt->execute([date('Y-m-d H:i:s')]);
        
        $pdo->commit();
        
        // 记录清理日志
        error_log("login_logs轻量级清理完成: 删除了 {$deletedCount} 条记录");
        
        return [
            'success' => true,
            'deleted_count' => $deletedCount,
            'cleanup_time' => date('Y-m-d H:i:s')
        ];
        
    } catch (Exception $e) {
        if (isset($pdo)) {
            $pdo->rollBack();
        }
        error_log("login_logs轻量级清理失败: " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * 在登录时自动检查并清理
 */
function autoCleanupOnLogin($dbPath) {
    // 随机触发清理（10%概率），避免每次登录都检查
    if (rand(1, 10) !== 1) {
        return null;
    }
    
    $checkResult = shouldCleanupLoginLogs($dbPath);
    
    if ($checkResult['needs_cleanup']) {
        return performLightweightCleanup($dbPath);
    }
    
    return null;
}

/**
 * 获取清理建议
 */
function getCleanupRecommendation($dbPath) {
    try {
        $pdo = new PDO("sqlite:$dbPath");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 获取统计信息
        $stmt = $pdo->query("SELECT COUNT(*) FROM login_logs");
        $totalRecords = $stmt->fetchColumn();
        
        $stmt = $pdo->query("
            SELECT COUNT(*) FROM login_logs 
            WHERE login_time < datetime('now', '-30 days')
        ");
        $oldRecords = $stmt->fetchColumn();
        
        $stmt = $pdo->query("
            SELECT COUNT(*) FROM login_logs 
            WHERE status IN ('failed', 'blocked')
        ");
        $failedRecords = $stmt->fetchColumn();
        
        $recommendations = [];
        
        if ($totalRecords > 1000) {
            $recommendations[] = [
                'level' => 'high',
                'message' => "记录数过多 ({$totalRecords} 条)，建议立即清理",
                'action' => 'immediate_cleanup'
            ];
        } elseif ($totalRecords > 500) {
            $recommendations[] = [
                'level' => 'medium',
                'message' => "记录数较多 ({$totalRecords} 条)，建议定期清理",
                'action' => 'schedule_cleanup'
            ];
        }
        
        if ($oldRecords > 100) {
            $recommendations[] = [
                'level' => 'medium',
                'message' => "有 {$oldRecords} 条超过30天的旧记录，可以清理",
                'action' => 'cleanup_old_records'
            ];
        }
        
        if ($failedRecords > 200) {
            $recommendations[] = [
                'level' => 'low',
                'message' => "有 {$failedRecords} 条失败登录记录，可以清理",
                'action' => 'cleanup_failed_records'
            ];
        }
        
        if (empty($recommendations)) {
            $recommendations[] = [
                'level' => 'info',
                'message' => "当前记录数 ({$totalRecords} 条) 在合理范围内",
                'action' => 'no_action_needed'
            ];
        }
        
        return [
            'total_records' => $totalRecords,
            'old_records' => $oldRecords,
            'failed_records' => $failedRecords,
            'recommendations' => $recommendations
        ];
        
    } catch (Exception $e) {
        return ['error' => $e->getMessage()];
    }
}
?>
