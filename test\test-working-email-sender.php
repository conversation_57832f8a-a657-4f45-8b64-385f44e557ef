<?php
/**
 * 测试工作的邮件发送器
 * 验证从test_email_system移植的邮件发送功能
 */

header('Content-Type: text/html; charset=utf-8');

$testResult = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_working_email'])) {
    $testEmail = $_POST['test_email'] ?? '';
    $testType = $_POST['test_type'] ?? 'simple';
    
    $testResult = testWorkingEmailSender($testEmail, $testType);
}

function testWorkingEmailSender($toEmail, $testType) {
    $result = [
        'start_time' => microtime(true),
        'success' => false,
        'error' => null,
        'steps' => []
    ];
    
    try {
        // 步骤1: 加载工作的邮件发送器
        $result['steps'][] = ['step' => '加载工作的邮件发送器', 'status' => 'start', 'time' => microtime(true)];
        require_once '../server/working-email-sender.php';
        $result['steps'][] = ['step' => '加载工作的邮件发送器', 'status' => 'success', 'time' => microtime(true)];
        
        // 步骤2: 创建邮件发送器实例
        $result['steps'][] = ['step' => '创建邮件发送器实例', 'status' => 'start', 'time' => microtime(true)];
        $emailSender = new WorkingEmailSender();
        $result['steps'][] = ['step' => '创建邮件发送器实例', 'status' => 'success', 'time' => microtime(true)];
        
        // 步骤3: 测试SMTP连接
        $result['steps'][] = ['step' => '测试SMTP连接', 'status' => 'start', 'time' => microtime(true)];
        $connectionTest = $emailSender->testConnection();
        $result['steps'][] = ['step' => '测试SMTP连接', 'status' => $connectionTest['success'] ? 'success' : 'error', 'time' => microtime(true), 'data' => $connectionTest];
        
        if (!$connectionTest['success']) {
            $result['error'] = $connectionTest['message'];
            return $result;
        }
        
        // 步骤4: 发送测试邮件
        $result['steps'][] = ['step' => '发送测试邮件', 'status' => 'start', 'time' => microtime(true)];
        
        if ($testType === 'password_reset') {
            // 测试密码重置邮件
            $sendResult = $emailSender->sendPasswordResetEmail($toEmail, 'testuser', 'test_token_123456');
        } else {
            // 测试简单邮件
            $subject = '测试邮件 - 工作的邮件发送器';
            $body = '<h2>测试邮件</h2><p>这是使用工作的邮件发送器发送的测试邮件。</p><p>发送时间：' . date('Y-m-d H:i:s') . '</p>';
            $sendResult = $emailSender->sendEmail($toEmail, $subject, $body, true);
        }
        
        $result['steps'][] = ['step' => '发送测试邮件', 'status' => $sendResult['success'] ? 'success' : 'error', 'time' => microtime(true), 'data' => $sendResult];
        
        $result['success'] = $sendResult['success'];
        if (!$sendResult['success']) {
            $result['error'] = $sendResult['message'];
        }
        
    } catch (Exception $e) {
        $result['error'] = $e->getMessage();
        $result['steps'][] = ['step' => '异常', 'status' => 'error', 'time' => microtime(true), 'error' => $e->getMessage()];
    }
    
    $result['end_time'] = microtime(true);
    $result['total_time'] = $result['end_time'] - $result['start_time'];
    
    return $result;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作的邮件发送器测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
        input, select, button { padding: 8px; margin: 5px; }
        button { background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
        .step { margin: 10px 0; padding: 10px; border-left: 4px solid #ddd; }
        .step.success { border-left-color: #4CAF50; background: #f0f8f0; }
        .step.error { border-left-color: #f44336; background: #fdf0f0; }
        .step.start { border-left-color: #2196F3; background: #f0f7ff; }
        .highlight { background: #ffffcc; padding: 15px; border-left: 4px solid #ffeb3b; margin: 15px 0; }
    </style>
</head>
<body>
    <h1>📧 工作的邮件发送器测试</h1>
    <p><strong>测试时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="highlight">
        <h3>🎯 关键改进</h3>
        <p><strong>使用来源:</strong> 从 <code>test_email_system</code> 项目移植的可工作的PHPMailer邮件发送器</p>
        <p><strong>主要优势:</strong></p>
        <ul>
            <li>✅ 使用成熟的PHPMailer库</li>
            <li>✅ 已验证可以正常发送邮件</li>
            <li>✅ 完整的错误处理机制</li>
            <li>✅ 兼容原有API接口</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>📊 PHPMailer依赖检查</h2>
        <?php
        $vendorPath = '../vendor/autoload.php';
        if (file_exists($vendorPath)) {
            echo "<p class='success'>✅ PHPMailer依赖文件存在: $vendorPath</p>";
            
            try {
                require_once $vendorPath;
                echo "<p class='success'>✅ PHPMailer自动加载成功</p>";
                
                if (class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
                    echo "<p class='success'>✅ PHPMailer类可用</p>";
                } else {
                    echo "<p class='error'>❌ PHPMailer类不可用</p>";
                }
            } catch (Exception $e) {
                echo "<p class='error'>❌ PHPMailer加载失败: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p class='error'>❌ PHPMailer依赖文件不存在: $vendorPath</p>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>🧪 邮件发送测试</h2>
        <form method="POST">
            <div>
                <label>收件人邮箱:</label><br>
                <input type="email" name="test_email" value="<?php echo $_POST['test_email'] ?? '<EMAIL>'; ?>" required style="width: 300px;">
                <small>建议先测试发件人自己的邮箱</small>
            </div>
            <div>
                <label>测试类型:</label><br>
                <select name="test_type">
                    <option value="simple" <?php echo ($_POST['test_type'] ?? '') === 'simple' ? 'selected' : ''; ?>>简单邮件测试</option>
                    <option value="password_reset" <?php echo ($_POST['test_type'] ?? '') === 'password_reset' ? 'selected' : ''; ?>>密码重置邮件测试</option>
                </select>
            </div>
            <div>
                <button type="submit" name="test_working_email">测试工作的邮件发送器</button>
            </div>
        </form>
    </div>
    
    <?php if ($testResult): ?>
    <div class="test-section">
        <h2>📋 测试结果</h2>
        
        <div class="step <?php echo $testResult['success'] ? 'success' : 'error'; ?>">
            <h3><?php echo $testResult['success'] ? '✅ 邮件发送成功' : '❌ 邮件发送失败'; ?></h3>
            <p><strong>总耗时:</strong> <?php echo number_format($testResult['total_time'], 2); ?> 秒</p>
            <?php if ($testResult['error']): ?>
                <p><strong>错误信息:</strong> <?php echo htmlspecialchars($testResult['error']); ?></p>
            <?php endif; ?>
        </div>
        
        <h3>详细步骤:</h3>
        <?php foreach ($testResult['steps'] as $step): ?>
            <div class="step <?php echo $step['status']; ?>">
                <h4><?php echo $step['step']; ?> - <?php echo ucfirst($step['status']); ?></h4>
                <?php if (isset($step['data'])): ?>
                    <pre><?php echo htmlspecialchars(json_encode($step['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
                <?php endif; ?>
                <?php if (isset($step['error'])): ?>
                    <p class="error">错误: <?php echo htmlspecialchars($step['error']); ?></p>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
        
        <?php if ($testResult['success']): ?>
        <div class="highlight">
            <h3>🎉 测试成功！</h3>
            <p>工作的邮件发送器运行正常，现在可以：</p>
            <ol>
                <li>测试密码找回功能</li>
                <li>检查邮箱是否收到邮件</li>
                <li>验证邮件内容和格式</li>
            </ol>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>
    
    <div class="test-section">
        <h2>🔧 技术说明</h2>
        
        <h3>移植的关键组件:</h3>
        <ul>
            <li><strong>PHPMailer库:</strong> 使用本项目 <code>vendor/</code> 目录中的PHPMailer</li>
            <li><strong>SMTP配置:</strong> 复制了工作项目中的163邮箱配置</li>
            <li><strong>邮件模板:</strong> 专门的密码重置邮件模板</li>
            <li><strong>错误处理:</strong> 完整的异常处理和错误报告</li>
        </ul>
        
        <h3>与原系统的兼容性:</h3>
        <ul>
            <li>✅ 保持了原有的 <code>sendEmail()</code> 接口</li>
            <li>✅ 返回格式与原 <code>EmailSender</code> 兼容</li>
            <li>✅ 添加了专用的 <code>sendPasswordResetEmail()</code> 方法</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🔗 相关链接</h2>
        <ul>
            <li><a href="test-forgot-password-fixed.php">测试修复后的密码找回功能</a></li>
            <li><a href="fix-550-error.php">550错误修复工具</a></li>
            <li><a href="../new-lock.html">返回登录页面测试</a></li>
        </ul>
    </div>
    
    <hr>
    <p><small>📅 生成时间: <?php echo date('Y-m-d H:i:s'); ?></small></p>
</body>
</html>
