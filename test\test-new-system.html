<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新系统测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .info {
            color: #0c5460;
            background: #d1ecf1;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .test-section {
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin: 20px 0;
        }
        
        .code-display {
            font-family: 'Courier New', monospace;
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 14px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <h1>🧪 新用户认证系统测试</h1>
    
    <div class="container">
        <h2>系统概述</h2>
        <div class="info">
            <strong>新系统特点:</strong><br>
            ✅ 用户名+密码+验证码登录<br>
            ✅ 单设备登录（新登录挤掉旧登录）<br>
            ✅ 激活码绑定到用户账号<br>
            ✅ 会话管理和状态检查<br>
            ✅ SQLite3 .db3格式数据库
        </div>
    </div>
    
    <div class="container">
        <h2>🔧 API测试</h2>
        
        <div class="test-section">
            <h3>1. 验证码测试</h3>
            <button onclick="testCaptcha()">测试验证码生成</button>
            <div id="captchaResult"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 用户注册测试</h3>
            <button onclick="testRegister()">测试用户注册</button>
            <div id="registerResult"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 用户登录测试</h3>
            <button onclick="testLogin()">测试用户登录</button>
            <div id="loginResult"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 激活码绑定测试</h3>
            <button onclick="testBindActivation()">测试激活码绑定</button>
            <div id="bindResult"></div>
        </div>
        
        <div class="test-section">
            <h3>5. 会话验证测试</h3>
            <button onclick="testSessionVerify()">测试会话验证</button>
            <div id="sessionResult"></div>
        </div>
    </div>
    
    <div class="container">
        <h2>📊 数据库状态</h2>
        <button onclick="checkDatabase()">检查数据库状态</button>
        <div id="dbResult"></div>
    </div>
    
    <div class="container">
        <h2>🎯 快速测试流程</h2>
        <button onclick="runFullTest()">运行完整测试流程</button>
        <div id="fullTestResult"></div>
    </div>
    
    <div class="container">
        <h2>🔗 快速链接</h2>
        <button onclick="window.location.href='../new-lock.html'">新登录页面</button>
        <button onclick="window.location.href='../index.php'">主页面</button>
        <button onclick="window.location.href='../server/user_system.db3'" download>下载数据库</button>
    </div>

    <script>
        let testSessionToken = null;
        
        async function testCaptcha() {
            const resultDiv = document.getElementById('captchaResult');
            resultDiv.innerHTML = '<div class="info">测试验证码生成...</div>';
            
            try {
                // 创建图片元素测试验证码
                const img = document.createElement('img');
                img.src = '../api/captcha.php?' + Date.now();
                img.style.border = '1px solid #ddd';
                img.style.borderRadius = '5px';
                img.onload = () => {
                    resultDiv.innerHTML = `
                        <div class="success">✅ 验证码生成成功</div>
                        <div>验证码图片:</div>
                        ${img.outerHTML}
                        <div style="margin-top: 10px;">
                            <button onclick="testCaptcha()">刷新验证码</button>
                        </div>
                    `;
                };
                img.onerror = () => {
                    resultDiv.innerHTML = '<div class="error">❌ 验证码生成失败</div>';
                };
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }
        
        async function testRegister() {
            const resultDiv = document.getElementById('registerResult');
            resultDiv.innerHTML = '<div class="info">测试用户注册...</div>';
            
            try {
                const testUser = {
                    username: 'testuser' + Date.now(),
                    password: 'test123456',
                    email: '<EMAIL>',
                    captcha: 'TEST' // 这里需要真实验证码
                };
                
                const response = await fetch('../api/register.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testUser)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ 注册测试成功</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ 注册失败: ${data.message}</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }
        
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = '<div class="info">测试用户登录...</div>';
            
            try {
                const loginData = {
                    username: 'testuser',
                    password: 'test123',
                    captcha: 'TEST', // 这里需要真实验证码
                    device_info: 'Test Device'
                };
                
                const response = await fetch('../api/login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(loginData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    testSessionToken = data.data.session_token;
                    resultDiv.innerHTML = `
                        <div class="success">✅ 登录测试成功</div>
                        <div class="code-display">会话令牌: ${testSessionToken}</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ 登录失败: ${data.message}</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }
        
        async function testBindActivation() {
            const resultDiv = document.getElementById('bindResult');
            
            if (!testSessionToken) {
                resultDiv.innerHTML = '<div class="error">❌ 请先进行登录测试获取会话令牌</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">测试激活码绑定...</div>';
            
            try {
                // 先获取一个可用的激活码
                const dbResponse = await fetch('check-db-status.php');
                const dbData = await dbResponse.json();
                
                let activationCode = '746VV-JVQ3X-M8XBU-U33HL'; // 使用示例激活码
                
                const bindData = {
                    activation_code: activationCode,
                    session_token: testSessionToken
                };
                
                const response = await fetch('../api/bind-activation.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(bindData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ 激活码绑定成功</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ 绑定失败: ${data.message}</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }
        
        async function testSessionVerify() {
            const resultDiv = document.getElementById('sessionResult');
            
            if (!testSessionToken) {
                resultDiv.innerHTML = '<div class="error">❌ 请先进行登录测试获取会话令牌</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">测试会话验证...</div>';
            
            try {
                const response = await fetch('../api/verify-session.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        session_token: testSessionToken
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ 会话验证成功</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ 会话验证失败: ${data.message}</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }
        
        async function checkDatabase() {
            const resultDiv = document.getElementById('dbResult');
            resultDiv.innerHTML = '<div class="info">检查数据库状态...</div>';
            
            try {
                // 这里需要创建一个检查数据库状态的API
                resultDiv.innerHTML = `
                    <div class="info">
                        📊 数据库信息:<br>
                        • 文件: server/user_system.db3<br>
                        • 格式: SQLite3 (.db3)<br>
                        • 表: users, activation_codes, user_sessions, login_logs, captcha_codes, payment_records, system_config<br>
                        • 测试用户: testuser / test123<br>
                        • 示例激活码: 746VV-JVQ3X-M8XBU-U33HL
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 检查失败: ${error.message}</div>`;
            }
        }
        
        async function runFullTest() {
            const resultDiv = document.getElementById('fullTestResult');
            resultDiv.innerHTML = '<div class="info">运行完整测试流程...</div>';
            
            let results = [];
            
            try {
                results.push('🚀 开始完整测试流程');
                results.push('1. ✅ 验证码生成 - 需要手动验证');
                results.push('2. ⚠️ 用户注册 - 需要真实验证码');
                results.push('3. ⚠️ 用户登录 - 需要真实验证码');
                results.push('4. ⚠️ 激活码绑定 - 需要有效会话');
                results.push('5. ⚠️ 会话验证 - 需要有效会话');
                results.push('');
                results.push('💡 建议测试步骤:');
                results.push('1. 访问 new-lock.html 进行真实注册/登录');
                results.push('2. 使用激活码: 746VV-JVQ3X-M8XBU-U33HL');
                results.push('3. 绑定成功后访问 index.php');
                
                resultDiv.innerHTML = `
                    <div class="info">
                        <pre>${results.join('\n')}</pre>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
            }
        }
        
        // 页面加载时自动检查数据库
        window.addEventListener('load', () => {
            checkDatabase();
        });
    </script>
</body>
</html>
