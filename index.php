<?php
// 多品牌倒计时网站主页面
session_start();

// 检查是否有品牌参数
$hasValidBrand = false;
$brandPath = null;

if (isset($_GET['brand'])) {
    // 从URL参数获取品牌
    $brandPath = $_GET['brand'];
    $hasValidBrand = true;
} elseif (isset($_SERVER['BRAND_PATH'])) {
    // 从环境变量获取品牌（兼容router.php）
    $brandPath = $_SERVER['BRAND_PATH'];
    $hasValidBrand = true;
}

// 如果没有品牌信息，重定向到品牌选择页面
if (!$hasValidBrand) {
    header('Location: brand-selector.html');
    exit;
}

// 设置环境变量供品牌处理器使用
$_SERVER['BRAND_PATH'] = $brandPath;

// 引入品牌处理器
require_once 'includes/brand-handler.php';

// 获取当前品牌信息
$currentBrand = $brandHandler->getCurrentBrand();
$brandPath = $brandHandler->getBrandPath();
$brandName = $brandHandler->getBrandName();

// 获取品牌相关的图片路径
$logoPath = $brandHandler->getLogoPath();
$carPath = $brandHandler->getCarImagePath();

// 设置倒计时目标日期 (2025年12月31日 24:00，即2026年1月1日 00:00:00)
$targetDate = '2026-01-01 00:00:00';

// 页面标题（固定为国家报废置换补贴）
$pageTitle = '国家报废置换补贴倒计时';
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <title>国家报废置换补贴倒计时</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Logo显示区域 -->
    <div class="logo-container">
        <?php if (file_exists($logoPath)): ?>
            <img src="<?php echo $logoPath; ?>" alt="Logo" class="logo">
        <?php endif; ?>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
        <h1 class="main-title">国家报废置换补贴 12.31 24:00</h1>
        
        <div class="countdown-container">
            <div class="countdown-item">
                <span class="countdown-number" id="days">00</span>
                <span class="countdown-label">天</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number" id="hours">00</span>
                <span class="countdown-label">小时</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number" id="minutes">00</span>
                <span class="countdown-label">分钟</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number" id="seconds">00</span>
                <span class="countdown-label">秒</span>
            </div>
            <div class="countdown-item">
                <span class="countdown-number" id="milliseconds">000</span>
                <span class="countdown-label">毫秒</span>
            </div>
        </div>

        <h2 class="warning-title">2026年1月1日起买车成本至高增加<span class="highlight-number">20000</span>元</h2>
    </div>

    <!-- 汽车图片区域 -->
    <div class="car-container">
        <?php if (file_exists($carPath)): ?>
            <img src="<?php echo $carPath; ?>" alt="汽车" class="car-image">
        <?php endif; ?>
    </div>

    <!-- 用户图标 -->
    <div class="user-icon-container">
        <div class="user-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
            </svg>
        </div>
        <div class="user-menu">
            <div class="user-info">
                <div class="username" id="display-username">加载中...</div>
                <div class="version-info">标准版本</div>
                <div class="activation-code" id="display-activation-code">加载中...</div>
                <div class="expiry-date" id="display-expiry-date">加载中...</div>
            </div>
            <button class="logout-btn">🚪 退出登录</button>
        </div>
    </div>

    <!-- 版本切换图标 -->
    <div class="version-toggle-container">
        <div class="version-toggle" title="<?php echo htmlspecialchars($brandHandler->getToggleVersionText()); ?>" onclick="window.location.href='<?php echo htmlspecialchars($brandHandler->getToggleVersionUrl()); ?>'">
            <?php echo $brandHandler->getToggleVersionText(); ?>
        </div>
    </div>

    <script>
        // 设置目标日期
        const targetDate = new Date('<?php echo $targetDate; ?>').getTime();

        function updateCountdown() {
            const now = new Date().getTime();
            const distance = targetDate - now;

            if (distance < 0) {
                document.getElementById('days').textContent = '00';
                document.getElementById('hours').textContent = '00';
                document.getElementById('minutes').textContent = '00';
                document.getElementById('seconds').textContent = '00';
                document.getElementById('milliseconds').textContent = '000';
                return;
            }

            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);
            const milliseconds = Math.floor(distance % 1000);

            document.getElementById('days').textContent = days.toString().padStart(2, '0');
            document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
            document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
            document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
            document.getElementById('milliseconds').textContent = milliseconds.toString().padStart(3, '0');
        }

        // 每10毫秒更新倒计时以显示毫秒变化
        setInterval(updateCountdown, 10);
        updateCountdown(); // 立即执行一次

        // 版本切换功能
        document.querySelector('.version-toggle').addEventListener('click', function() {
            // 设置手动切换标记
            sessionStorage.setItem('manual_version_switch', 'true');
            sessionStorage.setItem('device_redirect_done', 'true');

            // 使用PHP生成的切换URL
            const toggleUrl = this.getAttribute('onclick').match(/window\.location\.href='([^']+)'/)[1];
            window.location.href = toggleUrl;
        });

        // 退出登录功能
        document.querySelector('.logout-btn').addEventListener('click', function() {
            // 清除会话数据
            sessionStorage.clear();
            localStorage.clear();

            // 跳转到正确的登录页面
            window.location.href = 'new-lock.html';
        });

        // 获取并显示真实的用户信息
        async function loadUserInfo() {
            try {
                const sessionToken = localStorage.getItem('session_token') ||
                                   sessionStorage.getItem('session_token') ||
                                   getCookie('session_token');

                if (!sessionToken) {
                    console.log('未找到会话令牌');
                    return;
                }

                const response = await fetch('api/verify-session.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        session_token: sessionToken
                    })
                });

                const data = await response.json();

                if (data.success && data.data) {
                    // 更新用户名
                    const usernameElement = document.getElementById('display-username');
                    if (usernameElement) {
                        usernameElement.textContent = data.data.username || '未知用户';
                    }

                    // 更新激活码
                    const activationCodeElement = document.getElementById('display-activation-code');
                    if (activationCodeElement) {
                        activationCodeElement.textContent = data.data.activation_code || '未绑定激活码';
                    }

                    // 更新到期时间
                    const expiryDateElement = document.getElementById('display-expiry-date');
                    if (expiryDateElement) {
                        if (data.data.activation_expires) {
                            const expiryDate = new Date(data.data.activation_expires * 1000);
                            expiryDateElement.textContent = '到期时间: ' + expiryDate.toLocaleDateString('zh-CN');
                        } else {
                            expiryDateElement.textContent = '到期时间: 未设置';
                        }
                    }
                } else {
                    console.log('获取用户信息失败:', data.message);
                }
            } catch (error) {
                console.error('加载用户信息错误:', error);
            }
        }

        // 获取Cookie的辅助函数
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }

        // 页面加载完成后获取用户信息
        loadUserInfo();

    </script>

    <!-- 智能设备检测脚本 -->
    <script src="js/smart-device-detector.js"></script>

    <!-- 增强版会话检查脚本 -->
    <script src="js/enhanced-session-check.js"></script>
</body>
</html>
