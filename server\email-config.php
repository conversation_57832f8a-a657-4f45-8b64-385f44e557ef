<?php
/**
 * 邮件配置文件
 * 支持多种邮件服务商的SMTP配置
 */

// 邮件配置数组
$emailConfig = [
    // 当前使用的邮件服务商 (可选: 'qq', 'gmail', 'outlook', '163', 'aliyun', 'custom')
    // 由于163邮箱出现550错误，临时切换到QQ邮箱
    'provider' => 'qq', // 从163切换到qq邮箱解决550错误
    
    // QQ邮箱配置
    'qq' => [
        'smtp_host' => 'smtp.qq.com',
        'smtp_port' => 587,
        'smtp_secure' => 'tls', // 或 'ssl'
        'username' => '', // 您的QQ邮箱
        'password' => '', // QQ邮箱授权码(不是QQ密码)
        'from_email' => '', // 发件人邮箱(同username)
        'from_name' => '倒计时系统'
    ],
    
    // Gmail配置
    'gmail' => [
        'smtp_host' => 'smtp.gmail.com',
        'smtp_port' => 587,
        'smtp_secure' => 'tls',
        'username' => '', // 您的Gmail地址
        'password' => '', // Gmail应用专用密码
        'from_email' => '',
        'from_name' => '倒计时系统'
    ],

    // Outlook配置
    'outlook' => [
        'smtp_host' => 'smtp-mail.outlook.com',
        'smtp_port' => 587,
        'smtp_secure' => 'tls',
        'username' => '<EMAIL>', // 您的Outlook邮箱(@outlook.com, @hotmail.com等)
        'password' => 'Xthl147258.', // Microsoft账户密码(使用OAuth2/现代认证)
        'from_email' => '<EMAIL>',
        'from_name' => '倒计时系统'
    ],
    
    // 163邮箱配置
    '163' => [
        'smtp_host' => 'smtp.163.com',
        'smtp_port' => 465,
        'smtp_secure' => 'ssl', // 163邮箱465端口使用SSL加密
        'username' => '<EMAIL>', // 您的163邮箱地址
        'password' => 'BJXfKJw32HgDSMSg', // 163邮箱授权码（不是登录密码）
        'from_email' => '<EMAIL>',
        'from_name' => '倒计时系统'
    ],

    // 阿里云邮箱配置
    'aliyun' => [
        'smtp_host' => 'smtp.mxhichina.com',
        'smtp_port' => 25,
        'smtp_secure' => false,
        'username' => '',
        'password' => '',
        'from_email' => '',
        'from_name' => '倒计时系统'
    ],

    // 自定义SMTP配置
    'custom' => [
        'smtp_host' => '',
        'smtp_port' => 587,
        'smtp_secure' => 'tls',
        'username' => '',
        'password' => '',
        'from_email' => '',
        'from_name' => '倒计时系统'
    ]
];

/**
 * 获取当前邮件配置
 */
function getEmailConfig() {
    global $emailConfig;

    // 如果全局变量不存在，重新定义配置
    if (!isset($emailConfig) || !is_array($emailConfig)) {
        $emailConfig = [
            'provider' => '163',
            '163' => [
                'smtp_host' => 'smtp.163.com',
                'smtp_port' => 465,
                'smtp_secure' => 'ssl',
                'username' => '<EMAIL>',
                'password' => 'BJXfKJw32HgDSMSg',
                'from_email' => '<EMAIL>',
                'from_name' => '倒计时系统'
            ]
        ];
    }

    $provider = $emailConfig['provider'] ?? '163';
    if (empty($provider)) {
        $provider = '163'; // 默认使用163邮箱
        $emailConfig['provider'] = $provider;
    }

    if (!isset($emailConfig[$provider])) {
        throw new Exception("邮件服务商配置不存在: $provider");
    }

    $config = $emailConfig[$provider];

    // 验证必需的配置项
    $required = ['smtp_host', 'smtp_port', 'username', 'password', 'from_email'];
    foreach ($required as $field) {
        if (empty($config[$field])) {
            throw new Exception("邮件配置缺少必需字段: $field");
        }
    }

    return $config;
}

/**
 * 验证邮件配置是否完整
 */
function validateEmailConfig() {
    try {
        $config = getEmailConfig();
        return [
            'valid' => true,
            'provider' => $GLOBALS['emailConfig']['provider'],
            'smtp_host' => $config['smtp_host'],
            'from_email' => $config['from_email']
        ];
    } catch (Exception $e) {
        return [
            'valid' => false,
            'error' => $e->getMessage()
        ];
    }
}

// 使用说明
/*
配置步骤：

1. QQ邮箱配置：
   - 登录QQ邮箱 -> 设置 -> 账户
   - 开启IMAP/SMTP服务
   - 获取授权码(不是QQ密码)
   - 填入username和password字段

2. Gmail配置：
   - 开启两步验证
   - 生成应用专用密码
   - 填入username和password字段

3. Outlook配置：
   - 直接使用Microsoft账户密码
   - 系统使用OAuth2/现代认证
   - 无需额外的应用专用密码
   - 填入username和password字段

4. 163邮箱配置：
   - 登录163邮箱 -> 设置 -> POP3/SMTP/IMAP
   - 开启SMTP服务
   - 获取授权码
   - 填入username和password字段

5. 修改provider字段选择要使用的邮件服务商

示例配置(QQ邮箱)：
$emailConfig['provider'] = 'qq';
$emailConfig['qq']['username'] = '<EMAIL>';
$emailConfig['qq']['password'] = 'your-auth-code';
$emailConfig['qq']['from_email'] = '<EMAIL>';

示例配置(Outlook邮箱)：
$emailConfig['provider'] = 'outlook';
$emailConfig['outlook']['username'] = '<EMAIL>';
$emailConfig['outlook']['password'] = 'your-microsoft-password';
$emailConfig['outlook']['from_email'] = '<EMAIL>';
*/
?>
