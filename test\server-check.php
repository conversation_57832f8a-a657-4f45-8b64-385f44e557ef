<?php
/**
 * 服务器环境检查工具
 * 检查PHP配置、扩展和权限
 */

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器环境检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
        }
        .check-item {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #ddd;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        .code {
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 14px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 服务器环境检查</h1>
        
        <?php
        // 基本信息
        echo "<h2>📋 基本信息</h2>";
        echo "<table>";
        echo "<tr><th>项目</th><th>值</th></tr>";
        echo "<tr><td>PHP版本</td><td>" . PHP_VERSION . "</td></tr>";
        echo "<tr><td>服务器软件</td><td>" . ($_SERVER['SERVER_SOFTWARE'] ?? '未知') . "</td></tr>";
        echo "<tr><td>操作系统</td><td>" . PHP_OS . "</td></tr>";
        echo "<tr><td>PHP SAPI</td><td>" . php_sapi_name() . "</td></tr>";
        echo "<tr><td>文档根目录</td><td>" . ($_SERVER['DOCUMENT_ROOT'] ?? '未知') . "</td></tr>";
        echo "<tr><td>当前脚本路径</td><td>" . __FILE__ . "</td></tr>";
        echo "<tr><td>检查时间</td><td>" . date('Y-m-d H:i:s') . "</td></tr>";
        echo "</table>";
        
        // GD扩展检查
        echo "<h2>🎨 GD扩展检查</h2>";
        if (extension_loaded('gd')) {
            echo "<div class='success check-item'>✅ GD扩展已加载</div>";
            
            $gdInfo = gd_info();
            echo "<table>";
            echo "<tr><th>功能</th><th>状态</th></tr>";
            foreach ($gdInfo as $key => $value) {
                $status = is_bool($value) ? ($value ? '✅ 支持' : '❌ 不支持') : $value;
                echo "<tr><td>$key</td><td>$status</td></tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='error check-item'>❌ GD扩展未加载</div>";
        }
        
        // 重要扩展检查
        echo "<h2>🔧 重要扩展检查</h2>";
        $extensions = [
            'gd' => 'GD图像处理',
            'pdo' => 'PDO数据库',
            'pdo_sqlite' => 'SQLite数据库',
            'session' => '会话管理',
            'json' => 'JSON处理',
            'mbstring' => '多字节字符串',
            'openssl' => 'SSL加密',
            'curl' => 'HTTP客户端'
        ];
        
        echo "<table>";
        echo "<tr><th>扩展</th><th>描述</th><th>状态</th></tr>";
        foreach ($extensions as $ext => $desc) {
            $loaded = extension_loaded($ext);
            $status = $loaded ? '<span style="color: green;">✅ 已加载</span>' : '<span style="color: red;">❌ 未加载</span>';
            echo "<tr><td><code>$ext</code></td><td>$desc</td><td>$status</td></tr>";
        }
        echo "</table>";
        
        // PHP配置检查
        echo "<h2>⚙️ PHP配置检查</h2>";
        $configs = [
            'display_errors' => '显示错误',
            'log_errors' => '记录错误',
            'file_uploads' => '文件上传',
            'session.auto_start' => '自动启动会话',
            'allow_url_fopen' => '允许URL文件操作',
            'memory_limit' => '内存限制',
            'max_execution_time' => '最大执行时间',
            'upload_max_filesize' => '最大上传文件大小',
            'post_max_size' => 'POST最大大小'
        ];
        
        echo "<table>";
        echo "<tr><th>配置项</th><th>描述</th><th>当前值</th></tr>";
        foreach ($configs as $config => $desc) {
            $value = ini_get($config);
            if ($value === false) {
                $value = '未设置';
            } elseif ($value === '') {
                $value = '空';
            } elseif ($value === '1') {
                $value = '开启';
            } elseif ($value === '0') {
                $value = '关闭';
            }
            echo "<tr><td><code>$config</code></td><td>$desc</td><td>$value</td></tr>";
        }
        echo "</table>";
        
        // 文件权限检查
        echo "<h2>📁 文件权限检查</h2>";
        $paths = [
            '../' => '项目根目录',
            '../api/' => 'API目录',
            '../server/' => '服务器目录',
            '../test/' => '测试目录'
        ];
        
        echo "<table>";
        echo "<tr><th>路径</th><th>描述</th><th>可读</th><th>可写</th><th>权限</th></tr>";
        foreach ($paths as $path => $desc) {
            if (file_exists($path)) {
                $readable = is_readable($path) ? '✅' : '❌';
                $writable = is_writable($path) ? '✅' : '❌';
                $perms = substr(sprintf('%o', fileperms($path)), -4);
                echo "<tr><td><code>$path</code></td><td>$desc</td><td>$readable</td><td>$writable</td><td>$perms</td></tr>";
            } else {
                echo "<tr><td><code>$path</code></td><td>$desc</td><td colspan='3'>❌ 不存在</td></tr>";
            }
        }
        echo "</table>";
        
        // 数据库检查
        echo "<h2>🗄️ 数据库检查</h2>";
        $dbPath = '../server/user_system.db3';
        if (file_exists($dbPath)) {
            echo "<div class='success check-item'>✅ 数据库文件存在: $dbPath</div>";
            
            try {
                $pdo = new PDO("sqlite:$dbPath");
                echo "<div class='success check-item'>✅ 数据库连接成功</div>";
                
                // 检查表
                $tables = ['users', 'captcha_codes'];
                foreach ($tables as $table) {
                    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='$table'");
                    if ($stmt->fetch()) {
                        echo "<div class='success check-item'>✅ 表 '$table' 存在</div>";
                    } else {
                        echo "<div class='error check-item'>❌ 表 '$table' 不存在</div>";
                    }
                }
                
            } catch (Exception $e) {
                echo "<div class='error check-item'>❌ 数据库连接失败: " . $e->getMessage() . "</div>";
            }
        } else {
            echo "<div class='error check-item'>❌ 数据库文件不存在: $dbPath</div>";
        }
        
        // 验证码测试
        echo "<h2>🖼️ 验证码测试</h2>";
        echo "<div class='info check-item'>";
        echo "<strong>测试不同的验证码方案:</strong><br>";
        echo "<a href='../api/captcha.php' target='_blank' class='btn'>原始验证码</a>";
        echo "<a href='../api/captcha-fallback.php' target='_blank' class='btn'>备用验证码</a>";
        echo "<a href='../api/captcha-fallback.php?format=svg' target='_blank' class='btn'>SVG验证码</a>";
        echo "<a href='../api/captcha-fallback.php?format=text' target='_blank' class='btn'>文本验证码</a>";
        echo "</div>";
        
        // 解决方案建议
        echo "<h2>💡 解决方案建议</h2>";
        
        if (!extension_loaded('gd')) {
            echo "<div class='warning check-item'>";
            echo "<strong>GD扩展未加载的解决方案:</strong><br>";
            echo "1. 编辑 php.ini 文件，取消注释或添加: <code>extension=gd</code><br>";
            echo "2. 重启Web服务器 (Apache/Nginx)<br>";
            echo "3. 如果是Linux系统，安装GD扩展: <code>sudo apt-get install php-gd</code><br>";
            echo "4. 如果是共享主机，联系主机商启用GD扩展<br>";
            echo "5. 使用备用验证码方案 (SVG格式)";
            echo "</div>";
        }
        
        echo "<div class='info check-item'>";
        echo "<strong>其他建议:</strong><br>";
        echo "• 确保所有必要的PHP扩展都已安装<br>";
        echo "• 检查文件和目录权限<br>";
        echo "• 定期更新PHP版本<br>";
        echo "• 启用错误日志以便调试";
        echo "</div>";
        ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="javascript:location.reload()" class="btn">🔄 重新检查</a>
            <a href="check-gd-extension.php" class="btn">🎨 详细GD检查</a>
            <a href="<?php echo $_SERVER['PHP_SELF']; ?>?phpinfo=1" class="btn">📋 查看phpinfo</a>
        </div>
        
        <?php
        // 显示phpinfo (如果请求)
        if (isset($_GET['phpinfo'])) {
            echo "<h2>📋 PHP信息</h2>";
            echo "<div style='background: white; padding: 20px; border-radius: 5px; overflow: auto;'>";
            ob_start();
            phpinfo();
            $phpinfo = ob_get_clean();
            
            // 提取body内容
            preg_match('/<body[^>]*>(.*?)<\/body>/is', $phpinfo, $matches);
            if (isset($matches[1])) {
                echo $matches[1];
            } else {
                echo $phpinfo;
            }
            echo "</div>";
        }
        ?>
    </div>
</body>
</html>
