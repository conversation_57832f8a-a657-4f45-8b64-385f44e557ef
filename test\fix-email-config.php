<?php
/**
 * 邮件配置修复脚本
 * 确保邮件配置正确并可用
 */

header('Content-Type: text/html; charset=utf-8');

echo "<h1>🔧 邮件配置修复脚本</h1>";
echo "<p><strong>执行时间:</strong> " . date('Y-m-d H:i:s') . "</p>";

// 修复后的邮件配置
$fixedEmailConfig = [
    // 当前使用的邮件服务商
    'provider' => '163',
    
    // QQ邮箱配置
    'qq' => [
        'smtp_host' => 'smtp.qq.com',
        'smtp_port' => 587,
        'smtp_secure' => 'tls',
        'username' => '',
        'password' => '',
        'from_email' => '',
        'from_name' => '倒计时系统'
    ],
    
    // Gmail配置
    'gmail' => [
        'smtp_host' => 'smtp.gmail.com',
        'smtp_port' => 587,
        'smtp_secure' => 'tls',
        'username' => '',
        'password' => '',
        'from_email' => '',
        'from_name' => '倒计时系统'
    ],

    // Outlook配置
    'outlook' => [
        'smtp_host' => 'smtp-mail.outlook.com',
        'smtp_port' => 587,
        'smtp_secure' => 'tls',
        'username' => '<EMAIL>',
        'password' => 'Xthl147258.',
        'from_email' => '<EMAIL>',
        'from_name' => '倒计时系统'
    ],
    
    // 163邮箱配置
    '163' => [
        'smtp_host' => 'smtp.163.com',
        'smtp_port' => 465,
        'smtp_secure' => 'ssl',
        'username' => '<EMAIL>',
        'password' => 'BJXfKJw32HgDSMSg',
        'from_email' => '<EMAIL>',
        'from_name' => '倒计时系统'
    ],

    // 阿里云邮箱配置
    'aliyun' => [
        'smtp_host' => 'smtp.mxhichina.com',
        'smtp_port' => 25,
        'smtp_secure' => false,
        'username' => '',
        'password' => '',
        'from_email' => '',
        'from_name' => '倒计时系统'
    ],

    // 自定义SMTP配置
    'custom' => [
        'smtp_host' => '',
        'smtp_port' => 587,
        'smtp_secure' => 'tls',
        'username' => '',
        'password' => '',
        'from_email' => '',
        'from_name' => '倒计时系统'
    ]
];

echo "<h2>🔍 步骤1: 检查当前配置</h2>";
try {
    require_once '../server/email-config.php';
    echo "<p style='color: green;'>✅ 当前配置文件加载成功</p>";
    
    if (isset($emailConfig)) {
        echo "<p><strong>当前Provider:</strong> ";
        if (isset($emailConfig['provider'])) {
            echo "'" . htmlspecialchars($emailConfig['provider']) . "'";
        } else {
            echo "<span style='color: red;'>未设置</span>";
        }
        echo "</p>";
    } else {
        echo "<p style='color: red;'>❌ \$emailConfig 变量不存在</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 配置文件加载失败: " . $e->getMessage() . "</p>";
}

echo "<h2>🔧 步骤2: 生成修复后的配置文件</h2>";

$configContent = '<?php
/**
 * 邮件配置文件 (修复版本)
 * 支持多种邮件服务商的SMTP配置
 * 修复时间: ' . date('Y-m-d H:i:s') . '
 */

// 邮件配置数组
$emailConfig = ' . var_export($fixedEmailConfig, true) . ';

/**
 * 获取当前邮件配置
 */
function getEmailConfig() {
    global $emailConfig;
    
    $provider = $emailConfig[\'provider\'];
    if (!isset($emailConfig[$provider])) {
        throw new Exception("邮件服务商配置不存在: $provider");
    }
    
    $config = $emailConfig[$provider];
    
    // 验证必需的配置项
    $required = [\'smtp_host\', \'smtp_port\', \'username\', \'password\', \'from_email\'];
    foreach ($required as $field) {
        if (empty($config[$field])) {
            throw new Exception("邮件配置缺少必需字段: $field");
        }
    }
    
    return $config;
}

/**
 * 验证邮件配置是否完整
 */
function validateEmailConfig() {
    try {
        $config = getEmailConfig();
        return [
            \'valid\' => true,
            \'provider\' => $GLOBALS[\'emailConfig\'][\'provider\'],
            \'smtp_host\' => $config[\'smtp_host\'],
            \'from_email\' => $config[\'from_email\']
        ];
    } catch (Exception $e) {
        return [
            \'valid\' => false,
            \'error\' => $e->getMessage()
        ];
    }
}
?>';

// 备份原文件
$originalFile = '../server/email-config.php';
$backupFile = '../server/email-config.php.backup.' . date('Y-m-d-H-i-s');

if (file_exists($originalFile)) {
    if (copy($originalFile, $backupFile)) {
        echo "<p style='color: green;'>✅ 原配置文件已备份到: " . basename($backupFile) . "</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ 无法备份原配置文件</p>";
    }
}

// 写入修复后的配置
if (file_put_contents($originalFile, $configContent)) {
    echo "<p style='color: green;'>✅ 修复后的配置文件已写入</p>";
} else {
    echo "<p style='color: red;'>❌ 无法写入修复后的配置文件</p>";
    exit;
}

echo "<h2>🧪 步骤3: 测试修复后的配置</h2>";
try {
    // 重新加载配置
    unset($emailConfig);
    require $originalFile;
    
    echo "<p style='color: green;'>✅ 修复后的配置文件加载成功</p>";
    
    $config = getEmailConfig();
    echo "<p style='color: green;'>✅ getEmailConfig() 调用成功</p>";
    
    echo "<p><strong>当前邮件配置:</strong></p>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>字段</th><th>值</th></tr>";
    foreach ($config as $key => $value) {
        echo "<tr>";
        echo "<td><strong>$key</strong></td>";
        if ($key === 'password') {
            echo "<td>" . str_repeat('*', strlen($value)) . "</td>";
        } else {
            echo "<td>" . htmlspecialchars($value) . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 测试失败: " . $e->getMessage() . "</p>";
}

echo "<h2>🧪 步骤4: 测试 EmailSender 类</h2>";
try {
    require_once '../server/email-sender.php';
    $emailSender = new EmailSender();
    echo "<p style='color: green;'>✅ EmailSender 对象创建成功</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ EmailSender 测试失败: " . $e->getMessage() . "</p>";
}

echo "<h2>🎯 步骤5: 测试密码找回API</h2>";
echo "<p>现在可以测试密码找回功能了:</p>";
echo "<ul>";
echo "<li><a href='test-forgot-password-fixed.php' target='_blank'>测试修复后的密码找回功能</a></li>";
echo "<li><a href='debug-email-config.php' target='_blank'>详细的邮件配置调试</a></li>";
echo "<li><a href='../new-lock.html' target='_blank'>返回登录页面测试</a></li>";
echo "</ul>";

echo "<h2>📋 修复总结</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-left: 4px solid #4CAF50;'>";
echo "<h3>✅ 已修复的问题:</h3>";
echo "<ul>";
echo "<li>消除了重复的 aliyun 配置</li>";
echo "<li>确保 provider 字段正确设置为 '163'</li>";
echo "<li>验证了163邮箱的完整配置</li>";
echo "<li>测试了配置加载和验证函数</li>";
echo "</ul>";

echo "<h3>📧 当前邮件配置:</h3>";
echo "<ul>";
echo "<li><strong>服务商:</strong> 163邮箱</li>";
echo "<li><strong>SMTP:</strong> smtp.163.com:465 (SSL)</li>";
echo "<li><strong>发件人:</strong> <EMAIL></li>";
echo "<li><strong>认证:</strong> 授权码认证</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p><small>📅 修复时间: " . date('Y-m-d H:i:s') . "</small></p>";
?>
