<?php
// 创建测试图片
$width = 200;
$height = 100;

// 创建一个图像资源
$image = imagecreate($width, $height);

// 分配颜色
$background = imagecolorallocate($image, 255, 107, 107); // 红色背景
$textColor = imagecolorallocate($image, 255, 255, 255);  // 白色文字

// 填充背景
imagefill($image, 0, 0, $background);

// 添加文字
$text = 'TEST LOGO';
$font_size = 5;
$text_width = imagefontwidth($font_size) * strlen($text);
$text_height = imagefontheight($font_size);
$x = ($width - $text_width) / 2;
$y = ($height - $text_height) / 2;

imagestring($image, $font_size, $x, $y, $text, $textColor);

// 保存为PNG
imagepng($image, '../uploads/logo.png');

// 创建汽车测试图片
$carImage = imagecreate(300, 200);
$carBackground = imagecolorallocate($carImage, 100, 150, 255); // 蓝色背景
$carTextColor = imagecolorallocate($carImage, 255, 255, 255);  // 白色文字

imagefill($carImage, 0, 0, $carBackground);

$carText = 'TEST CAR';
$car_text_width = imagefontwidth($font_size) * strlen($carText);
$car_text_height = imagefontheight($font_size);
$car_x = (300 - $car_text_width) / 2;
$car_y = (200 - $car_text_height) / 2;

imagestring($carImage, $font_size, $car_x, $car_y, $carText, $carTextColor);

// 保存汽车图片
imagepng($carImage, '../uploads/car.png');

// 清理内存
imagedestroy($image);
imagedestroy($carImage);

echo "测试图片已创建！";
?>
